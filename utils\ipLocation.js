/**
 * IP地区分析工具
 * 支持多种IP地区查询方式
 */

// IP地区查询配置
const IP_API_CONFIG = {
	// 主要API - 免费且精准
	primary: {
		url: 'https://ip-api.com/json/',
		fields: 'status,country,regionName,city,district,isp,query',
		lang: 'zh-CN'
	},
	
	// 备用API 1
	backup1: {
		url: 'https://api.ip.sb/geoip/',
		type: 'json'
	},
	
	// 备用API 2  
	backup2: {
		url: 'https://ipapi.co/',
		format: 'json'
	}
}

/**
 * 获取用户IP地址
 */
export function getUserIP() {
	return new Promise((resolve, reject) => {
		// 方法1: 使用ip-api.com获取IP
		uni.request({
			url: 'https://ip-api.com/json/',
			method: 'GET',
			timeout: 5000,
			success: (res) => {
				if (res.data && res.data.status === 'success') {
					resolve(res.data.query)
				} else {
					// 方法2: 使用备用API
					getUserIPBackup().then(resolve).catch(reject)
				}
			},
			fail: (error) => {
				console.error('获取IP失败:', error)
				getUserIPBackup().then(resolve).catch(reject)
			}
		})
	})
}

/**
 * 备用IP获取方法
 */
function getUserIPBackup() {
	return new Promise((resolve, reject) => {
		uni.request({
			url: 'https://api.ip.sb/ip',
			method: 'GET',
			timeout: 5000,
			success: (res) => {
				if (res.data) {
					resolve(res.data.trim())
				} else {
					reject(new Error('无法获取IP'))
				}
			},
			fail: reject
		})
	})
}

/**
 * 根据IP获取精准地区信息
 * @param {string} ip - IP地址，不传则自动获取
 */
export function getLocationByIP(ip = null) {
	return new Promise(async (resolve, reject) => {
		try {
			let targetIP = ip
			
			// 如果没有传入IP，先获取用户IP
			if (!targetIP) {
				try {
					targetIP = await getUserIP()
				} catch (error) {
					console.error('获取IP失败:', error)
					// 如果获取IP失败，返回默认位置
					resolve(getDefaultLocation())
					return
				}
			}
			
			// 使用主要API查询地区
			getLocationFromPrimaryAPI(targetIP)
				.then(resolve)
				.catch(() => {
					// 主要API失败，尝试备用API
					getLocationFromBackupAPI(targetIP)
						.then(resolve)
						.catch(() => {
							// 所有API都失败，返回默认位置
							resolve(getDefaultLocation())
						})
				})
				
		} catch (error) {
			console.error('IP地区查询失败:', error)
			resolve(getDefaultLocation())
		}
	})
}

/**
 * 使用主要API查询地区 (ip-api.com)
 */
function getLocationFromPrimaryAPI(ip) {
	return new Promise((resolve, reject) => {
		const url = `${IP_API_CONFIG.primary.url}${ip}?fields=${IP_API_CONFIG.primary.fields}&lang=${IP_API_CONFIG.primary.lang}`
		
		uni.request({
			url: url,
			method: 'GET',
			timeout: 8000,
			success: (res) => {
				if (res.data && res.data.status === 'success') {
					const location = formatLocationData(res.data, 'primary')
					resolve(location)
				} else {
					reject(new Error('API返回失败'))
				}
			},
			fail: reject
		})
	})
}

/**
 * 使用备用API查询地区
 */
function getLocationFromBackupAPI(ip) {
	return new Promise((resolve, reject) => {
		uni.request({
			url: `${IP_API_CONFIG.backup1.url}${ip}`,
			method: 'GET',
			timeout: 8000,
			success: (res) => {
				if (res.data) {
					const location = formatLocationData(res.data, 'backup')
					resolve(location)
				} else {
					reject(new Error('备用API返回失败'))
				}
			},
			fail: reject
		})
	})
}

/**
 * 格式化地区数据
 */
function formatLocationData(data, apiType) {
	let location = {
		ip: '',
		country: '',
		province: '',
		city: '',
		district: '',
		isp: '',
		fullLocation: '',
		accuracy: 'high' // high, medium, low
	}

	if (apiType === 'primary') {
		// ip-api.com 数据格式
		location.ip = data.query || ''
		location.country = data.country || '中国'
		location.province = data.regionName || ''
		location.city = data.city || ''
		location.district = data.district || ''
		location.isp = data.isp || ''

		// 构建地址 - 只显示省级或直辖市
		if (location.country === '中国' || location.country === 'China') {
			location.fullLocation = formatChineseLocation(location.province, location.city)
		} else {
			location.fullLocation = location.country
		}

		location.accuracy = 'high'

	} else if (apiType === 'backup') {
		// 备用API数据格式
		location.ip = data.ip || ''
		location.country = data.country || '中国'
		location.province = data.region || ''
		location.city = data.city || ''
		location.isp = data.organization || ''

		// 构建地址 - 只显示省级或直辖市
		if (location.country === '中国' || location.country === 'China') {
			location.fullLocation = formatChineseLocation(location.province, location.city)
		} else {
			location.fullLocation = location.country
		}
		location.accuracy = 'medium'
	}

	// 如果地址为空，使用默认
	if (!location.fullLocation.trim()) {
		location = getDefaultLocation()
		location.ip = data.query || data.ip || ''
	}

	return location
}

/**
 * 格式化中国地区显示（只显示省级或直辖市）
 */
function formatChineseLocation(province, city) {
	if (!province && !city) return '上海'

	// 直辖市列表
	const municipalities = ['北京', '上海', '天津', '重庆']

	// 特别行政区
	const specialRegions = ['香港', '澳门']

	// 处理省份信息
	let targetLocation = province || city || ''

	// 去除常见后缀
	targetLocation = targetLocation
		.replace(/市$/, '')
		.replace(/省$/, '')
		.replace(/自治区$/, '')
		.replace(/特别行政区$/, '')
		.replace(/维吾尔$/, '')
		.replace(/回族$/, '')
		.replace(/壮族$/, '')
		.trim()

	// 检查是否是直辖市
	const isDirectMunicipality = municipalities.some(m =>
		targetLocation.includes(m) || (city && city.includes(m))
	)

	// 检查是否是特别行政区
	const isSpecialRegion = specialRegions.some(s =>
		targetLocation.includes(s) || (city && city.includes(s))
	)

	if (isDirectMunicipality) {
		// 直辖市：返回直辖市名称
		const municipality = municipalities.find(m =>
			targetLocation.includes(m) || (city && city.includes(m))
		)
		return municipality || targetLocation
	} else if (isSpecialRegion) {
		// 特别行政区：返回特别行政区名称
		const specialRegion = specialRegions.find(s =>
			targetLocation.includes(s) || (city && city.includes(s))
		)
		return specialRegion || targetLocation
	} else {
		// 普通省份：返回省份名称
		return targetLocation || '上海'
	}
}

/**
 * 获取默认地区信息
 */
function getDefaultLocation() {
	return {
		ip: '',
		country: '中国',
		province: '上海',
		city: '上海',
		district: '',
		isp: '',
		fullLocation: '上海',
		accuracy: 'low'
	}
}

/**
 * 简化地区名称（去除"市"、"省"等后缀）
 */
export function simplifyLocationName(location) {
	if (!location) return '未知'
	
	return location
		.replace(/省$/, '')
		.replace(/市$/, '')
		.replace(/自治区$/, '')
		.replace(/特别行政区$/, '')
		.replace(/维吾尔$/, '')
		.replace(/回族$/, '')
		.replace(/壮族$/, '')
		.trim()
}

/**
 * 缓存IP地区信息
 */
export function cacheLocationInfo(locationData) {
	try {
		const cacheData = {
			...locationData,
			timestamp: Date.now(),
			expireTime: Date.now() + (24 * 60 * 60 * 1000) // 24小时过期
		}
		uni.setStorageSync('ipLocationCache', cacheData)
	} catch (error) {
		console.error('缓存IP地区信息失败:', error)
	}
}

/**
 * 获取缓存的IP地区信息
 */
export function getCachedLocationInfo() {
	try {
		const cacheData = uni.getStorageSync('ipLocationCache')
		if (cacheData && cacheData.expireTime > Date.now()) {
			return cacheData
		}
		return null
	} catch (error) {
		console.error('获取缓存IP地区信息失败:', error)
		return null
	}
}
