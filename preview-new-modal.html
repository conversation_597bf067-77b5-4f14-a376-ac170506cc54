<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新弹窗配色预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            max-width: 800px;
            width: 100%;
        }
        
        .preview-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #FFD700;
        }
        
        .modal-preview {
            width: 300px;
            background: linear-gradient(135deg, #BBB2FF 0%, #E3D2FF 50%, #A7C0FF 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            padding: 30px 20px 20px;
            margin: 0 auto 30px;
            box-shadow: 0 10px 30px rgba(187, 178, 255, 0.4);
        }
        
        .modal-decoration {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        .deco-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .deco-1 {
            width: 60px;
            height: 60px;
            top: -30px;
            right: -30px;
            background: rgba(255, 255, 255, 0.15);
        }
        
        .deco-2 {
            width: 40px;
            height: 40px;
            top: 50px;
            left: -20px;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .deco-3 {
            width: 30px;
            height: 30px;
            bottom: 40px;
            right: 30px;
            background: rgba(255, 255, 255, 0.08);
        }
        
        .modal-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }
        
        .success-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .icon-text {
            font-size: 30px;
            font-weight: bold;
            color: #FFFFFF;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: bold;
            color: #FFFFFF;
            margin-bottom: 10px;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
        }
        
        .sms-content {
            background: rgba(255, 255, 255, 0.15);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .sms-text {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.5;
        }
        
        .verification-code {
            font-size: 18px;
            font-weight: bold;
            color: #FFD700;
            text-shadow: 0 1px 4px rgba(255, 215, 0, 0.5);
            margin: 0 5px;
        }
        
        .modal-phone {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
        }
        
        .modal-btn {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 12px 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .modal-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .comparison {
            display: flex;
            gap: 30px;
            justify-content: center;
            align-items: flex-start;
            flex-wrap: wrap;
        }
        
        .comparison-item {
            text-align: center;
        }
        
        .comparison-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #FFD700;
        }
        
        .old-modal {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }
        
        .color-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
        }
        
        .color-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0;
            gap: 15px;
        }
        
        .color-box {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .color-code {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="preview-section">
            <h1 class="title">🎨 新弹窗配色预览</h1>
            
            <div class="comparison">
                <div class="comparison-item">
                    <div class="comparison-title">❌ 修改前</div>
                    <div class="modal-preview old-modal">
                        <div class="modal-decoration">
                            <div class="deco-circle deco-1"></div>
                            <div class="deco-circle deco-2"></div>
                            <div class="deco-circle deco-3"></div>
                        </div>
                        <div class="modal-content">
                            <div class="success-icon">
                                <div class="icon-text">✓</div>
                            </div>
                            <div class="modal-title">短信验证码</div>
                            <div class="sms-content">
                                <span class="sms-text">【趣嗒同行】尊敬的用户，您的验证码为：</span>
                                <span class="verification-code">9072</span>
                                <span class="sms-text">，有效期1分钟，请勿泄露验证码，谨防诈骗。</span>
                            </div>
                            <div class="modal-phone">发送至: 166****1214</div>
                            <button class="modal-btn">好的</button>
                        </div>
                    </div>
                </div>
                
                <div class="comparison-item">
                    <div class="comparison-title">✅ 修改后</div>
                    <div class="modal-preview">
                        <div class="modal-decoration">
                            <div class="deco-circle deco-1"></div>
                            <div class="deco-circle deco-2"></div>
                            <div class="deco-circle deco-3"></div>
                        </div>
                        <div class="modal-content">
                            <div class="success-icon">
                                <div class="icon-text">✓</div>
                            </div>
                            <div class="modal-title">短信验证码</div>
                            <div class="sms-content">
                                <span class="sms-text">【趣嗒同行】尊敬的用户，您的验证码为：</span>
                                <span class="verification-code">9072</span>
                                <span class="sms-text">，有效期1分钟，请勿泄露验证码，谨防诈骗。</span>
                            </div>
                            <div class="modal-phone">发送至: 166****1214</div>
                            <button class="modal-btn">好的</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="color-info">
                <h3 style="text-align: center; margin-bottom: 20px;">🎨 新配色方案</h3>
                <div class="color-item">
                    <div class="color-box" style="background: #BBB2FF;"></div>
                    <div class="color-code">#BBB2FF</div>
                    <div>起始色</div>
                </div>
                <div class="color-item">
                    <div class="color-box" style="background: #E3D2FF;"></div>
                    <div class="color-code">#E3D2FF</div>
                    <div>中间色</div>
                </div>
                <div class="color-item">
                    <div class="color-box" style="background: #A7C0FF;"></div>
                    <div class="color-code">#A7C0FF</div>
                    <div>结束色</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
