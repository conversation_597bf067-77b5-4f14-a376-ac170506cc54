<template>
	<view class="followers-page">
		<!-- 状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left" @click="goBack">
				<view class="back-btn">
					<uni-icons type="left" size="18" color="#fff"></uni-icons>
				</view>
			</view>
			<view class="nav-center">
				<text class="nav-title">我的粉丝</text>
				<text class="nav-subtitle">{{ totalFollowers }}人关注了我</text>
			</view>
			<view class="nav-right">
				<view class="search-btn" @click="showSearch">
					<uni-icons type="search" size="18" color="#fff"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 搜索框 -->
		<view class="search-section" v-if="showSearchBox">
			<view class="search-container">
				<view class="search-input-wrapper">
					<uni-icons type="search" size="16" color="#999"></uni-icons>
					<input 
						class="search-input" 
						placeholder="搜索粉丝昵称" 
						v-model="searchKeyword"
						@input="onSearchInput"
					/>
					<view class="clear-btn" v-if="searchKeyword" @click="clearSearch">
						<uni-icons type="clear" size="14" color="#ccc"></uni-icons>
					</view>
				</view>
				<text class="cancel-btn" @click="hideSearch">取消</text>
			</view>
		</view>

		<!-- 筛选标签 -->
		<view class="filter-tabs">
			<scroll-view scroll-x class="tabs-scroll" :show-scrollbar="false">
				<view class="tabs-container">
					<view 
						class="tab-item" 
						:class="{ active: activeTab === index }"
						v-for="(tab, index) in filterTabs" 
						:key="index"
						@click="switchTab(index)"
					>
						<text class="tab-text">{{ tab.name }}</text>
						<text class="tab-count" v-if="tab.count">{{ tab.count }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 自定义下拉刷新 -->
		<view class="custom-refresh" v-if="refreshing">
			<view class="refresh-container">
				<view class="stars-animation">
					<view class="star-item star-1">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-2">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-3">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-4">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-5">
						<text class="star-text">✦</text>
					</view>
				</view>
				<text class="refresh-text">正在刷新...</text>
			</view>
		</view>

		<!-- 粉丝列表 -->
		<scroll-view
			class="followers-list"
			scroll-y
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd"
			@scrolltolower="loadMore"
		>
			<view class="list-container">
				<view 
					class="follower-card" 
					v-for="follower in filteredFollowers" 
					:key="follower.id"
					@click="viewProfile(follower)"
				>
					<view class="card-content">
						<view class="avatar-section">
							<image :src="follower.avatar" class="user-avatar"></image>
							<view v-if="follower.isOnline" class="online-dot"></view>
							<view class="level-badge" v-if="follower.level">
								<text class="level-text">Lv{{ follower.level }}</text>
							</view>
						</view>
						
						<view class="info-section">
							<view class="user-info">
								<text class="user-name">{{ follower.name }}</text>
								<view class="user-tags">
									<text class="tag vip" v-if="follower.isVip">VIP</text>
									<text class="tag verified" v-if="follower.isVerified">认证</text>
									<text class="tag new" v-if="follower.isNewFollower">新粉丝</text>
								</view>
							</view>
							<text class="user-desc">{{ follower.description }}</text>
							<view class="follow-info">
								<text class="follow-time">{{ follower.followTime }}</text>
								<view class="mutual-info" v-if="follower.isMutual">
									<uni-icons type="heart-filled" size="12" color="#FF6B6B"></uni-icons>
									<text class="mutual-text">互相关注</text>
								</view>
							</view>
						</view>
						
						<view class="action-section">
							<view 
								class="follow-btn" 
								:class="{ following: follower.isFollowingBack }"
								@click.stop="toggleFollow(follower)"
							>
								<text class="btn-text">{{ follower.isFollowingBack ? '已关注' : '回关' }}</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 加载更多 -->
				<view class="load-more" v-if="hasMore">
					<uni-icons type="spinner-cycle" size="20" color="#999"></uni-icons>
					<text class="load-text">加载更多...</text>
				</view>
				
				<!-- 空状态 -->
				<view class="empty-state" v-if="filteredFollowers.length === 0 && !loading">
					<view class="empty-icon">
						<uni-icons type="person" size="60" color="#ddd"></uni-icons>
					</view>
					<text class="empty-title">暂无粉丝</text>
					<text class="empty-desc">快去发布精彩内容吸引更多粉丝吧~</text>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				showSearchBox: false,
				searchKeyword: '',
				activeTab: 0,
				refreshing: false,
				loading: false,
				hasMore: true,
				totalFollowers: 256,
				touchStartY: 0,
				scrollTop: 0,
				
				filterTabs: [
					{ name: '全部', count: 256 },
					{ name: '互相关注', count: 89 },
					{ name: '新粉丝', count: 23 },
					{ name: 'VIP粉丝', count: 45 }
				],
				
				followers: [
					{
						id: 1,
						name: '小雨',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						description: '热爱生活的摄影师 📸',
						followTime: '3天前关注',
						isOnline: true,
						isVip: true,
						isVerified: true,
						isNewFollower: false,
						isMutual: true,
						isFollowingBack: true,
						level: 15
					},
					{
						id: 2,
						name: '阿杰',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						description: '游戏达人，王者荣耀主播',
						followTime: '1周前关注',
						isOnline: false,
						isVip: false,
						isVerified: false,
						isNewFollower: true,
						isMutual: false,
						isFollowingBack: false,
						level: 8
					}
				]
			}
		},
		
		computed: {
			filteredFollowers() {
				let result = this.followers;
				
				// 根据搜索关键词筛选
				if (this.searchKeyword) {
					result = result.filter(follower => 
						follower.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
					);
				}
				
				// 根据标签筛选
				switch (this.activeTab) {
					case 1: // 互相关注
						result = result.filter(follower => follower.isMutual);
						break;
					case 2: // 新粉丝
						result = result.filter(follower => follower.isNewFollower);
						break;
					case 3: // VIP粉丝
						result = result.filter(follower => follower.isVip);
						break;
				}
				
				return result;
			}
		},
		
		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},
		
		methods: {
			goBack() {
				uni.navigateBack();
			},
			
			showSearch() {
				this.showSearchBox = true;
			},
			
			hideSearch() {
				this.showSearchBox = false;
				this.searchKeyword = '';
			},
			
			clearSearch() {
				this.searchKeyword = '';
			},
			
			onSearchInput() {
				// 实时搜索逻辑
			},
			
			switchTab(index) {
				this.activeTab = index;
			},

			// 触摸开始
			onTouchStart(e) {
				this.touchStartY = e.touches[0].clientY;
				this.scrollTop = 0;
			},

			// 触摸移动
			onTouchMove(e) {
				const currentY = e.touches[0].clientY;
				const deltaY = currentY - this.touchStartY;

				// 只有在页面顶部且向下拉时才触发刷新
				if (this.scrollTop <= 0 && deltaY > 100 && !this.refreshing) {
					this.triggerRefresh();
				}
			},

			// 触摸结束
			onTouchEnd() {
				this.touchStartY = 0;
			},

			// 触发刷新
			triggerRefresh() {
				this.refreshing = true;
				setTimeout(() => {
					this.refreshing = false;
					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					});
				}, 2000);
			},
			
			loadMore() {
				if (this.hasMore && !this.loading) {
					this.loading = true;
					// 加载更多数据
					setTimeout(() => {
						this.loading = false;
					}, 1000);
				}
			},
			
			viewProfile(follower) {
				console.log('查看用户资料:', follower.name);
			},
			
			toggleFollow(follower) {
				follower.isFollowingBack = !follower.isFollowingBack;
				if (follower.isFollowingBack) {
					follower.isMutual = true;
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.followers-page {
		background: #f5f6fa;
		min-height: 100vh;
	}
	
	.status-bar {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}
	
	// 顶部导航
	.top-nav {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16rpx 24rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		
		.nav-left, .nav-right {
			.back-btn, .search-btn {
				width: 40rpx;
				height: 40rpx;
				border-radius: 20rpx;
				background: rgba(255, 255, 255, 0.2);
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				
				&:active {
					background: rgba(255, 255, 255, 0.3);
					transform: scale(0.95);
				}
			}
		}
		
		.nav-center {
			flex: 1;
			text-align: center;
			
			.nav-title {
				font-size: 34rpx;
				font-weight: 700;
				color: #fff;
				display: block;
				margin-bottom: 4rpx;
			}
			
			.nav-subtitle {
				font-size: 22rpx;
				color: rgba(255, 255, 255, 0.8);
			}
		}
	}

	// 自定义下拉刷新
	.custom-refresh {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10rpx);

		.refresh-container {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 40rpx 20rpx 20rpx;

			.stars-animation {
				position: relative;
				width: 80rpx;
				height: 80rpx;
				margin-bottom: 16rpx;

				.star-item {
					position: absolute;
					display: flex;
					align-items: center;
					justify-content: center;

					.star-text {
						font-size: 20rpx;
						color: #667eea;
						font-weight: bold;
					}

					&.star-1 {
						top: 0;
						left: 50%;
						transform: translateX(-50%);
						animation: starPulse 1.5s ease-in-out infinite;
					}

					&.star-2 {
						top: 20rpx;
						right: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 0.3s;
					}

					&.star-3 {
						bottom: 20rpx;
						right: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 0.6s;
					}

					&.star-4 {
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						animation: starPulse 1.5s ease-in-out infinite 0.9s;
					}

					&.star-5 {
						top: 20rpx;
						left: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 1.2s;
					}
				}
			}

			.refresh-text {
				font-size: 24rpx;
				color: #666;
				font-weight: 500;
			}
		}
	}

	// 搜索框
	.search-section {
		padding: 16rpx 24rpx;
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;

		.search-container {
			display: flex;
			align-items: center;
			gap: 16rpx;

			.search-input-wrapper {
				flex: 1;
				display: flex;
				align-items: center;
				background: #f8f9fa;
				border-radius: 24rpx;
				padding: 12rpx 20rpx;
				gap: 12rpx;

				.search-input {
					flex: 1;
					font-size: 28rpx;
					color: #333;

					&::placeholder {
						color: #999;
					}
				}

				.clear-btn {
					width: 28rpx;
					height: 28rpx;
					border-radius: 14rpx;
					background: #ddd;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.cancel-btn {
				font-size: 28rpx;
				color: #667eea;
				font-weight: 500;
			}
		}
	}

	// 筛选标签
	.filter-tabs {
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;

		.tabs-scroll {
			white-space: nowrap;

			.tabs-container {
				display: flex;
				padding: 0 24rpx;

				.tab-item {
					display: flex;
					align-items: center;
					gap: 8rpx;
					padding: 20rpx 24rpx;
					margin-right: 16rpx;
					border-radius: 24rpx;
					transition: all 0.3s ease;

					&.active {
						background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

						.tab-text {
							color: #fff;
						}

						.tab-count {
							background: rgba(255, 255, 255, 0.2);
							color: #fff;
						}
					}

					.tab-text {
						font-size: 26rpx;
						color: #666;
						font-weight: 500;
					}

					.tab-count {
						background: #f0f0f0;
						color: #999;
						font-size: 20rpx;
						padding: 4rpx 8rpx;
						border-radius: 10rpx;
						min-width: 32rpx;
						text-align: center;
					}
				}
			}
		}
	}

	// 粉丝列表
	.followers-list {
		flex: 1;

		.list-container {
			padding: 16rpx 24rpx;

			.follower-card {
				background: #fff;
				border-radius: 20rpx;
				margin-bottom: 16rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
				}

				.card-content {
					display: flex;
					align-items: center;
					padding: 24rpx;
					gap: 20rpx;

					.avatar-section {
						position: relative;
						flex-shrink: 0;

						.user-avatar {
							width: 88rpx;
							height: 88rpx;
							border-radius: 44rpx;
							border: 3rpx solid #f0f0f0;
						}

						.online-dot {
							position: absolute;
							bottom: 4rpx;
							right: 4rpx;
							width: 20rpx;
							height: 20rpx;
							background: #2ed573;
							border-radius: 50%;
							border: 3rpx solid #fff;
						}

						.level-badge {
							position: absolute;
							top: -8rpx;
							right: -8rpx;
							background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
							border-radius: 12rpx;
							padding: 4rpx 8rpx;

							.level-text {
								font-size: 18rpx;
								color: #333;
								font-weight: 600;
							}
						}
					}

					.info-section {
						flex: 1;
						min-width: 0;

						.user-info {
							display: flex;
							align-items: center;
							gap: 12rpx;
							margin-bottom: 8rpx;

							.user-name {
								font-size: 30rpx;
								font-weight: 600;
								color: #333;
							}

							.user-tags {
								display: flex;
								gap: 8rpx;

								.tag {
									font-size: 18rpx;
									padding: 4rpx 8rpx;
									border-radius: 8rpx;
									font-weight: 500;

									&.vip {
										background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
										color: #333;
									}

									&.verified {
										background: #4ECDC4;
										color: #fff;
									}

									&.new {
										background: #FF6B6B;
										color: #fff;
									}
								}
							}
						}

						.user-desc {
							font-size: 24rpx;
							color: #666;
							margin-bottom: 12rpx;
							line-height: 1.4;
						}

						.follow-info {
							display: flex;
							align-items: center;
							justify-content: space-between;

							.follow-time {
								font-size: 22rpx;
								color: #999;
							}

							.mutual-info {
								display: flex;
								align-items: center;
								gap: 6rpx;

								.mutual-text {
									font-size: 22rpx;
									color: #FF6B6B;
									font-weight: 500;
								}
							}
						}
					}

					.action-section {
						flex-shrink: 0;

						.follow-btn {
							padding: 12rpx 24rpx;
							border-radius: 20rpx;
							border: 2rpx solid #667eea;
							background: #fff;
							transition: all 0.3s ease;

							&.following {
								background: #667eea;

								.btn-text {
									color: #fff;
								}
							}

							&:active {
								transform: scale(0.95);
							}

							.btn-text {
								font-size: 24rpx;
								color: #667eea;
								font-weight: 500;
							}
						}
					}
				}
			}

			// 加载更多
			.load-more {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 12rpx;
				padding: 32rpx;

				.load-text {
					font-size: 26rpx;
					color: #999;
				}
			}

			// 空状态
			.empty-state {
				text-align: center;
				padding: 120rpx 40rpx;

				.empty-icon {
					margin-bottom: 24rpx;
				}

				.empty-title {
					font-size: 32rpx;
					color: #666;
					font-weight: 600;
					display: block;
					margin-bottom: 12rpx;
				}

				.empty-desc {
					font-size: 26rpx;
					color: #999;
					line-height: 1.5;
				}
			}
		}
	}

	// 星星脉冲动画
	@keyframes starPulse {
		0%, 100% {
			transform: scale(0.8);
			opacity: 0.5;
		}
		50% {
			transform: scale(1.2);
			opacity: 1;
		}
	}
</style>
