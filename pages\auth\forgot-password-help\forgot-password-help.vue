<template>
	<view class="container">
		<!-- 背景图片 -->
		<image src="/static/bangzhuzhongxin.png" class="background-image" mode="aspectFill"></image>

		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="back-button" @click="goBack">
				<image src="/static/fanhuilogo.png" class="back-icon" mode="aspectFit"></image>
			</view>
		</view>

		<!-- 主要内容 -->
		<view class="main-content">


			<!-- 方法列表 -->
			<view class="methods-list">
				<!-- 方法1: 手机验证码重置 -->
				<view class="method-card" @click="goToResetPassword">
					<view class="method-icon">
						<image src="/static/icons/shoujihao.png" class="method-image" mode="aspectFit"></image>
					</view>
					<view class="method-content">
						<text class="method-title">手机验证码重置</text>
						<text class="method-desc">通过注册手机号接收验证码，快速重置密码</text>
						<view class="method-steps">
							<text class="step-item">1. 输入注册手机号</text>
							<text class="step-item">2. 接收验证码</text>
							<text class="step-item">3. 设置新密码</text>
						</view>
					</view>
					<view class="method-arrow">
						<text class="arrow-text">→</text>
					</view>
				</view>

				<!-- 方法2: 邮箱重置 -->
				<view class="method-card disabled">
					<view class="method-icon">
						<image src="/static/icons/youxiang.png" class="method-image" mode="aspectFit"></image>
					</view>
					<view class="method-content">
						<text class="method-title">邮箱重置</text>
						<text class="method-desc">通过绑定邮箱重置密码（暂未开放）</text>
						<view class="coming-soon">
							<text class="coming-text">即将上线</text>
						</view>
					</view>
				</view>

				<!-- 方法3: 在线客服 -->
				<view class="method-card" @click="contactOnlineSupport">
					<view class="method-icon">
						<image src="/static/icons/kefu.png" class="method-image" mode="aspectFit"></image>
					</view>
					<view class="method-content">
						<text class="method-title">在线客服</text>
						<text class="method-desc">如果以上方式都无法使用，请联系在线客服协助</text>
						<view class="contact-info">
							<text class="contact-item online-service" @click="contactOnlineSupport">在线客服</text>
							<text class="contact-item">服务时间：7×24小时</text>
						</view>
					</view>
					<view class="method-arrow">
						<text class="arrow-text">→</text>
					</view>
				</view>
			</view>

			<!-- 安全提示 -->
			<view class="security-tips">
				<view class="tips-header">
					<image src="/static/icons/anquan.png" class="tips-icon" mode="aspectFit"></image>
					<text class="tips-title">安全提示</text>
				</view>
				<view class="tips-content">
					<text class="tip-item">• 请妥善保管您的密码，建议定期更换</text>
					<text class="tip-item">• 密码应包含字母、数字，长度6-20位</text>
					<text class="tip-item">• 不要在公共场所输入密码</text>
					<text class="tip-item">• 如发现账户异常，请立即联系客服</text>
					<text class="tip-item">• 请勿将账号借给他人使用，保护个人信息安全</text>
					<text class="tip-item">• 发现可疑登录或异常操作请及时修改密码</text>
				</view>
			</view>

			<!-- 账号保护提示 -->
			<view class="security-tips">
				<view class="tips-header">
					<image src="/static/icons/anquan.png" class="tips-icon" mode="aspectFit"></image>
					<text class="tips-title">账号保护</text>
				</view>
				<view class="tips-content">
					<text class="tip-item">• 启用双重验证，提高账号安全性</text>
					<text class="tip-item">• 绑定安全邮箱和手机号码</text>
					<text class="tip-item">• 定期检查登录记录和设备管理</text>
					<text class="tip-item">• 避免使用公共WiFi进行敏感操作</text>
					<text class="tip-item">• 及时更新应用版本，修复安全漏洞</text>
				</view>
			</view>

			<!-- 违规处理说明 -->
			<view class="security-tips warning">
				<view class="tips-header">
					<image src="/static/icons/anquan.png" class="tips-icon warning-icon" mode="aspectFit"></image>
					<text class="tips-title">违规处理说明</text>
				</view>
				<view class="tips-content">
					<text class="tip-item warning-text">• 发布违法违规内容将面临封号处理</text>
					<text class="tip-item warning-text">• 恶意刷屏、骚扰他人将被限制功能</text>
					<text class="tip-item warning-text">• 传播虚假信息将承担相应法律责任</text>
					<text class="tip-item warning-text">• 盗用他人账号将被永久封禁</text>
					<text class="tip-item">• 如对处理结果有异议，可通过客服申诉</text>
					<text class="tip-item">• 详细规则请查看《用户协议》和《社区规范》</text>
				</view>
			</view>

			<!-- 快速操作按钮 -->
			<view class="quick-actions">
				<view class="action-btn primary" @click="goToResetPassword">
					<text class="btn-text">开始重置密码</text>
				</view>
				<view class="action-btn secondary" @click="goToLogin">
					<text class="btn-text">返回登录</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			
		}
	},
	
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		// 跳转到密码重置页面
		goToResetPassword() {
			uni.navigateTo({
				url: '/pages/auth/forgot-password/forgot-password'
			})
		},
		
		// 联系在线客服
		contactOnlineSupport() {
			uni.showModal({
				title: '在线客服',
				content: '即将为您转接在线客服\n服务时间：7×24小时\n\n是否立即联系在线客服？',
				confirmText: '联系',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						// 这里可以跳转到客服聊天页面或打开客服系统
						uni.showToast({
							title: '正在连接客服...',
							icon: 'loading',
							duration: 2000
						})

						// 模拟跳转到客服页面
						setTimeout(() => {
							uni.showToast({
								title: '客服功能开发中',
								icon: 'none'
							})
						}, 2000)
					}
				}
			})
		},
		
		// 返回登录页面
		goToLogin() {
			uni.navigateBack()
		}
	}
}
</script>

<style scoped>
@import '@/styles/global.scss';

/* 强制消除所有可能的边距和留白 */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

page, body, html, uni-page-body, uni-page, uni-page-wrapper {
	margin: 0 !important;
	padding: 0 !important;
	width: 100% !important;
	height: 100% !important;
	overflow: hidden !important;
	border: none !important;
	outline: none !important;
}

.container {
	position: fixed;
	top: -10px;
	left: -10px;
	right: -10px;
	bottom: -10px;
	width: calc(100vw + 20px);
	height: calc(100vh + 20px);
	overflow: hidden;
	margin: 0;
	padding: 10px;
}

.background-image {
	width: calc(100% + 20px);
	height: calc(100% + 20px);
	position: fixed;
	top: -10px;
	left: -10px;
	z-index: 1;
}

.nav-bar {
	position: fixed;
	top: var(--status-bar-height);
	left: 0;
	right: 0;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 var(--spacing-lg);
	z-index: 100;
	background: transparent;
}

.back-button {
	position: absolute;
	left: var(--spacing-lg);
	width: 50rpx;
	height: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.3);
	border-radius: 50%;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
}

.back-button:active {
	transform: scale(0.95);
	background: rgba(0, 0, 0, 0.5);
}

.back-icon {
	width: 28rpx;
	height: 28rpx;
	filter: brightness(0) invert(1);
}



.main-content {
	position: fixed;
	top: calc(var(--status-bar-height) + 88rpx);
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 10;
	overflow-y: auto;
	padding: 600rpx 30rpx 60rpx;
	box-sizing: border-box;
}



.methods-list {
	margin-bottom: 40rpx;
	margin-top: 0;
}

.method-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: flex-start;
	backdrop-filter: blur(20rpx);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.method-card:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.method-card.disabled {
	opacity: 0.6;
	pointer-events: none;
}

.method-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(102, 212, 200, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.method-image {
	width: 40rpx;
	height: 40rpx;
}

.method-text-icon {
	font-size: 40rpx;
	color: #66D4C8;
	font-weight: bold;
}



.method-content {
	flex: 1;
}

.method-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 10rpx;
}

.method-desc {
	display: block;
	font-size: 26rpx;
	color: #666666;
	margin-bottom: 15rpx;
	line-height: 1.4;
}

.method-steps {
	display: flex;
	flex-direction: column;
	gap: 5rpx;
}

.step-item {
	font-size: 24rpx;
	color: #999999;
}

.coming-soon {
	background: rgba(255, 193, 7, 0.2);
	border-radius: 10rpx;
	padding: 8rpx 16rpx;
	display: inline-block;
}

.coming-text {
	font-size: 22rpx;
	color: #FF9800;
	font-weight: 500;
}

.contact-info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.contact-item {
	font-size: 24rpx;
	color: #66D4C8;
	font-weight: 500;
}

.online-service {
	color: #66D4C8;
	font-weight: bold;
	text-decoration: underline;
	cursor: pointer;
	transition: all 0.3s ease;
}

.online-service:active {
	color: #4ECDC4;
	transform: scale(0.98);
}

.method-arrow {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.arrow-text {
	font-size: 28rpx;
	color: #66D4C8;
	font-weight: bold;
}

.security-tips {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 40rpx;
	backdrop-filter: blur(20rpx);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.tips-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.tips-text-icon {
	font-size: 32rpx;
	margin-right: 12rpx;
	color: #66D4C8;
	font-weight: bold;
}

.tips-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
}

.warning-icon {
	filter: hue-rotate(0deg) saturate(2) brightness(0.8);
}

.tips-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333333;
}

.tips-content {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.tip-item {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.4;
}

.security-tips.warning {
	border-left: 6rpx solid #FF6B6B;
}

.security-tips.warning .tips-text-icon {
	color: #FF6B6B;
}

.warning-text {
	color: #FF6B6B !important;
	font-weight: 500;
}

.quick-actions {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.action-btn.primary {
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 100%);
	box-shadow: 0 8rpx 32rpx rgba(102, 212, 200, 0.4);
}

.action-btn.secondary {
	background: rgba(255, 255, 255, 0.9);
	border: 2rpx solid #66D4C8;
	backdrop-filter: blur(20rpx);
}

.action-btn:active {
	transform: scale(0.98);
}

.action-btn.primary .btn-text {
	color: #FFFFFF;
	font-size: 30rpx;
	font-weight: bold;
}

.action-btn.secondary .btn-text {
	color: #66D4C8;
	font-size: 30rpx;
	font-weight: bold;
}
</style>
