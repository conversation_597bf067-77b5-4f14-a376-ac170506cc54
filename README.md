# 趣玩星球 - Vue + uniCloud 重构版

## 项目概述
基于Vue 3 + uniCloud的全栈社交平台，APP客户端。

## 技术栈
- **前端**: Vue 3 + Vite + uniapp
- **后端**: uniCloud云函数 + 云数据库
- **实时通信**: uniCloud实时数据推送
- **UI框架**: uni-ui + 自定义组件
- **状态管理**: Pinia
- **路由**: uni-router

## 项目结构
```
quwanplanet-vue/
├── src/
│   ├── pages/           # 页面文件
│   │   ├── index/       # 首页
│   │   ├── login/       # 登录注册
│   │   ├── profile/     # 个人中心
│   │   ├── customer-service/ # 客服系统
│   │   └── admin/       # 管理后台
│   ├── components/      # 组件
│   ├── store/          # 状态管理
│   ├── utils/          # 工具函数
│   └── static/         # 静态资源
├── uniCloud-aliyun/    # 云函数
│   ├── cloudfunctions/ # 云函数
│   └── database/       # 数据库初始化
├── manifest.json       # 应用配置
├── pages.json         # 页面配置
└── uni.scss          # 全局样式
```

## 核心功能模块

### 1. 首页系统 ✅
- **智能定位**: 集成高德地图API，自动获取用户城市位置
- **搜索功能**: 支持搜索附近的人或地点
- **功能导航**: 四大核心功能入口（附近的人、活动广场、动态分享、客服中心）
- **推荐内容**: 基于位置的个性化推荐
- **底部导航**: 五个主要页面的快速切换
- **实时消息**: 消息数量提醒和快速入口
- **城市切换**: 支持手动选择城市
- **下拉刷新**: 支持下拉刷新和上拉加载更多

### 2. 用户系统
- 手机号一键注册登录
- 验证码登录（6位数字输入框）
- 完善个人信息（头像、昵称、地区等）
- 3天免登录
- 严格的昵称验证规则

### 3. 客服系统
- 实时聊天功能
- AI机器人自动回复
- 人工客服接入
- 表情包和图片发送
- 服务评价系统

### 4. 管理后台
- 员工ID登录系统
- 验证码锁定机制
- 权限分级管理
- 用户数据管理
- 客服工作台

### 5. 实时通信
- uniCloud实时数据推送
- 消息即时送达
- 在线状态显示
- 消息已读状态

## 第三方服务配置

### 图片存储 (Cloudinary)
- **Cloud name**: dwcauq0wy
- **API KEY**: 965165511998959
- **API Secret**: JYnkxTIAAC3GLuf3u6iiQpfqfMA
- **预设名称**: chat_app_preset
- **使用说明**: 上传图片后，Cloudinary会返回URL，需将此URL保存到数据库中（如用户头像等）

### 定位系统 (高德地图API)
- **Web端Key**: e318a539d606974c5f13654e905cf555
- **安全密钥**: b2d738d21b1aed6f1ec0d61a9c935a7b
- **定位要求**: 定位到"市"即可，无需定位到省或者区

### Android签名证书
- **证书文件**: quwanplanet.keystore
- **证书别名**: quwanplanet
- **密钥算法**: RSA 2048位
- **有效期**: 10,000天（约27年）
- **证书位置**: 项目根目录
- **生成时间**: 2024年
- **用途**: Android APK签名打包
- **注意**: 密钥库密码和证书密码请妥善保管，丢失将无法更新应用

## 设计规范

### 主题色彩
- **主题色**: #6F7BF5
- **辅助色**:
  - #FF6B6B（珊瑚红）
  - #4D5DFB（靛蓝色）
  - #FFD166（明亮黄）
  - #06D6A0（薄荷绿）
- **文字颜色**:
  - 主要文字: #333333
  - 次要文字: #666666
  - 提示文字: #999999
- **背景色**: #F8F9FA
- **字体**: 无线寸字体类型

### 页面要求
- 所有页面为手机H5网页端
- 网页需适配各种屏幕尺寸的手机
- 页面只能上下滑动，不能左右滑动，不能放大或缩小页面
- 弹窗使用toast

### 组件规范
- 四角星加载动画
- 纯CSS图标优先
- 白色状态栏
- 仅支持垂直滚动
- 统一的弹窗样式

## 数据库设计

### 用户表 (users)
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  phone VARCHAR(11) UNIQUE NOT NULL,
  nickname VARCHAR(20),
  avatar TEXT,
  gender TINYINT,
  birthday DATE,
  region VARCHAR(100),
  email VARCHAR(100),
  password VARCHAR(255),
  bio TEXT,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 客服会话表 (customer_service_sessions)
```sql
CREATE TABLE customer_service_sessions (
  id VARCHAR(50) PRIMARY KEY,
  user_id INT NOT NULL,
  customer_service_id INT,
  status ENUM('waiting', 'active', 'closed') DEFAULT 'waiting',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 消息表 (messages)
```sql
CREATE TABLE messages (
  id INT PRIMARY KEY AUTO_INCREMENT,
  session_id VARCHAR(50) NOT NULL,
  sender_id INT,
  sender_type ENUM('user', 'customer_service', 'bot') NOT NULL,
  content TEXT NOT NULL,
  message_type ENUM('text', 'image', 'emoji') DEFAULT 'text',
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES customer_service_sessions(id)
);
```

### 管理员表 (admins)
```sql
CREATE TABLE admins (
  id INT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(20) UNIQUE NOT NULL,
  name VARCHAR(50) NOT NULL,
  department VARCHAR(50),
  role ENUM('super_admin', 'admin', 'customer_service') NOT NULL,
  permissions JSON,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 部署配置

### uniCloud配置
- **服务商**: 阿里云
- **地域**: 上海
- **数据库**: MongoDB
- **存储**: 云存储
- **API接口**: uniCloud自动生成

#### 云函数列表
- `user-center`: 用户中心（登录、注册、用户信息管理）
- `customer-service`: 客服系统（会话管理、消息处理）

#### 数据库集合
- `uni-id-users`: 用户表
- `customer_service_sessions`: 客服会话表
- `messages`: 消息表
- `admins`: 管理员表

#### 配置文件
- `uni-id/config.json`: 用户身份认证配置
- `db_init.json`: 数据库初始化数据
- `*.schema.json`: 数据库表结构定义

## 开发计划

### 第一阶段 - 基础框架 (1-2天)
- [x] 项目初始化
- [x] Android签名证书生成
- [x] uniCloud云开发环境配置
- [x] 首页开发完成
- [x] 高德地图API集成
- [x] 基础路由配置
- [ ] 基础组件开发
- [ ] 状态管理设置

### 第二阶段 - 用户系统 (2-3天)
- [ ] 登录注册页面
- [ ] 验证码功能
- [ ] 个人信息完善
- [ ] 用户状态管理

### 第三阶段 - 客服系统 (3-4天)
- [ ] 聊天界面
- [ ] 实时消息推送
- [ ] AI机器人
- [ ] 文件上传功能

### 第四阶段 - 管理后台 (2-3天)
- [ ] 管理员登录
- [ ] 用户管理
- [ ] 客服工作台
- [ ] 权限控制

### 第五阶段 - 优化部署 (1-2天)
- [ ] 性能优化
- [ ] 安全加固
- [ ] 域名部署
- [ ] 测试验收

## 注意事项
1. 所有敏感信息使用环境变量
2. 严格的输入验证和XSS防护
3. 实时功能使用uniCloud推送，确保稳定性
4. 移动端适配优先，响应式设计
5. 代码注释完整，便于维护
6. **Android签名证书安全**：
   - 证书密码务必备份到安全位置
   - 证书文件不要提交到版本控制系统
   - 定期备份证书文件到云端
   - 证书丢失将导致无法更新已发布的应用

## 开发环境
- Node.js >= 16.0.0
- HBuilderX 或 VS Code
- uniCloud开发者工具

## 快速开始
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev:h5

# 构建生产版本
npm run build:h5
```

## 屏幕适配与UI规范

### 1. 屏幕适配系统

#### 适配工具类 (`utils/screen-adapter.js`)
- **多尺寸适配**：支持小屏(375px以下)、中屏(375-414px)、大屏(414px以上)设备
- **状态栏适配**：自动获取不同设备的状态栏高度
- **安全区域适配**：支持iPhone X系列等带刘海屏设备的安全区域
- **响应式计算**：提供字体、间距、尺寸的响应式计算方法

#### 全局样式系统 (`styles/global.scss`)
```scss
:root {
  /* 状态栏高度（JS动态设置） */
  --status-bar-height: 44px;
  --navigation-bar-height: 88px;
  --bottom-safe-height: 0px;

  /* 响应式间距 */
  --spacing-xs: 8rpx;   --spacing-sm: 16rpx;
  --spacing-md: 24rpx;  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;  --spacing-xxl: 64rpx;

  /* 响应式字体 */
  --font-size-xs: 24rpx; --font-size-sm: 28rpx;
  --font-size-md: 32rpx; --font-size-lg: 36rpx;
  --font-size-xl: 40rpx; --font-size-xxl: 48rpx;
}
```

### 2. 状态栏适配规范

#### 背景图片页面（登录页面）
- 背景图片延伸到状态栏，无缝衔接
- 状态栏背景透明，与背景图片融合
- 返回按钮使用半透明深色背景，白色图标

#### 纯色背景页面（法律文档）
- 状态栏背景色与页面背景一致
- 返回按钮使用浅色背景，深色图标
- 确保内容不被状态栏遮挡

### 3. 统一返回按钮规范

#### 样式规范
```scss
.back-button {
  width: 80rpx; height: 80rpx;
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

/* 深色背景 */
.back-button {
  background: rgba(0, 0, 0, 0.5);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

/* 浅色背景 */
.back-button-light {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
}
```

#### 位置规范
- 距离左边缘：32rpx
- 垂直居中：在导航栏中垂直居中
- 层级：z-index: 100

### 4. 手势返回功能

#### 手势返回混入 (`mixins/gesture-back.js`)
```javascript
import gestureBackMixin from '@/mixins/gesture-back.js'

export default {
  mixins: [gestureBackMixin],
  methods: {
    // 可重写返回逻辑
    goBack() {
      // 自定义返回逻辑
    }
  }
}
```

#### 手势识别参数
- **触发区域**：屏幕左边缘 100rpx 内
- **最小滑动距离**：60rpx 开始显示指示器
- **触发距离**：200rpx 触发返回
- **垂直容差**：200rpx 内的垂直偏移

### 5. 布局适配规范

#### 响应式断点
- **小屏设备** (≤375px)：减小间距和字体
- **中屏设备** (375-414px)：标准尺寸
- **大屏设备** (≥414px)：增大间距和字体
- **平板设备** (≥768px)：进一步优化布局

#### 容器适配
```scss
.page-container {
  min-height: 100vh;
  padding-top: var(--status-bar-height);
  padding-bottom: var(--bottom-safe-height);
}
```

### 6. 测试设备清单
- [ ] iPhone SE (375x667)
- [ ] iPhone 6/7/8 (375x667)
- [ ] iPhone X/XS (375x812)
- [ ] iPhone XR (414x896)
- [ ] iPhone 12/13/14 (390x844)
- [ ] Android 小屏 (360x640)
- [ ] Android 中屏 (375x667)
- [ ] Android 大屏 (414x896)

## 联系方式
- 项目负责人: [待填写]
- 技术支持: [待填写]
- 项目地址: [待填写]
