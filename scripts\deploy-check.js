/**
 * 云函数部署检查脚本
 * 用于检查云函数是否正确部署
 */

// 需要检查的云函数列表
const CLOUD_FUNCTIONS = [
	'simple-test',
	'auth',
	'test-db',
	'user-center'
]

/**
 * 检查云函数部署状态
 */
async function checkCloudFunctionDeployment() {
	console.log('开始检查云函数部署状态...')
	
	const results = {
		timestamp: new Date().toISOString(),
		functions: {},
		summary: {
			total: CLOUD_FUNCTIONS.length,
			deployed: 0,
			failed: 0
		}
	}
	
	for (const functionName of CLOUD_FUNCTIONS) {
		console.log(`检查云函数: ${functionName}`)
		
		try {
			const result = await uniCloud.callFunction({
				name: functionName,
				data: { test: 'deployment-check' }
			})
			
			results.functions[functionName] = {
				deployed: true,
				success: true,
				response: result.result,
				message: '部署正常'
			}
			results.summary.deployed++
			
			console.log(`✓ ${functionName} - 部署正常`)
			
		} catch (error) {
			results.functions[functionName] = {
				deployed: false,
				success: false,
				error: error.message || error.errMsg,
				errorCode: error.errCode,
				message: getErrorMessage(error)
			}
			results.summary.failed++
			
			console.log(`✗ ${functionName} - ${getErrorMessage(error)}`)
		}
	}
	
	console.log('云函数部署检查完成:', results.summary)
	return results
}

/**
 * 获取错误信息
 */
function getErrorMessage(error) {
	if (error.errCode) {
		switch (error.errCode) {
			case 'FUNCTION_NOT_FOUND':
				return '云函数未找到，需要部署'
			case 'FUNCTION_EXECUTION_FAIL':
				return '云函数执行失败'
			case 'NETWORK_ERROR':
				return '网络连接失败'
			case 'INVALID_PARAM':
				return '参数错误'
			default:
				return `未知错误: ${error.errCode}`
		}
	}
	return error.message || error.errMsg || '未知错误'
}

/**
 * 显示部署检查结果
 */
function showDeploymentResult(results) {
	const { summary, functions } = results
	
	let title = '云函数部署检查'
	let content = `总计: ${summary.total}\n已部署: ${summary.deployed}\n失败: ${summary.failed}\n\n`
	
	// 添加详细信息
	Object.keys(functions).forEach(name => {
		const func = functions[name]
		const status = func.deployed ? '✓' : '✗'
		content += `${status} ${name}: ${func.message}\n`
	})
	
	if (summary.failed > 0) {
		content += '\n建议：\n1. 在HBuilderX中右键云函数目录\n2. 选择"上传部署"\n3. 等待部署完成后重试'
	}
	
	uni.showModal({
		title: title,
		content: content,
		showCancel: false,
		confirmText: '知道了'
	})
}

/**
 * 自动部署云函数（仅在开发环境）
 */
async function autoDeployCloudFunctions() {
	console.log('开始自动部署云函数...')
	
	// 注意：实际的自动部署需要通过HBuilderX的API或命令行工具
	// 这里只是模拟部署过程
	
	uni.showModal({
		title: '自动部署',
		content: '自动部署功能需要在HBuilderX中手动操作：\n\n1. 右键 uniCloud-aliyun/cloudfunctions\n2. 选择"上传所有云函数"\n3. 等待部署完成\n\n或者单独部署每个云函数：\n- 右键具体云函数目录\n- 选择"上传部署"',
		showCancel: false,
		confirmText: '知道了'
	})
}

/**
 * 生成部署指南
 */
function generateDeploymentGuide() {
	const guide = `
# 云函数部署指南

## 1. 检查uniCloud配置
确保 manifest.json 中的 uniCloud 配置正确：
\`\`\`json
"uniCloud": {
  "provider": "aliyun",
  "spaceId": "mp-4f58ec35-fd98-402d-99e4-2bb423eaf604",
  "clientSecret": "pZBK1la3eLXxOMrhsDatXg=="
}
\`\`\`

## 2. 部署云函数
在 HBuilderX 中：
1. 右键 \`uniCloud-aliyun/cloudfunctions\`
2. 选择 "上传所有云函数"
3. 等待部署完成

或者单独部署：
1. 右键具体云函数目录（如 \`auth\`）
2. 选择 "上传部署"
3. 等待部署完成

## 3. 初始化数据库
1. 右键 \`uniCloud-aliyun/database\`
2. 选择 "初始化云数据库"
3. 等待初始化完成

## 4. 验证部署
运行应用中的"网络诊断"功能验证部署状态。

## 常见问题
- 如果提示"云函数未找到"，请检查函数是否正确部署
- 如果提示"网络错误"，请检查网络连接和uniCloud配置
- 如果提示"执行失败"，请检查云函数代码是否有错误
`
	
	console.log(guide)
	return guide
}

// 导出函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
	module.exports = {
		checkCloudFunctionDeployment,
		showDeploymentResult,
		autoDeployCloudFunctions,
		generateDeploymentGuide
	}
}

// 在浏览器环境中添加到全局对象
if (typeof window !== 'undefined') {
	window.deployCheck = {
		checkCloudFunctionDeployment,
		showDeploymentResult,
		autoDeployCloudFunctions,
		generateDeploymentGuide
	}
}
