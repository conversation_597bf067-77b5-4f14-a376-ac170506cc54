<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        }
        
        .auth-page {
            height: 100vh;
            background-image: url('./static/logoo.png');
            background-size: contain;
            background-position: center top;
            background-repeat: no-repeat;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .nav-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 88px;
            display: flex;
            align-items: center;
            padding: 44px 32px 0;
            z-index: 100;
        }
        
        .nav-icon {
            width: 20px;
            height: 20px;
            filter: brightness(0) invert(1);
        }
        
        .auth-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }
        
        .bottom-login-area {
            position: absolute;
            bottom: 40px;
            left: 30px;
            right: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-login-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            width: 100%;
        }
        
        .main-login-btn {
            width: 100%;
            height: 60px;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: none;
            cursor: pointer;
        }
        
        .main-login-btn:active {
            transform: scale(0.98);
        }
        
        .wechat-login-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .phone-login-btn {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .main-login-icon {
            width: 24px;
            height: 24px;
        }
        
        .main-login-text {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
        }
        
        .phone-text {
            color: #667eea !important;
        }
        
        .main-agreement-section {
            margin-top: 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }
        
        .main-agreement-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: nowrap;
            white-space: nowrap;
        }
        
        .main-checkbox {
            width: 18px;
            height: 18px;
            border: 1.5px solid rgba(255, 255, 255, 0.6);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }
        
        .main-checkbox.checked {
            background: rgba(138, 43, 226, 0.8);
            border-color: rgba(138, 43, 226, 0.8);
        }
        
        .main-checkbox-icon {
            color: #ffffff;
            font-size: 12px;
            font-weight: bold;
        }
        
        .main-agreement-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            white-space: nowrap;
        }
        
        .main-agreement-links {
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        
        .main-agreement-link {
            color: rgba(138, 43, 226, 0.9);
            font-size: 14px;
            text-decoration: none;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="auth-page">
        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-left">
                <img src="./static/icons/fanhui.png" class="nav-icon" alt="返回">
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="auth-content">
        </div>

        <!-- 底部登录区域 -->
        <div class="bottom-login-area">
            <!-- 主要登录按钮 -->
            <div class="main-login-section">
                <!-- 微信登录按钮 -->
                <button class="main-login-btn wechat-login-btn">
                    <img src="./static/weixinlogo.png" class="main-login-icon" alt="微信">
                    <span class="main-login-text">微信登录</span>
                </button>

                <!-- 手机登录按钮 -->
                <button class="main-login-btn phone-login-btn">
                    <img src="./static/icons/shoujihao.png" class="main-login-icon" alt="手机">
                    <span class="main-login-text phone-text">手机登录</span>
                </button>

                <!-- 用户协议 -->
                <div class="main-agreement-section">
                    <div class="main-agreement-checkbox">
                        <div class="main-checkbox checked">
                            <span class="main-checkbox-icon">✓</span>
                        </div>
                        <span class="main-agreement-text">我已阅读并同意趣嗒同行</span>
                    </div>
                    <div class="main-agreement-links">
                        <a href="#" class="main-agreement-link">《用户协议》</a>
                        <span class="main-agreement-text"> </span>
                        <a href="#" class="main-agreement-link">《隐私政策》</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
