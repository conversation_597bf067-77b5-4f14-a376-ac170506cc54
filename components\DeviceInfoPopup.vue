<template>
  <view class="popup-overlay" v-if="visible">
    <view class="popup-content">
      <view class="popup-header">
        <text class="popup-title">温馨提示</text>
        <uni-icons type="closeempty" size="22" color="#999" @click="closePopup"></uni-icons>
      </view>
      <scroll-view scroll-y class="popup-body">
        <view class="info-section">
          <text class="section-title">为了给您提供更好的服务，趣玩星球需要获取以下设备信息与权限：</text>
        </view>

        <view class="info-section">
          <view class="info-item">
            <text class="info-label">操作系统:</text>
            <text class="info-value">{{ deviceInfo.osName }} {{ deviceInfo.osVersion }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">设备型号:</text>
            <text class="info-value">{{ deviceInfo.model }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">应用设备ID:</text>
            <text class="info-value device-id">{{ deviceInfo.deviceId }}</text>
          </view>
        </view>

        <view class="permission-section">
          <view class="permission-item" v-for="(item, index) in permissions" :key="index">
            <view class="permission-info">
              <uni-icons :type="item.icon" size="24" :color="item.authorized ? '#6F7BF5' : '#888'"></uni-icons>
              <view class="permission-text">
                <text class="permission-name">{{ item.name }}</text>
                <text class="permission-status" :class="{ 'status-authorized': item.authorized, 'status-denied': !item.authorized && item.status !== 'unknown' }">
                  {{ item.statusText }}
                </text>
              </view>
            </view>
            <button
              v-if="!item.authorized && item.status !== 'denied_permanently'"
              class="permission-button"
              size="mini"
              @click="requestPermission(item)"
            >
              去开启
            </button>
             <button
              v-if="item.status === 'denied_permanently'"
              class="permission-button"
              size="mini"
              @click="openSettings(item)"
            >
              去设置
            </button>
          </view>
        </view>
      </scroll-view>
      <view class="popup-footer">
        <button class="confirm-button" @click="handleConfirm">我已知晓</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DeviceInfoPopup',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      deviceInfo: {
        osName: '',
        osVersion: '',
        model: '',
        deviceId: '',
      },
      permissions: [
        {
          id: 'location',
          name: '位置信息',
          scope: 'scope.userLocation',
          icon: 'location-filled',
          authorized: false,
          status: 'unknown', // unknown, authorized, denied, denied_permanently
          statusText: '检测中...',
        },
        {
          id: 'camera',
          name: '相机权限',
          scope: 'scope.camera',
          icon: 'camera-filled',
          authorized: false,
          status: 'unknown',
          statusText: '检测中...',
        },
        {
          id: 'album',
          name: '相册权限',
          scope: 'scope.writePhotosAlbum', 
          icon: 'image-filled',
          authorized: false,
          status: 'unknown',
          statusText: '检测中...',
        },
      ],
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.getDeviceInfo();
        this.checkAllPermissions();
      }
    }
  },
  methods: {
    getDeviceInfo() {
      try {
        const info = uni.getSystemInfoSync();
        this.deviceInfo.osName = info.platform === 'android' ? 'Android' : info.platform === 'ios' ? 'iOS' : info.osName;
        this.deviceInfo.osVersion = info.system;
        this.deviceInfo.model = info.model;
        this.deviceInfo.deviceId = info.deviceId || '获取失败';
      } catch (e) {
        console.error('获取设备信息失败:', e);
        this.deviceInfo.osName = '未知';
        this.deviceInfo.osVersion = '未知';
        this.deviceInfo.model = '未知';
        this.deviceInfo.deviceId = '获取失败';
      }
    },
    async checkAllPermissions() {
      for (let perm of this.permissions) {
        await this.checkPermissionStatus(perm);
      }
    },
    async checkPermissionStatus(perm) {
      try {
        const settingRes = await uni.getSetting();
        if (settingRes.authSetting[perm.scope] === true) {
          perm.authorized = true;
          perm.status = 'authorized';
          perm.statusText = '已授权';
        } else if (settingRes.authSetting[perm.scope] === false) {
          perm.authorized = false;
          perm.status = 'denied_permanently'; 
          perm.statusText = '已拒绝，请手动开启';
        } else {
          perm.authorized = false;
          perm.status = 'unknown'; 
          perm.statusText = '未授权';
        }
      } catch (e) {
        console.error('检查权限 ' + perm.name + ' 失败:', e);
        perm.statusText = '检测失败';
        perm.status = 'unknown';
      }
    },
    async requestPermission(perm) {
      if (!perm.scope) {
          perm.statusText = '此功能无需额外请求'; 
          perm.authorized = true; 
          return;
      }
      try {
        // In a real environment, uni.authorize would be called.
        const authRes = await uni.authorize({ scope: perm.scope });
         if (authRes.errMsg === 'authorize:ok') {
          perm.authorized = true;
          perm.status = 'authorized';
          perm.statusText = '已授权';
        } else {
          perm.authorized = false;
          perm.status = 'denied'; // User denied via prompt
          perm.statusText = '授权失败';
        }
      } catch (e) {
        console.error('请求权限 ' + perm.name + ' 失败:', e);
        perm.authorized = false;
        // If authorize fails (e.g. user previously denied permanently and system blocks prompt)
        // it often means they need to go to settings.
        perm.status = 'denied_permanently'; 
        perm.statusText = '您已拒绝，请在设置中开启';
      }
      // Re-check status as uni.authorize might not update uni.getSetting immediately or behavior varies
      // However, often the catch block itself indicates a more permanent denial.
      // For simplicity in this auto-applied code, we will re-check.
      // A more nuanced approach might involve interpreting the specific error from uni.authorize.
      await this.checkPermissionStatus(perm);
    },
    openSettings(perm) {
        uni.showModal({
            title: '权限提示',
            content: '您已拒绝"' + perm.name + '"权限，请前往系统设置页面开启。',
            confirmText: '去设置',
            cancelText: '取消',
            success: (res) => {
                if(res.confirm) {
                    uni.openSetting({
                        success: (settingRes) => {
                           this.checkPermissionStatus(perm); 
                        },
                        fail: () => {
                           uni.showToast({title: '打开设置失败', icon: 'none'});
                        }
                    });
                }
            }
        })
    },
    closePopup() {
      this.$emit('close');
    },
    handleConfirm() {
      this.closePopup();
    },
  },
};
</script>

<style scoped>
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
.popup-content {
  background-color: #ffffff;
  width: 85%;
  max-width: 400px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
}
.popup-title {
  font-size: 17px;
  font-weight: bold;
  color: #333;
}
.popup-body {
  max-height: 60vh; 
  padding: 15px;
}
.info-section {
  margin-bottom: 15px;
}
.section-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  line-height: 1.5;
}
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #333;
  padding: 6px 0;
}
.info-label {
  color: #555;
  margin-right: 10px;
}
.info-value {
  color: #333;
  text-align: right;
}
.device-id {
  font-size: 12px;
  color: #888;
  word-break: break-all;
}

.permission-section {
  /* Styles for permission section if needed */
}
.permission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}
.permission-item:last-child {
  border-bottom: none;
}
.permission-info {
  display: flex;
  align-items: center;
}
.permission-text {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
}
.permission-name {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}
.permission-status {
  font-size: 12px;
  color: #888;
}
.status-authorized {
  color: #6F7BF5; /* 主题色 */
}
.status-denied {
  color: #E53935; /* 红色表示拒绝 */
}
.permission-button {
  background-color: #6F7BF5;
  color: white !important; /* uni-app button text color issue fix */
  border: none;
  border-radius: 20px;
  font-size: 12px !important; /* Override uni-app default */
  padding: 4px 12px !important; /* Override uni-app default */
  line-height: 1.5 !important; /* Override uni-app default */
  margin: 0 !important; /* Override uni-app default */
  height: auto; /* Override uni-app default */
}
.permission-button::after { /* Remove button border for uni-app */
    border: none;
}


.popup-footer {
  padding: 15px;
  border-top: 1px solid #f0f0f0;
}
.confirm-button {
  background-color: #6F7BF5;
  color: white;
  border-radius: 8px;
  font-size: 16px;
  text-align: center;
  border: none;
}
.confirm-button::after { /* Remove button border for uni-app */
    border: none;
}
</style> 