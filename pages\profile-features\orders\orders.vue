<template>
	<view class="orders-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<uni-icons type="left" size="24" color="#333"></uni-icons>
					<text class="back-text">返回</text>
				</view>
				<text class="nav-title">我的订单</text>
				<view class="nav-right">
					<view class="search-btn" @tap="searchOrders">
						<uni-icons type="search" size="20" color="#666"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 订单状态筛选 -->
		<view class="order-tabs">
			<scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false">
				<view class="tab-item" 
					v-for="(tab, index) in orderTabs" 
					:key="index"
					:class="{ active: currentTab === index }"
					@tap="switchTab(index)">
					<text class="tab-text">{{ tab.name }}</text>
					<view class="tab-badge" v-if="tab.count > 0">{{ tab.count }}</view>
				</view>
			</scroll-view>
		</view>

		<!-- 订单列表 -->
		<scroll-view class="orders-content" scroll-y="true" @scrolltolower="loadMore">
			<!-- 空状态 -->
			<view class="empty-state" v-if="currentOrders.length === 0">
				<view class="empty-icon">
					<uni-icons type="list" size="80" color="#ddd"></uni-icons>
				</view>
				<text class="empty-title">暂无订单</text>
				<text class="empty-desc">{{ getEmptyDesc() }}</text>
				<view class="empty-action" @tap="goToMall">
					<text class="action-text">去逛逛</text>
				</view>
			</view>

			<!-- 订单列表 -->
			<view class="order-list" v-else>
				<view class="order-item" v-for="order in currentOrders" :key="order.id" @tap="viewOrderDetail(order)">
					<view class="order-header">
						<view class="order-info">
							<text class="order-number">订单号：{{ order.orderNumber }}</text>
							<text class="order-time">{{ order.createTime }}</text>
						</view>
						<view class="order-status" :class="order.statusClass">
							<text class="status-text">{{ order.statusText }}</text>
						</view>
					</view>

					<view class="order-content">
						<view class="product-list">
							<view class="product-item" v-for="product in order.products" :key="product.id">
								<image class="product-image" :src="product.image" mode="aspectFill"></image>
								<view class="product-info">
									<text class="product-name">{{ product.name }}</text>
									<text class="product-spec" v-if="product.spec">{{ product.spec }}</text>
									<view class="product-price-qty">
										<text class="product-price">¥{{ product.price }}</text>
										<text class="product-qty">x{{ product.quantity }}</text>
									</view>
								</view>
							</view>
						</view>

						<view class="order-total">
							<text class="total-label">实付款：</text>
							<text class="total-amount">¥{{ order.totalAmount }}</text>
						</view>
					</view>

					<view class="order-actions">
						<view class="action-btn secondary" v-if="order.canContact" @tap.stop="contactService(order)">
							<text class="btn-text">联系客服</text>
						</view>
						<view class="action-btn secondary" v-if="order.canCancel" @tap.stop="cancelOrder(order)">
							<text class="btn-text">取消订单</text>
						</view>
						<view class="action-btn secondary" v-if="order.canRefund" @tap.stop="requestRefund(order)">
							<text class="btn-text">申请退款</text>
						</view>
						<view class="action-btn primary" v-if="order.canPay" @tap.stop="payOrder(order)">
							<text class="btn-text">立即支付</text>
						</view>
						<view class="action-btn primary" v-if="order.canConfirm" @tap.stop="confirmOrder(order)">
							<text class="btn-text">确认收货</text>
						</view>
						<view class="action-btn secondary" v-if="order.canReview" @tap.stop="reviewOrder(order)">
							<text class="btn-text">评价</text>
						</view>
						<view class="action-btn secondary" v-if="order.canRepurchase" @tap.stop="repurchase(order)">
							<text class="btn-text">再次购买</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore">
				<text class="load-text">加载更多...</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				currentTab: 0,
				hasMore: true,
				
				// 订单状态标签
				orderTabs: [
					{ name: '全部', count: 0 },
					{ name: '待付款', count: 2 },
					{ name: '待发货', count: 1 },
					{ name: '待收货', count: 0 },
					{ name: '待评价', count: 1 },
					{ name: '已完成', count: 5 }
				],

				// 所有订单数据
				allOrders: [
					{
						id: '001',
						orderNumber: '202412200001',
						createTime: '2024-12-20 14:30',
						status: 'pending_payment',
						statusText: '待付款',
						statusClass: 'pending',
						totalAmount: '168.00',
						canPay: true,
						canCancel: true,
						canContact: true,
						products: [
							{
								id: 'p001',
								name: '闲伴黄金会员',
								spec: '1个月',
								price: '168.00',
								quantity: 1,
								image: 'https://s1.imagehub.cc/images/2025/05/26/vip-gold.png'
							}
						]
					},
					{
						id: '002',
						orderNumber: '202412190002',
						createTime: '2024-12-19 16:45',
						status: 'pending_delivery',
						statusText: '待发货',
						statusClass: 'processing',
						totalAmount: '299.00',
						canContact: true,
						canRefund: true,
						products: [
							{
								id: 'p002',
								name: '闲伴周边礼品盒',
								spec: '限量版',
								price: '299.00',
								quantity: 1,
								image: 'https://s1.imagehub.cc/images/2025/05/26/gift-box.png'
							}
						]
					}
				]
			}
		},

		computed: {
			currentOrders() {
				if (this.currentTab === 0) {
					return this.allOrders;
				}
				const statusMap = {
					1: 'pending_payment',
					2: 'pending_delivery', 
					3: 'pending_receipt',
					4: 'pending_review',
					5: 'completed'
				};
				return this.allOrders.filter(order => order.status === statusMap[this.currentTab]);
			}
		},

		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},

		methods: {
			goBack() {
				uni.switchTab({
					url: '/pages/profile/profile'
				});
			},

			searchOrders() {
				uni.showToast({
					title: '搜索功能开发中',
					icon: 'none'
				});
			},

			switchTab(index) {
				this.currentTab = index;
			},

			getEmptyDesc() {
				const descs = [
					'还没有任何订单哦',
					'暂无待付款订单',
					'暂无待发货订单', 
					'暂无待收货订单',
					'暂无待评价订单',
					'暂无已完成订单'
				];
				return descs[this.currentTab];
			},

			goToMall() {
				uni.navigateTo({
					url: '/pages/profile-features/mall/mall'
				});
			},

			viewOrderDetail(order) {
				uni.navigateTo({
					url: `/pages/profile-features/orders/order-detail?id=${order.id}`
				});
			},

			// 订单操作
			payOrder(order) {
				uni.showModal({
					title: '确认支付',
					content: `确认支付 ¥${order.totalAmount}？`,
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '支付功能开发中',
								icon: 'none'
							});
						}
					}
				});
			},

			cancelOrder(order) {
				uni.showModal({
					title: '取消订单',
					content: '确定要取消这个订单吗？',
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '订单已取消',
								icon: 'success'
							});
						}
					}
				});
			},

			confirmOrder(order) {
				uni.showModal({
					title: '确认收货',
					content: '确认已收到商品？',
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '确认收货成功',
								icon: 'success'
							});
						}
					}
				});
			},

			requestRefund(order) {
				uni.showToast({
					title: '退款功能开发中',
					icon: 'none'
				});
			},

			contactService(order) {
				uni.showToast({
					title: '客服功能开发中',
					icon: 'none'
				});
			},

			reviewOrder(order) {
				uni.showToast({
					title: '评价功能开发中',
					icon: 'none'
				});
			},

			repurchase(order) {
				uni.showToast({
					title: '再次购买功能开发中',
					icon: 'none'
				});
			},

			loadMore() {
				if (!this.hasMore) return;
				
				setTimeout(() => {
					this.hasMore = false;
				}, 1000);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.orders-page {
		background: #f5f5f5;
		min-height: 100vh;
	}

	.status-bar {
		background: #fff;
	}

	.header {
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;

		.nav-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;
			height: 88rpx;

			.nav-left {
				display: flex;
				align-items: center;
				gap: 8rpx;
				flex: 1;

				.back-text {
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				text-align: center;
				flex: 2;
			}

			.nav-right {
				flex: 1;
				display: flex;
				justify-content: flex-end;

				.search-btn {
					width: 48rpx;
					height: 48rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: #f8f9fa;
					border-radius: 24rpx;
				}
			}
		}
	}

	// 订单状态筛选
	.order-tabs {
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;
		padding: 0 24rpx;

		.tabs-scroll {
			white-space: nowrap;

			.tab-item {
				display: inline-block;
				position: relative;
				padding: 24rpx 32rpx;
				margin-right: 16rpx;
				border-radius: 24rpx;
				transition: all 0.3s ease;

				&.active {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

					.tab-text {
						color: #fff;
						font-weight: 600;
					}

					.tab-badge {
						background: #fff;
						color: #667eea;
					}
				}

				.tab-text {
					font-size: 28rpx;
					color: #666;
					transition: all 0.3s ease;
				}

				.tab-badge {
					position: absolute;
					top: 8rpx;
					right: 8rpx;
					background: #ff4757;
					color: #fff;
					font-size: 20rpx;
					padding: 4rpx 8rpx;
					border-radius: 12rpx;
					min-width: 24rpx;
					text-align: center;
					line-height: 1;
				}
			}
		}
	}

	// 订单内容区域
	.orders-content {
		flex: 1;
		padding: 16rpx 24rpx;

		// 空状态
		.empty-state {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 120rpx 40rpx;
			text-align: center;

			.empty-icon {
				margin-bottom: 32rpx;
				opacity: 0.5;
			}

			.empty-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				margin-bottom: 16rpx;
			}

			.empty-desc {
				font-size: 26rpx;
				color: #999;
				margin-bottom: 48rpx;
				line-height: 1.5;
			}

			.empty-action {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				padding: 20rpx 48rpx;
				border-radius: 32rpx;
				box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);

				.action-text {
					font-size: 28rpx;
					color: #fff;
					font-weight: 600;
				}
			}
		}

		// 订单列表
		.order-list {
			.order-item {
				background: #fff;
				border-radius: 16rpx;
				margin-bottom: 24rpx;
				overflow: hidden;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
				}

				.order-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 24rpx;
					border-bottom: 1rpx solid #f5f5f5;

					.order-info {
						.order-number {
							font-size: 26rpx;
							color: #333;
							font-weight: 500;
							display: block;
							margin-bottom: 8rpx;
						}

						.order-time {
							font-size: 22rpx;
							color: #999;
						}
					}

					.order-status {
						padding: 8rpx 16rpx;
						border-radius: 16rpx;
						font-size: 24rpx;
						font-weight: 500;

						&.pending {
							background: rgba(255, 193, 7, 0.1);
							color: #ffc107;
						}

						&.processing {
							background: rgba(23, 162, 184, 0.1);
							color: #17a2b8;
						}

						&.completed {
							background: rgba(40, 167, 69, 0.1);
							color: #28a745;
						}

						&.cancelled {
							background: rgba(220, 53, 69, 0.1);
							color: #dc3545;
						}
					}
				}

				.order-content {
					padding: 24rpx;

					.product-list {
						.product-item {
							display: flex;
							align-items: center;
							margin-bottom: 16rpx;

							&:last-child {
								margin-bottom: 0;
							}

							.product-image {
								width: 120rpx;
								height: 120rpx;
								border-radius: 12rpx;
								margin-right: 16rpx;
								background: #f5f5f5;
							}

							.product-info {
								flex: 1;

								.product-name {
									font-size: 28rpx;
									color: #333;
									font-weight: 500;
									display: block;
									margin-bottom: 8rpx;
									line-height: 1.4;
								}

								.product-spec {
									font-size: 22rpx;
									color: #999;
									display: block;
									margin-bottom: 12rpx;
								}

								.product-price-qty {
									display: flex;
									justify-content: space-between;
									align-items: center;

									.product-price {
										font-size: 28rpx;
										color: #ff4757;
										font-weight: 600;
									}

									.product-qty {
										font-size: 24rpx;
										color: #666;
									}
								}
							}
						}
					}

					.order-total {
						display: flex;
						justify-content: flex-end;
						align-items: center;
						margin-top: 16rpx;
						padding-top: 16rpx;
						border-top: 1rpx solid #f5f5f5;

						.total-label {
							font-size: 26rpx;
							color: #666;
							margin-right: 8rpx;
						}

						.total-amount {
							font-size: 32rpx;
							color: #ff4757;
							font-weight: 700;
						}
					}
				}

				.order-actions {
					display: flex;
					justify-content: flex-end;
					align-items: center;
					gap: 16rpx;
					padding: 24rpx;
					border-top: 1rpx solid #f5f5f5;

					.action-btn {
						padding: 16rpx 32rpx;
						border-radius: 24rpx;
						font-size: 26rpx;
						font-weight: 500;
						transition: all 0.3s ease;

						&.primary {
							background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
							color: #fff;
							box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);

							&:active {
								transform: scale(0.95);
							}
						}

						&.secondary {
							background: #f8f9fa;
							color: #666;
							border: 1rpx solid #e9ecef;

							&:active {
								background: #e9ecef;
							}
						}

						.btn-text {
							font-size: 26rpx;
						}
					}
				}
			}
		}

		// 加载更多
		.load-more {
			text-align: center;
			padding: 32rpx;

			.load-text {
				font-size: 26rpx;
				color: #999;
			}
		}
	}
</style>
