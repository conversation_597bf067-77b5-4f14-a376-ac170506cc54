'use strict';

const uniID = require('uni-id-common');

exports.main = async (event, context) => {
  console.log('event : ', event);
  
  const { action, params } = event;
  const uniIdIns = uniID.createInstance({
    context: context
  });
  
  let result = {};
  
  switch (action) {
    // 发送短信验证码
    case 'sendSmsCode':
      result = await uniIdIns.sendSmsCode({
        mobile: params.mobile,
        scene: 'login'
      });
      break;
      
    // 手机号验证码登录
    case 'loginBySms':
      result = await uniIdIns.loginBySms({
        mobile: params.mobile,
        code: params.code
      });
      break;
      
    // 获取用户信息
    case 'getUserInfo':
      result = await uniIdIns.getUserInfo({
        uid: params.uid
      });
      break;
      
    // 更新用户信息
    case 'updateUser':
      result = await uniIdIns.updateUser({
        uid: params.uid,
        nickname: params.nickname,
        avatar: params.avatar,
        gender: params.gender,
        birthday: params.birthday,
        region: params.region
      });
      break;
      
    // 退出登录
    case 'logout':
      result = await uniIdIns.logout();
      break;
      
    default:
      result = {
        code: 403,
        message: '非法访问'
      };
  }
  
  return result;
};
