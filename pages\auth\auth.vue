<template>
	<view class="auth-page" @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="getStatusBarStyle()"></view>

		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="back-button" @click="goBack">
				<image src="/static/fanhuilogo.png" class="back-icon" mode="aspectFit"></image>
			</view>
		</view>

		<!-- 手势返回指示器 -->
		<view class="gesture-indicator" v-if="showGestureIndicator" :style="{ opacity: gestureOpacity }">
			<image src="/static/fanhuilogo.png" class="gesture-icon" mode="aspectFit"></image>
			<text class="gesture-text">松手返回</text>
		</view>

		<!-- 主要内容 -->
		<view class="auth-content">
			<!-- 智能识别提示 -->
			<view class="auth-tip" v-if="showTip">
				<text class="tip-text">{{ tipText }}</text>
			</view>
		</view>

		<!-- 底部登录区域 -->
		<view class="bottom-login-area">
			<!-- 主要登录按钮 -->
			<view class="main-login-section">
				<!-- 微信登录按钮 -->
				<view class="main-login-btn wechat-login-btn" :class="{ 'disabled': !canLogin }" @click="wechatLogin">
					<image src="/static/weixinlogo.png" class="main-login-icon" mode="aspectFit"></image>
					<text class="main-login-text">微信登录</text>
				</view>

				<!-- 手机登录按钮 -->
				<view class="main-login-btn phone-login-btn" :class="{ 'disabled': !canLogin }" @click="phoneLogin">
					<image src="/static/icons/shoujihao.png" class="main-login-icon" mode="aspectFit"></image>
					<text class="main-login-text phone-text">手机登录</text>
				</view>

				<!-- 用户协议 -->
				<view class="main-agreement-section">
					<view class="main-agreement-row">
						<view class="main-agreement-checkbox" @click="toggleMainAgreement">
							<view class="main-checkbox" :class="{ checked: mainAgreement }">
								<text v-if="mainAgreement" class="main-checkbox-icon">✓</text>
							</view>
							<text class="main-agreement-text">我已阅读并同意趣嗒同行</text>
						</view>
					</view>
					<view class="main-agreement-links">
						<text class="main-agreement-link" @click="showUserAgreement">《用户协议》</text>
						<text class="main-agreement-text"> </text>
						<text class="main-agreement-link" @click="showPrivacyPolicy">《隐私政策》</text>
					</view>
				</view>
			</view>

			<!-- 详细表单（隐藏，点击手机登录后显示） -->
			<view v-if="showDetailForm" class="detail-form-overlay" @click="hideDetailForm">
				<view class="detail-form" @click.stop>
					<view class="detail-form-header">
						<text class="detail-form-title">{{ authStep === 'login' ? '手机登录' : '手机注册' }}</text>
						<view class="detail-form-close" @click="hideDetailForm">
							<text class="close-icon">×</text>
						</view>
					</view>

					<!-- 智能识别提示 -->
					<view class="detail-tip" v-if="showTip">
						<text class="detail-tip-text">{{ tipText }}</text>
					</view>

					<!-- 手机号输入 -->
					<view class="detail-form-item">
						<view class="detail-input-container" :class="{ focused: focusedField === 'mobile', 'has-value': authForm.mobile }">
							<image src="/static/icons/shoujihao.png" class="detail-input-icon" mode="aspectFit"></image>
							<view class="detail-input-wrapper">
								<text class="detail-floating-label" :class="{ active: focusedField === 'mobile' || authForm.mobile }">手机号</text>
								<input
									class="detail-form-input"
									type="number"
									v-model="authForm.mobile"
									@focus="onInputFocus('mobile')"
									@blur="onMobileBlur"
									@input="onMobileInput"
									maxlength="11"
									placeholder=""
								/>
							</view>
						</view>
					</view>

					<!-- 密码输入（登录时显示） -->
					<view v-if="authStep === 'login'" class="detail-form-item">
						<view class="detail-input-container" :class="{ focused: focusedField === 'password', 'has-value': authForm.password }">
							<image src="/static/icons/mima.png" class="detail-input-icon" mode="aspectFit"></image>
							<view class="detail-input-wrapper">
								<text class="detail-floating-label" :class="{ active: focusedField === 'password' || authForm.password }">密码</text>
								<input
									class="detail-form-input"
									:type="showPassword ? 'text' : 'password'"
									v-model="authForm.password"
									@focus="onInputFocus('password')"
									@blur="onInputBlur('password')"
									placeholder=""
								/>
							</view>
							<view class="detail-password-toggle" @click="togglePassword">
								<image :src="showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'" class="detail-toggle-icon" mode="aspectFit"></image>
							</view>
						</view>
					</view>

					<!-- 用户名输入（注册时显示） -->
					<view v-if="authStep === 'register'" class="detail-form-item">
						<view class="detail-input-container" :class="{ focused: focusedField === 'username', 'has-value': authForm.username }">
							<image src="/static/icons/yonghuming.png" class="detail-input-icon" mode="aspectFit"></image>
							<view class="detail-input-wrapper">
								<text class="detail-floating-label" :class="{ active: focusedField === 'username' || authForm.username }">用户名</text>
								<input
									class="detail-form-input"
									type="text"
									v-model="authForm.username"
									@focus="onInputFocus('username')"
									@blur="onInputBlur('username')"
									placeholder=""
									maxlength="20"
								/>
							</view>
						</view>
					</view>

					<!-- 验证码输入（注册时显示） -->
					<view v-if="authStep === 'register'" class="detail-form-item">
						<view class="detail-input-container" :class="{ focused: focusedField === 'code', 'has-value': authForm.code }">
							<image src="/static/icons/yanzhengma.png" class="detail-input-icon" mode="aspectFit"></image>
							<view class="detail-input-wrapper">
								<text class="detail-floating-label" :class="{ active: focusedField === 'code' || authForm.code }">验证码</text>
								<input
									class="detail-form-input"
									type="number"
									v-model="authForm.code"
									@focus="onInputFocus('code')"
									@blur="onInputBlur('code')"
									placeholder=""
									maxlength="4"
								/>
							</view>
							<view class="detail-code-btn" :class="{ disabled: codeCountdown > 0 }" @click="getVerificationCode">
								<text class="detail-code-text">{{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}</text>
							</view>
						</view>
					</view>

					<!-- 密码设置（注册时显示） -->
					<view v-if="authStep === 'register'" class="detail-form-item">
						<view class="detail-input-container" :class="{ focused: focusedField === 'password', 'has-value': authForm.password }">
							<image src="/static/icons/mima.png" class="detail-input-icon" mode="aspectFit"></image>
							<view class="detail-input-wrapper">
								<text class="detail-floating-label" :class="{ active: focusedField === 'password' || authForm.password }">设置密码</text>
								<input
									class="detail-form-input"
									:type="showPassword ? 'text' : 'password'"
									v-model="authForm.password"
									@focus="onInputFocus('password')"
									@blur="onInputBlur('password')"
									placeholder=""
								/>
							</view>
							<view class="detail-password-toggle" @click="togglePassword">
								<image :src="showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'" class="detail-toggle-icon" mode="aspectFit"></image>
							</view>
						</view>
					</view>

					<!-- 忘记密码（登录时显示） -->
					<view v-if="authStep === 'login'" class="detail-forgot-password" @click="forgotPassword">
						<text class="detail-forgot-text">忘记密码？</text>
					</view>

					<!-- 提交按钮 -->
					<view class="detail-submit-section">
						<view class="detail-submit-btn" :class="{ disabled: !canSubmit }" @click="handleSubmit">
							<text class="detail-submit-text">{{ getSubmitText() }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 验证码弹窗 -->
		<view v-if="showCodeModal" class="code-modal-overlay" @click="closeCodeModal">
			<view class="code-modal" @click.stop>
				<!-- 顶部装饰 -->
				<view class="modal-decoration">
					<view class="decoration-circle circle-1"></view>
					<view class="decoration-circle circle-2"></view>
					<view class="decoration-circle circle-3"></view>
				</view>
				
				<!-- 弹窗内容 -->
				<view class="modal-content">
					<text class="modal-title">验证码</text>
					<text class="modal-subtitle">您的验证码是：</text>
					<view class="code-display">
						<text class="code-number">{{ generatedCode }}</text>
					</view>
					<text class="modal-tip">点击下方按钮可自动填入验证码</text>
					
					<view class="modal-buttons">
						<view class="modal-btn auto-fill" @click="autoFillCode">
							<text class="btn-text">自动填入</text>
						</view>
						<view class="modal-btn close-btn" @click="closeCodeModal">
							<text class="btn-text">关闭</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getLocationByIP
	} from '@/utils/ipLocation.js'
	import gestureBackMixin from '@/mixins/gesture-back.js'

export default {
	mixins: [gestureBackMixin],
	data() {
		return {
			statusBarHeight: 0,
			logoUrl: '/static/app-logo-new.png',
			logoError: false,
			authStep: 'mobile', // 'mobile', 'login', 'register'
			userExists: false,
			focusedField: '',
			showPassword: false,
			showTip: false,
			tipText: '',
			authForm: {
				mobile: '',
				password: '',
				username: '',
				code: '',
				agreement: false
			},
			codeCountdown: 0,
			codeTimer: null,
			showCodeModal: false,
			generatedCode: '',
			showDetailForm: false,
			mainAgreement: false
		}
	},
	
	computed: {
		canSubmit() {
			if (this.authStep === 'mobile') {
				return this.authForm.mobile.length === 11
			} else if (this.authStep === 'login') {
				return this.authForm.mobile.length === 11 && this.authForm.password.length >= 6
			} else if (this.authStep === 'register') {
				return this.authForm.mobile.length === 11 &&
					   this.authForm.username.length >= 2 &&
					   this.authForm.code.length === 4 &&
					   this.authForm.password.length >= 6 &&
					   this.authForm.agreement
			}
			return false
		},

		// 检查是否可以进行登录操作（需要同意协议）
		canLogin() {
			return this.mainAgreement
		}
	},
	
	onLoad() {
		this.getSystemInfo()
	},
	
	methods: {
		getSystemInfo() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight || 0
		},

		// 微信登录
		wechatLogin() {
			// 检查是否同意用户协议
			if (!this.mainAgreement) {
				uni.showToast({
					title: '请先同意用户协议和隐私政策',
					icon: 'none',
					duration: 2000
				})
				return
			}

			uni.showToast({
				title: '微信登录开发中',
				icon: 'none',
				duration: 2000
			})
		},

		// 手机登录 - 直接跳转到手机登录页面（包含注册和登录功能）
		phoneLogin() {
			// 检查是否同意用户协议
			if (!this.mainAgreement) {
				uni.showToast({
					title: '请先同意用户协议和隐私政策',
					icon: 'none',
					duration: 2000
				})
				return
			}

			uni.navigateTo({
				url: '/pages/auth/phone-register/phone-register'
			})
		},

		// 隐藏详细表单
		hideDetailForm() {
			this.showDetailForm = false
		},

		// 切换主协议勾选
		toggleMainAgreement() {
			this.mainAgreement = !this.mainAgreement
		},

		// 生成智能趣嗒ID
		generateSmartPlanetId() {
			const existingUsers = uni.getStorageSync('registeredUsers') || []
			const usedIds = existingUsers.map(user => user.planet_id)

			// 从7位数开始尝试
			let digits = 7
			let maxAttempts = 10000 // 防止无限循环
			let attempts = 0

			while (attempts < maxAttempts) {
				const id = this.generateRandomId(digits)

				// 检查是否已被使用
				if (usedIds.includes(id)) {
					attempts++
					continue
				}

				// 检查是否符合规则
				if (this.isValidPlanetId(id)) {
					return id
				}

				attempts++

				// 如果当前位数尝试次数过多，增加位数
				if (attempts > 1000 && digits < 10) {
					digits++
					attempts = 0
				}
			}

			// 如果都失败了，返回时间戳作为备用
			return Date.now().toString().slice(-8)
		},

		// 生成指定位数的随机ID
		generateRandomId(digits) {
			let id = ''
			// 第一位不能是0
			id += Math.floor(Math.random() * 9) + 1
			// 其余位数
			for (let i = 1; i < digits; i++) {
				id += Math.floor(Math.random() * 10)
			}
			return id
		},

		// 验证ID是否符合规则
		isValidPlanetId(id) {
			// 不能0开头（已在生成时处理）
			if (id[0] === '0') return false

			// 不能是豹子号（所有数字相同）
			if (this.isLeopardNumber(id)) return false

			// 不能是爱情号（包含520、1314等）
			if (this.isLoveNumber(id)) return false

			// 不能是顺子号（连续数字）
			if (this.isStraightNumber(id)) return false

			// 不能是靓号（重复数字过多）
			if (this.isPrettyNumber(id)) return false

			return true
		},

		// 检查是否为豹子号
		isLeopardNumber(id) {
			const firstDigit = id[0]
			return id.split('').every(digit => digit === firstDigit)
		},

		// 检查是否为爱情号
		isLoveNumber(id) {
			const lovePatterns = ['520', '521', '1314', '1413', '1420', '1314520', '5201314']
			return lovePatterns.some(pattern => id.includes(pattern))
		},

		// 检查是否为顺子号
		isStraightNumber(id) {
			const digits = id.split('').map(Number)

			// 检查递增顺子
			let isAscending = true
			let isDescending = true

			for (let i = 1; i < digits.length; i++) {
				if (digits[i] !== digits[i-1] + 1) {
					isAscending = false
				}
				if (digits[i] !== digits[i-1] - 1) {
					isDescending = false
				}
			}

			return isAscending || isDescending
		},

		// 检查是否为靓号
		isPrettyNumber(id) {
			const digitCount = {}
			for (let digit of id) {
				digitCount[digit] = (digitCount[digit] || 0) + 1
			}

			// 如果有数字重复超过一半，认为是靓号
			const maxCount = Math.max(...Object.values(digitCount))
			return maxCount > Math.floor(id.length / 2)
		},

		// 验证手机号格式
		validateMobile(mobile) {
			const mobileReg = /^1[3-9]\d{9}$/
			return mobileReg.test(mobile)
		},

		// 获取当前IP地区信息
		async getCurrentIPLocation() {
			try {
				// 先检查缓存
				const cachedLocation = this.getCachedLocationInfo()
				if (cachedLocation) {
					console.log('使用缓存的IP地区信息:', cachedLocation.fullLocation)
					return cachedLocation
				}

				// 获取新的IP地区信息
				const locationData = await this.getLocationByIP()

				// 缓存结果
				this.cacheLocationInfo(locationData)

				console.log('获取到IP地区信息:', locationData.fullLocation)
				return locationData

			} catch (error) {
				console.error('获取IP地区失败:', error)
				return {
					ip: '',
					country: '中国',
					province: '上海市',
					city: '上海市',
					fullLocation: '上海',
					accuracy: 'low'
				}
			}
		},

		// IP地区查询
		async getLocationByIP(ip = null) {
			return new Promise(async (resolve, reject) => {
				try {
					let targetIP = ip

					// 如果没有传入IP，先获取用户IP
					if (!targetIP) {
						try {
							targetIP = await this.getUserIP()
						} catch (error) {
							console.error('获取IP失败:', error)
							resolve(this.getDefaultLocation())
							return
						}
					}

					// 使用ip-api.com查询地区
					this.getLocationFromAPI(targetIP)
						.then(resolve)
						.catch(() => {
							// API失败，返回默认位置
							resolve(this.getDefaultLocation())
						})

				} catch (error) {
					console.error('IP地区查询失败:', error)
					resolve(this.getDefaultLocation())
				}
			})
		},

		// 获取用户IP（多重备用方案）
		async getUserIP() {
			const apis = [
				'https://api.ipify.org?format=json',
				'https://ipapi.co/json/',
				'https://ip-api.com/json/',
				'https://httpbin.org/ip'
			]

			for (let i = 0; i < apis.length; i++) {
				try {
					const result = await this.tryGetIPFromAPI(apis[i])
					if (result) {
						console.log(`IP获取成功，使用API${i + 1}:`, result)
						return result
					}
				} catch (error) {
					console.log(`API${i + 1}失败:`, error.message)
					continue
				}
			}

			throw new Error('所有IP获取API都失败')
		},

		// 尝试从单个API获取IP
		async tryGetIPFromAPI(url) {
			return new Promise((resolve, reject) => {
				uni.request({
					url: url,
					method: 'GET',
					timeout: 3000,
					success: (res) => {
						console.log('API响应:', url, res.data)

						if (res.data) {
							// 处理不同API的响应格式
							let ip = null

							if (res.data.ip) {
								ip = res.data.ip // ipapi.co, httpbin.org
							} else if (res.data.query) {
								ip = res.data.query // ip-api.com
							} else if (typeof res.data === 'string') {
								ip = res.data.trim() // 纯文本响应
							}

							if (ip && this.isValidIP(ip)) {
								resolve(ip)
							} else {
								reject(new Error('无效的IP格式'))
							}
						} else {
							reject(new Error('API返回空数据'))
						}
					},
					fail: (error) => {
						reject(new Error(`网络请求失败: ${error.errMsg || 'unknown'}`))
					}
				})
			})
		},

		// 验证IP格式
		isValidIP(ip) {
			const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
			const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
			return ipv4Regex.test(ip) || ipv6Regex.test(ip)
		},

		// 从API获取地区信息（多重备用方案）
		async getLocationFromAPI(ip) {
			const locationAPIs = [
				{
					url: `https://ip-api.com/json/${ip}?fields=status,country,regionName,city,district,isp,query&lang=zh-CN`,
					type: 'ip-api'
				},
				{
					url: `https://ipapi.co/${ip}/json/`,
					type: 'ipapi'
				},
				{
					url: `https://api.ip.sb/geoip/${ip}`,
					type: 'ipsb'
				}
			]

			for (let i = 0; i < locationAPIs.length; i++) {
				try {
					const result = await this.tryGetLocationFromAPI(locationAPIs[i], ip)
					if (result) {
						console.log(`地区获取成功，使用API${i + 1}:`, result.fullLocation)
						return result
					}
				} catch (error) {
					console.log(`地区API${i + 1}失败:`, error.message)
					continue
				}
			}

			throw new Error('所有地区获取API都失败')
		},

		// 尝试从单个地区API获取信息
		async tryGetLocationFromAPI(apiConfig, ip) {
			return new Promise((resolve, reject) => {
				uni.request({
					url: apiConfig.url,
					method: 'GET',
					timeout: 5000,
					success: (res) => {
						console.log('地区API响应:', apiConfig.type, res.data)

						if (res.data) {
							try {
								const location = this.formatLocationDataByType(res.data, apiConfig.type)
								if (location && location.fullLocation) {
									resolve(location)
								} else {
									reject(new Error('地区数据格式化失败'))
								}
							} catch (error) {
								reject(new Error(`数据解析失败: ${error.message}`))
							}
						} else {
							reject(new Error('API返回空数据'))
						}
					},
					fail: (error) => {
						reject(new Error(`网络请求失败: ${error.errMsg || 'unknown'}`))
					}
				})
			})
		},

		// 根据API类型格式化地区数据
		formatLocationDataByType(data, apiType) {
			let location = {
				ip: '',
				country: '',
				province: '',
				city: '',
				district: '',
				isp: '',
				fullLocation: '',
				accuracy: 'high'
			}

			try {
				if (apiType === 'ip-api') {
					// ip-api.com 格式
					if (data.status !== 'success') {
						throw new Error('ip-api返回失败状态')
					}
					location.ip = data.query || ''
					location.country = data.country || '中国'
					location.province = data.regionName || ''
					location.city = data.city || ''
					location.district = data.district || ''
					location.isp = data.isp || ''

				} else if (apiType === 'ipapi') {
					// ipapi.co 格式
					location.ip = data.ip || ''
					location.country = data.country_name || '中国'
					location.province = data.region || ''
					location.city = data.city || ''
					location.isp = data.org || ''

				} else if (apiType === 'ipsb') {
					// api.ip.sb 格式
					location.ip = data.ip || ''
					location.country = data.country || '中国'
					location.province = data.region || ''
					location.city = data.city || ''
					location.isp = data.organization || ''
				}

				// 构建地址 - 只显示省级或直辖市
				if (location.country === '中国' || location.country === 'China') {
					location.fullLocation = this.formatChineseLocation(location.province, location.city)
				} else {
					location.fullLocation = location.country
				}

				// 如果地址为空，使用默认
				if (!location.fullLocation.trim()) {
					location = this.getDefaultLocation()
					location.ip = data.query || data.ip || ''
				}

				return location

			} catch (error) {
				console.error('格式化地区数据失败:', error)
				return this.getDefaultLocation()
			}
		},

		// 格式化地区数据（兼容旧方法）
		formatLocationData(data) {
			let location = {
				ip: data.query || '',
				country: data.country || '中国',
				province: data.regionName || '',
				city: data.city || '',
				district: data.district || '',
				isp: data.isp || '',
				fullLocation: '',
				accuracy: 'high'
			}

			// 构建地址 - 只显示省级或直辖市
			if (location.country === '中国' || location.country === 'China') {
				location.fullLocation = this.formatChineseLocation(location.province, location.city)
			} else {
				location.fullLocation = `${location.country}`
			}

			// 如果地址为空，使用默认
			if (!location.fullLocation.trim()) {
				location = this.getDefaultLocation()
				location.ip = data.query || ''
			}

			return location
		},

		// 格式化中国地区显示（只显示省级或直辖市）
		formatChineseLocation(province, city) {
			if (!province && !city) return '上海'

			// 直辖市列表
			const municipalities = ['北京', '上海', '天津', '重庆']

			// 特别行政区
			const specialRegions = ['香港', '澳门']

			// 处理省份信息
			let targetLocation = province || city || ''

			// 去除常见后缀
			targetLocation = targetLocation
				.replace(/市$/, '')
				.replace(/省$/, '')
				.replace(/自治区$/, '')
				.replace(/特别行政区$/, '')
				.replace(/维吾尔$/, '')
				.replace(/回族$/, '')
				.replace(/壮族$/, '')
				.trim()

			// 检查是否是直辖市
			const isDirectMunicipality = municipalities.some(m =>
				targetLocation.includes(m) || (city && city.includes(m))
			)

			// 检查是否是特别行政区
			const isSpecialRegion = specialRegions.some(s =>
				targetLocation.includes(s) || (city && city.includes(s))
			)

			if (isDirectMunicipality) {
				// 直辖市：返回直辖市名称
				const municipality = municipalities.find(m =>
					targetLocation.includes(m) || (city && city.includes(m))
				)
				return municipality || targetLocation
			} else if (isSpecialRegion) {
				// 特别行政区：返回特别行政区名称
				const specialRegion = specialRegions.find(s =>
					targetLocation.includes(s) || (city && city.includes(s))
				)
				return specialRegion || targetLocation
			} else {
				// 普通省份：返回省份名称
				return targetLocation || '上海'
			}
		},

		// 简化地区名称
		simplifyLocationName(location) {
			if (!location) return '未知'

			return location
				.replace(/省$/, '')
				.replace(/市$/, '')
				.replace(/自治区$/, '')
				.replace(/特别行政区$/, '')
				.replace(/维吾尔$/, '')
				.replace(/回族$/, '')
				.replace(/壮族$/, '')
				.trim()
		},

		// 获取默认地区
		getDefaultLocation() {
			return {
				ip: '',
				country: '中国',
				province: '上海',
				city: '上海',
				district: '',
				isp: '',
				fullLocation: '上海',
				accuracy: 'low'
			}
		},

		// IP获取重试方法
		async getIPLocationWithRetry() {
			const apis = [
				{
					name: 'ip-api',
					getIP: () => this.getUserIP(),
					getLocation: (ip) => this.getLocationFromAPI(ip)
				},
				{
					name: 'pconline',
					getLocation: () => this.getLocationFromPconline()
				},
				{
					name: 'ipify',
					getIP: () => this.getIPFromIpify(),
					getLocation: (ip) => this.getLocationFromAPI(ip)
				}
			]

			for (let i = 0; i < apis.length; i++) {
				try {
					console.log(`尝试API ${i + 1}: ${apis[i].name}`)

					if (apis[i].getIP) {
						// 需要先获取IP再获取地区
						const ip = await apis[i].getIP()
						if (ip) {
							const location = await apis[i].getLocation(ip)
							if (location && location.fullLocation) {
								console.log(`API ${i + 1} 成功:`, location.fullLocation)
								return location
							}
						}
					} else {
						// 直接获取地区信息
						const location = await apis[i].getLocation()
						if (location && location.fullLocation) {
							console.log(`API ${i + 1} 成功:`, location.fullLocation)
							return location
						}
					}
				} catch (error) {
					console.log(`API ${i + 1} 失败:`, error.message)
					continue
				}
			}

			return this.getDefaultLocation()
		},

		// 从pconline获取地区信息
		async getLocationFromPconline() {
			return new Promise((resolve, reject) => {
				uni.request({
					url: 'https://whois.pconline.com.cn/ipJson.jsp',
					data: {
						json: true
					},
					method: 'GET',
					timeout: 5000,
					success: (res) => {
						console.log('pconline API响应:', res.data)
						if (res.data && res.data.pro) {
							const location = {
								ip: res.data.ip || '',
								country: '中国',
								province: res.data.pro || '',
								city: res.data.city || '',
								fullLocation: this.formatChineseLocation(res.data.pro, res.data.city),
								accuracy: 'high',
								timestamp: Date.now()
							}
							resolve(location)
						} else {
							reject(new Error('pconline API返回无效数据'))
						}
					},
					fail: (error) => {
						reject(new Error(`pconline API请求失败: ${error.errMsg || 'unknown'}`))
					}
				})
			})
		},

		// 从ipify获取IP
		async getIPFromIpify() {
			return new Promise((resolve, reject) => {
				uni.request({
					url: 'https://api.ipify.org?format=json',
					method: 'GET',
					timeout: 3000,
					success: (res) => {
						if (res.data && res.data.ip) {
							resolve(res.data.ip)
						} else {
							reject(new Error('ipify返回无效数据'))
						}
					},
					fail: (error) => {
						reject(new Error(`ipify请求失败: ${error.errMsg || 'unknown'}`))
					}
				})
			})
		},

		// 缓存地区信息
		cacheLocationInfo(locationData) {
			try {
				const cacheData = {
					...locationData,
					timestamp: Date.now(),
					expireTime: Date.now() + (24 * 60 * 60 * 1000) // 24小时过期
				}
				uni.setStorageSync('ipLocationCache', cacheData)
			} catch (error) {
				console.error('缓存IP地区信息失败:', error)
			}
		},

		// 获取缓存的地区信息
		getCachedLocationInfo() {
			try {
				const cacheData = uni.getStorageSync('ipLocationCache')
				if (cacheData && cacheData.expireTime > Date.now()) {
					return cacheData
				}
				return null
			} catch (error) {
				console.error('获取缓存IP地区信息失败:', error)
				return null
			}
		},


		
		getWelcomeText() {
			return '趣嗒同行'
		},

		getSubtitleText() {
			return '探索有趣的生活'
		},
		
		getSubmitText() {
			if (this.authStep === 'mobile') {
				return '下一步'
			} else if (this.authStep === 'login') {
				return '登录'
			} else {
				return '注册'
			}
		},

		// 输入框焦点处理
		onInputFocus(field) {
			this.focusedField = field
		},

		onInputBlur(field) {
			this.focusedField = ''
		},

		// 手机号输入处理
		onMobileInput() {
			// 清除之前的状态
			if (this.authStep !== 'mobile') {
				this.authStep = 'mobile'
				this.authForm.password = ''
				this.authForm.username = ''
				this.authForm.code = ''
				this.authForm.agreement = false
			}
		},

		// 手机号失焦检测
		async onMobileBlur() {
			this.focusedField = ''
			if (this.authForm.mobile.length === 11) {
				await this.checkUserExists()
			}
		},

		// 检测用户是否存在
		async checkUserExists() {
			try {
				// 验证手机号格式
				if (!this.validateMobile(this.authForm.mobile)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}

				uni.showLoading({
					title: '检测中...'
				})

				console.log('开始调用云函数，手机号:', this.authForm.mobile)

				// 先测试简单云函数
				try {
					const simpleTest = await uniCloud.callFunction({
						name: 'simple-test',
						data: { test: 'connection' }
					})
					console.log('简单云函数测试成功:', simpleTest)
				} catch (simpleError) {
					console.error('简单云函数测试失败:', simpleError)
				}

				// 调用云函数检测用户是否存在
				const result = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'checkUserExists',
						mobile: this.authForm.mobile
					}
				})

				uni.hideLoading()

				console.log('检测用户存在性结果:', result)

				if (result.result.code === 0) {
					this.userExists = result.result.data.exists
					if (this.userExists) {
						this.authStep = 'login'
						this.showTip = true
						this.tipText = '该手机号已注册，请输入密码登录'
					} else {
						this.authStep = 'register'
						this.showTip = true
						this.tipText = '该手机号未注册，请完善注册信息'
					}

					// 3秒后隐藏提示
					setTimeout(() => {
						this.showTip = false
					}, 3000)
				} else {
					uni.showToast({
						title: result.result.message || '检测失败，请重试',
						icon: 'none'
					})
				}

			} catch (error) {
				uni.hideLoading()
				console.error('检测用户失败:', error)

				// 详细错误分析
				let errorMessage = '网络连接失败'
				if (error.errCode) {
					switch (error.errCode) {
						case 'FUNCTION_NOT_FOUND':
							errorMessage = '云函数未找到，请检查部署状态'
							break
						case 'FUNCTION_EXECUTION_FAIL':
							errorMessage = '云函数执行失败'
							break
						case 'NETWORK_ERROR':
							errorMessage = '网络连接超时'
							break
						case 'INVALID_PARAM':
							errorMessage = '参数错误'
							break
						default:
							errorMessage = `连接失败: ${error.errCode}`
					}
				} else if (error.message) {
					errorMessage = error.message
				}

				// 云函数调用失败，使用本地存储作为备用方案
				console.log('云函数调用失败，使用本地存储检测')
				try {
					const existingUsers = uni.getStorageSync('registeredUsers') || []
					const userExists = existingUsers.some(user => user.mobile === this.authForm.mobile)

					this.userExists = userExists
					if (userExists) {
						this.authStep = 'login'
						this.showTip = true
						this.tipText = '该手机号已注册，请输入密码登录'
					} else {
						this.authStep = 'register'
						this.showTip = true
						this.tipText = '该手机号未注册，请完善注册信息'
					}

					// 3秒后隐藏提示
					setTimeout(() => {
						this.showTip = false
					}, 3000)

					uni.showToast({
						title: errorMessage,
						icon: 'none',
						duration: 3000
					})
				} catch (localError) {
					console.error('本地存储也失败:', localError)
					uni.showToast({
						title: '检测失败，请重试',
						icon: 'none'
					})
				}
			}
		},

		// 获取验证码
		async getVerificationCode() {
			if (this.codeCountdown > 0) return

			if (!this.authForm.mobile || this.authForm.mobile.length !== 11) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				})
				return
			}

			try {
				uni.showLoading({
					title: '发送中...'
				})

				// 调用云函数获取验证码
				const result = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'getVerificationCode',
						mobile: this.authForm.mobile,
						type: this.authStep === 'login' ? 'login' : 'register'
					}
				})

				uni.hideLoading()

				console.log('获取验证码结果:', result)

				if (result.result.code === 0) {
					// 保存验证码用于验证（开发阶段）
					this.generatedCode = result.result.data.code

					// 显示验证码弹窗
					this.showCodeModal = true

					// 开始倒计时
					this.startCountdown()
				} else {
					uni.showToast({
						title: result.result.message || '获取验证码失败',
						icon: 'none'
					})
				}

			} catch (error) {
				uni.hideLoading()
				console.error('获取验证码失败:', error)
				uni.showToast({
					title: '获取验证码失败，请重试',
					icon: 'none'
				})
			}
		},

		// 开始倒计时
		startCountdown() {
			this.codeCountdown = 60
			this.codeTimer = setInterval(() => {
				this.codeCountdown--
				if (this.codeCountdown <= 0) {
					clearInterval(this.codeTimer)
					this.codeTimer = null
				}
			}, 1000)
		},

		// 自动填入验证码
		autoFillCode() {
			this.authForm.code = this.generatedCode
			this.closeCodeModal()
			uni.showToast({
				title: '验证码已自动填入',
				icon: 'success'
			})
		},

		// 关闭验证码弹窗
		closeCodeModal() {
			this.showCodeModal = false
		},

		// 切换密码显示
		togglePassword() {
			this.showPassword = !this.showPassword
		},

		// 切换协议同意
		toggleAgreement() {
			this.authForm.agreement = !this.authForm.agreement
		},

		// 主要提交处理
		async handleSubmit() {
			if (!this.canSubmit) return

			if (this.authStep === 'mobile') {
				await this.checkUserExists()
			} else if (this.authStep === 'login') {
				await this.doLogin()
			} else if (this.authStep === 'register') {
				await this.doRegister()
			}
		},

		// 执行登录
		async doLogin() {
			try {
				uni.showLoading({
					title: '登录中...'
				})

				// 调用云函数进行登录
				const result = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'login',
						mobile: this.authForm.mobile,
						password: this.authForm.password
					}
				})

				uni.hideLoading()

				console.log('登录结果:', result)

				if (result.result.code === 0) {
					console.log('🎉 登录成功，准备保存用户信息');
					console.log('📋 完整返回数据:', result.result.data);
					console.log('📋 返回的用户信息:', result.result.data.userInfo);
					console.log('🔑 返回的token:', result.result.data.token);

					// 智能处理不同的数据结构
					let userInfo, token;

					if (result.result.data.userInfo && result.result.data.token) {
						// 标准结构：data.userInfo 和 data.token
						userInfo = result.result.data.userInfo;
						token = result.result.data.token;
						console.log('✅ 使用标准数据结构');
					} else if (result.result.data._id) {
						// 直接结构：data 就是用户信息，token 在 result.result.token
						userInfo = result.result.data;
						token = result.result.token || result.result.data.token || 'temp_token_' + result.result.data._id;
						console.log('✅ 使用直接数据结构');
					} else {
						console.error('❌ 无法识别的数据结构!');
						uni.showToast({
							title: '登录数据异常',
							icon: 'none'
						})
						return
					}

					// 保存用户信息到本地
					uni.setStorageSync('userInfo', userInfo)
					uni.setStorageSync('isLoggedIn', true)
					uni.setStorageSync('token', token)

					// 验证保存是否成功
					const savedUserInfo = uni.getStorageSync('userInfo');
					console.log('✅ 验证保存的用户信息:', savedUserInfo);

					uni.showToast({
						title: '登录成功',
						icon: 'success'
					})

					// 登录成功后跳转到我的页面
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/profile/profile'
						})
					}, 1500)
				} else {
					uni.showToast({
						title: result.result.message || '登录失败',
						icon: 'none'
					})
				}
			} catch (error) {
				uni.hideLoading()
				console.error('登录失败:', error)

				// 详细错误分析
				let errorMessage = '登录失败'
				if (error.errCode) {
					switch (error.errCode) {
						case 'FUNCTION_NOT_FOUND':
							errorMessage = '登录服务未找到'
							break
						case 'FUNCTION_EXECUTION_FAIL':
							errorMessage = '登录服务异常'
							break
						case 'NETWORK_ERROR':
							errorMessage = '网络连接超时'
							break
						default:
							errorMessage = `登录失败: ${error.errCode}`
					}
				} else if (error.message) {
					errorMessage = error.message
				}

				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			}
		},

		// 执行注册
		async doRegister() {
			try {
				// 验证手机号格式
				if (!this.validateMobile(this.authForm.mobile)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}

				uni.showLoading({
					title: '注册中...'
				})

				// 调用云函数进行注册
				const result = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'register',
						username: this.authForm.username,
						mobile: this.authForm.mobile,
						code: this.authForm.code,
						password: this.authForm.password
					}
				})

				uni.hideLoading()

				console.log('注册结果:', result)

				if (result.result.code === 0) {
					const userInfo = result.result.data.userInfo
					const token = result.result.data.token

					console.log('🎉 注册成功，准备保存用户信息');
					console.log('📋 返回的用户信息:', userInfo);
					console.log('🔑 返回的token:', token);

					// 保存用户信息到本地
					uni.setStorageSync('userInfo', userInfo)
					uni.setStorageSync('isLoggedIn', true)
					uni.setStorageSync('token', token)

					// 验证保存是否成功
					const savedUserInfo = uni.getStorageSync('userInfo');
					console.log('✅ 验证保存的用户信息:', savedUserInfo);

					uni.showToast({
						title: '注册成功',
						icon: 'success'
					})

					// 注册成功后跳转到完善资料页面
					setTimeout(() => {
						uni.navigateTo({
							url: `/pages/profile-setup/profile-setup?userInfo=${encodeURIComponent(JSON.stringify(userInfo))}`
						})
					}, 1500)
				} else {
					uni.showToast({
						title: result.result.message || '注册失败',
						icon: 'none'
					})
				}


			} catch (error) {
				uni.hideLoading()
				console.error('注册失败:', error)

				// 详细错误分析
				let errorMessage = '注册失败'
				if (error.errCode) {
					switch (error.errCode) {
						case 'FUNCTION_NOT_FOUND':
							errorMessage = '注册服务未找到'
							break
						case 'FUNCTION_EXECUTION_FAIL':
							errorMessage = '注册服务异常'
							break
						case 'NETWORK_ERROR':
							errorMessage = '网络连接超时'
							break
						default:
							errorMessage = `注册失败: ${error.errCode}`
					}
				} else if (error.message) {
					errorMessage = error.message
				}

				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			}
		},

		// 忘记密码
		forgotPassword() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			})
		},



		// 显示用户协议
		showUserAgreement() {
			uni.navigateTo({
				url: '/pages/user-agreement/user-agreement'
			})
		},

		// 显示隐私政策
		showPrivacyPolicy() {
			uni.navigateTo({
				url: '/pages/privacy-policy/privacy-policy'
			})
		},

		// Logo错误处理
		onLogoError() {
			this.logoError = true
		},

		// 返回
		goBack() {
			// 检查是否有上一页，如果没有则跳转到我的页面
			const pages = getCurrentPages()
			if (pages.length > 1) {
				uni.navigateBack()
			} else {
				uni.switchTab({
					url: '/pages/profile/profile'
				})
			}
		},




	}
}
</script>

<style lang="scss" scoped>
@import '@/styles/global.scss';

/* 强制消除所有可能的边距和留白 */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

page, body, html, uni-page-body, uni-page, uni-page-wrapper {
	margin: 0 !important;
	padding: 0 !important;
	width: 100% !important;
	height: 100% !important;
	overflow: hidden !important;
	border: none !important;
	outline: none !important;
}

/* 专门针对可能的容器元素 */
.uni-page, .uni-page-body, .uni-page-wrapper {
	margin: 0 !important;
	padding: 0 !important;
	border: none !important;
}

.auth-page {
	position: fixed;
	top: -10px;
	left: -10px;
	right: -10px;
	bottom: -10px;
	width: calc(100vw + 20px);
	height: calc(100vh + 20px);
	background: linear-gradient(to bottom,
		transparent 0%,
		transparent 75%,
		#e8f4fd 75%,
		#e8f4fd 100%
	), url('/static/loging.png');
	background-size: cover, cover;
	background-position: center top, center top;
	background-repeat: no-repeat, no-repeat;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	margin: 0;
	padding: 10px;
	border: none;
	outline: none;
	z-index: 1;
}

.status-bar {
	background: transparent;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	z-index: 10;
	height: 0;
	margin: 0;
	padding: 0;
}

.nav-bar {
	position: fixed;
	top: var(--status-bar-height);
	left: 0;
	right: 0;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	padding: 0 24rpx;
	z-index: 100;
	background: transparent;
}

.back-button {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.3);
	border-radius: 50%;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
}

.back-button:active {
	transform: scale(0.95);
	background: rgba(0, 0, 0, 0.5);
}

.back-icon {
	width: 32rpx;
	height: 32rpx;
	filter: brightness(0) invert(1);
}



.auth-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	position: relative;
}

.auth-header {
	text-align: center;
	margin-bottom: 80rpx;
}

.logo-section {
	margin-bottom: 48rpx;
}

.logo-container {
	width: 200rpx;
	height: 200rpx;
	margin: 0 auto 32rpx;
	border-radius: 40rpx;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(20rpx);
	display: flex;
	align-items: center;
	justify-content: center;
	border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.app-logo {
	width: 160rpx;
	height: 160rpx;
	border-radius: 32rpx;
}

.logo-fallback {
	width: 160rpx;
	height: 160rpx;
	border-radius: 32rpx;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.logo-text {
	font-size: 48rpx;
	font-weight: bold;
	color: #ffffff;
}

.app-name {
	font-size: 48rpx;
	font-weight: bold;
	color: #ffffff;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.welcome-text {
	font-size: 32rpx;
	color: #ffffff;
	margin-bottom: 16rpx;
	display: block;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.subtitle {
	font-size: 28rpx;
	color: #ffffff;
	display: block;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.auth-tip {
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(20rpx);
	border-radius: 24rpx;
	padding: 24rpx 32rpx;
	margin-bottom: 48rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.tip-text {
	font-size: 28rpx;
	color: #ffffff;
	text-align: center;
	display: block;
}

/* 底部登录区域 */
.bottom-login-area {
	position: absolute;
	bottom: 60rpx;
	left: 60rpx;
	right: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 主要登录按钮区域 */
.main-login-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
}

.main-login-btn {
	width: 100%;
	height: 120rpx;
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 24rpx;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
	margin: 0;
	padding: 0;
}

.main-login-btn:active {
	transform: scale(0.98);
}

.main-login-btn.disabled {
	opacity: 0.5;
	pointer-events: none;
}

.wechat-login-btn {
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 100%);
	box-shadow: 0 8rpx 24rpx rgba(102, 212, 200, 0.4);
	margin-bottom: 40rpx !important;
}

.phone-login-btn {
	background: rgba(102, 212, 200, 0.2);
	border: 2rpx solid rgba(102, 212, 200, 0.4);
	backdrop-filter: blur(20rpx);
	box-shadow: 0 4rpx 16rpx rgba(102, 212, 200, 0.2);
	margin-top: 0 !important;
}

.main-login-icon {
	width: 48rpx;
	height: 48rpx;
}

.main-login-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

.phone-text {
	color: #66D4C8 !important;
}

.main-agreement-section {
	margin-top: 48rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
}

.main-agreement-row {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
}

.main-agreement-checkbox {
	display: flex;
	align-items: center;
	gap: 12rpx;
	flex-wrap: nowrap;
	white-space: nowrap;
}

.main-agreement-links {
	display: flex;
	align-items: center;
	justify-content: center;
	white-space: nowrap;
}

.main-checkbox {
	width: 36rpx;
	height: 36rpx;
	border: 3rpx solid #333333;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.9);
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
}

.main-checkbox.checked {
	background: #66D4C8;
	border-color: #66D4C8;
}

.main-checkbox-icon {
	color: #ffffff;
	font-size: 24rpx;
	font-weight: bold;
}

.main-agreement-text {
	color: #000000;
	font-size: 28rpx;
	white-space: nowrap;
}



.main-agreement-link {
	color: #66D4C8;
	font-size: 28rpx;
	text-decoration: none;
	font-weight: 500;
}

/* 详细表单弹窗 */
.detail-form-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 60rpx;
}

.detail-form {
	width: 100%;
	max-width: 600rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 32rpx;
	padding: 60rpx 48rpx;
	backdrop-filter: blur(20rpx);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	max-height: 80vh;
	overflow-y: auto;
}

.detail-form-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 48rpx;
}

.detail-form-title {
	font-size: 40rpx;
	font-weight: 600;
	color: #333333;
}

.detail-form-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(0, 0, 0, 0.1);
}

.close-icon {
	font-size: 36rpx;
	color: #666666;
}

.detail-tip {
	background: rgba(138, 43, 226, 0.1);
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 32rpx;
	border-left: 6rpx solid rgba(138, 43, 226, 0.8);
}

.detail-tip-text {
	color: rgba(138, 43, 226, 0.8);
	font-size: 28rpx;
	line-height: 1.5;
}

.detail-form-item {
	margin-bottom: 32rpx;
}

.detail-input-container {
	position: relative;
	background: rgba(255, 255, 255, 0.8);
	border: 2rpx solid rgba(0, 0, 0, 0.1);
	border-radius: 16rpx;
	padding: 24rpx 20rpx;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
}

.detail-input-container.focused {
	border-color: rgba(138, 43, 226, 0.8);
	background: rgba(255, 255, 255, 0.95);
}

.detail-input-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 20rpx;
	opacity: 0.6;
}

.detail-input-wrapper {
	flex: 1;
	position: relative;
}

.detail-floating-label {
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	color: #999999;
	font-size: 32rpx;
	transition: all 0.3s ease;
	pointer-events: none;
}

.detail-floating-label.active {
	top: -12rpx;
	font-size: 24rpx;
	color: rgba(138, 43, 226, 0.8);
	background: rgba(255, 255, 255, 0.9);
	padding: 0 8rpx;
}

.detail-form-input {
	width: 100%;
	border: none;
	outline: none;
	background: transparent;
	font-size: 32rpx;
	color: #333333;
	padding-top: 8rpx;
}

.detail-password-toggle {
	width: 40rpx;
	height: 40rpx;
	margin-left: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.detail-toggle-icon {
	width: 36rpx;
	height: 36rpx;
	opacity: 0.6;
}

.detail-code-btn {
	margin-left: 20rpx;
	padding: 16rpx 24rpx;
	background: rgba(138, 43, 226, 0.8);
	border-radius: 12rpx;
	transition: all 0.3s ease;
}

.detail-code-btn.disabled {
	background: rgba(0, 0, 0, 0.2);
}

.detail-code-text {
	color: #ffffff;
	font-size: 24rpx;
	white-space: nowrap;
}

.detail-forgot-password {
	text-align: right;
	margin-bottom: 32rpx;
}

.detail-forgot-text {
	color: rgba(138, 43, 226, 0.8);
	font-size: 28rpx;
}

.detail-submit-section {
	margin-top: 48rpx;
}

.detail-submit-btn {
	width: 100%;
	height: 96rpx;
	background: linear-gradient(135deg, rgba(138, 43, 226, 0.8) 0%, rgba(138, 43, 226, 0.6) 100%);
	border-radius: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.detail-submit-btn.disabled {
	background: rgba(0, 0, 0, 0.2);
}

.detail-submit-btn:active {
	transform: scale(0.98);
}

.detail-submit-text {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 600;
}

/* 旧样式 - 已注释
.form-item {
	margin-bottom: 32rpx;
}

.input-container {
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(20rpx);
	border-radius: 24rpx;
	padding: 32rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.1);
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
	position: relative;
}

.input-container.focused {
	border-color: rgba(255, 255, 255, 0.4);
	background: rgba(255, 255, 255, 0.2);
}

.input-icon {
	width: 48rpx;
	height: 48rpx;
	margin-right: 24rpx;
	opacity: 0.8;
}

.input-wrapper {
	flex: 1;
	position: relative;
}

.floating-label {
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	font-size: 32rpx;
	color: rgba(255, 255, 255, 0.7);
	transition: all 0.3s ease;
	pointer-events: none;
}

.floating-label.active {
	top: -12rpx;
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.9);
}

.form-input {
	width: 100%;
	height: 48rpx;
	font-size: 32rpx;
	color: #ffffff;
	background: transparent;
	border: none;
	outline: none;
}

.form-input::placeholder {
	color: rgba(255, 255, 255, 0.5);
}

.code-input {
	padding-right: 160rpx;
}

.code-btn {
	position: absolute;
	right: 32rpx;
	top: 50%;
	transform: translateY(-50%);
	background: rgba(255, 255, 255, 0.2);
	border-radius: 16rpx;
	padding: 16rpx 24rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.code-btn.disabled {
	opacity: 0.5;
}

.code-text {
	font-size: 24rpx;
	color: #ffffff;
}

.password-toggle {
	margin-left: 24rpx;
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.toggle-icon {
	width: 32rpx;
	height: 32rpx;
	opacity: 0.7;
}

.agreement-section {
	margin: 48rpx 0;
}

.agreement-item {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
}

.checkbox {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.5);
	border-radius: 6rpx;
	margin-right: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: transparent;
}

.checkbox.checked {
	background: #ffffff;
	border-color: #ffffff;
}

.check-icon {
	width: 20rpx;
	height: 20rpx;
}

.agreement-text {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
}

.agreement-link {
	font-size: 28rpx;
	color: #ffffff;
	text-decoration: underline;
	margin: 0 8rpx;
}

.forgot-password {
	text-align: right;
	margin: 32rpx 0;
}

.forgot-text {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
}

.submit-section {
	margin: 64rpx 0 48rpx;
}

.submit-btn {
	background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.9) 100%);
	border-radius: 24rpx;
	height: 96rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.submit-btn.disabled {
	opacity: 0.5;
}

.submit-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #667eea;
}

.third-party-section {
	margin-top: auto;
	padding-top: 48rpx;
}

.divider-section {
	display: flex;
	align-items: center;
	margin-bottom: 48rpx;
}

.divider-line {
	flex: 1;
	height: 1rpx;
	background: rgba(255, 255, 255, 0.3);
}
旧样式结束 */

.divider-text {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.6);
	margin: 0 32rpx;
}

.third-party-buttons {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	box-sizing: border-box;
}

.third-party-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	flex: 0 0 auto;
}

.third-party-spacer {
	width: 160rpx;
	height: 1rpx;
}

.third-party-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(20rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.third-party-text {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
}

/* 验证码弹窗样式 */
.code-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.code-modal {
	width: 600rpx;
	background: #ffffff;
	border-radius: 32rpx;
	position: relative;
	overflow: hidden;
	box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
}

.modal-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	overflow: hidden;
}

.decoration-circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
}

.circle-1 {
	width: 80rpx;
	height: 80rpx;
	top: -20rpx;
	left: 40rpx;
}

.circle-2 {
	width: 120rpx;
	height: 120rpx;
	top: -40rpx;
	right: 80rpx;
}

.circle-3 {
	width: 60rpx;
	height: 60rpx;
	top: 60rpx;
	right: 200rpx;
}

.modal-content {
	padding: 160rpx 48rpx 48rpx;
	text-align: center;
}

.modal-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 16rpx;
	display: block;
}

.modal-subtitle {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 32rpx;
	display: block;
}

.code-display {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 16rpx;
	padding: 32rpx;
	margin: 32rpx 0;
}

.code-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #ffffff;
	letter-spacing: 8rpx;
}

.modal-tip {
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 48rpx;
	display: block;
}

.modal-buttons {
	display: flex;
	gap: 24rpx;
}

.modal-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.auto-fill {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.close-btn {
	background: #f5f5f5;
}

.auto-fill .btn-text {
	color: #ffffff;
	font-weight: bold;
}

.close-btn .btn-text {
	color: #666666;
}

.btn-text {
	font-size: 28rpx;
}


</style>
