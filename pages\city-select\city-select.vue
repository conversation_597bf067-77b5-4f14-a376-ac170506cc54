<template>
	<view class="city-select-page">
		<!-- Fixed Nav Bar Area (with gradient) -->
		<view class="nav-bar-area" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="nav-content">
				<uni-icons type="back" size="24" color="#333" @click="goBack"></uni-icons>
				<text class="page-title">城市选择</text>
				<uni-icons type="location-filled" size="24" color="#6F7BF5" @click="locateCity"></uni-icons>
			</view>
		</view>

		<!-- Fixed Search Bar Area (below nav bar, no gradient) -->
		<view class="search-bar-area">
			<view class="search-box">
				<uni-icons type="search" size="18" color="#999"></uni-icons>
				<input type="text" v-model="searchKeyword" placeholder="北京/bj/beijing" @input="onSearchInput" class="search-input" />
			</view>
		</view>

		<!-- Main Content -->
		<scroll-view
			scroll-y
			class="city-list-scroll"
		>
			<!-- <view class="current-city-section section-group" v-if="currentCity">
				<view class="section-title">当前城市</view>
				<view class="city-grid">
					<view class="city-item" @click="selectCity(currentCity)">{{ currentCity }}</view>
				</view>
			</view> -->

			<view class="hot-cities-section section-group">
				<view class="section-title">热门城市</view>
				<view class="city-grid">
					<view class="city-item" v-for="city in filteredHotCities" :key="city" @click="selectCity(city)">
						{{ city }}
					</view>
				</view>
			</view>

			<view class="city-list-by-initial" v-for="(cities, initial) in filteredCityList" :key="initial">
				<view class="section-title">{{ initial }}</view>
				<view class="city-list">
					<view class="city-item-full" v-for="city in cities" :key="city" @click="selectCity(city)">
						{{ city }}
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { cityData } from '@/common/cityData.js';

export default {
	data() {
		return {
			statusBarHeight: 0,
			searchKeyword: '',
			currentCity: '北海',
			hotCities: ['北京', '上海', '广州', '深圳', '成都', '重庆', '武汉', '杭州', '西安', '北海'],
			cityList: {},
			fromPage: '' // 记录来源页面
		};
	},
	computed: {
		filteredHotCities() {
			const keyword = this.searchKeyword.toLowerCase();
			if (!keyword) {
				return this.hotCities;
			}
			return this.hotCities.filter(city => city.toLowerCase().includes(keyword));
		},
		filteredCityList() {
			const keyword = this.searchKeyword.toLowerCase();
			if (!keyword) {
				return this.cityList;
			}

			const filtered = {};
			for (const initial in this.cityList) {
				const cities = this.cityList[initial].filter(city => {
					return city.toLowerCase().includes(keyword);
				});
				if (cities.length > 0) {
					filtered[initial] = cities;
				}
			}
			return filtered;
		}
	},
	onLoad(options) {
		this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
		this.fromPage = options.from || ''; // 获取来源页面参数
		console.log('城市选择页面来源:', this.fromPage);
		this.processCityData(cityData);
	},

	methods: {

		processCityData(data) {
			console.log("processCityData: 接收到的原始数据", data);
			const cityList = {};

			data.forEach(city => {
				if (city && typeof city.initial === 'string' && typeof city.name === 'string') {
					const initial = city.initial.toUpperCase();
					if (!cityList[initial]) {
						cityList[initial] = [];
					}
					cityList[initial].push(city.name);
				} else {
					console.warn("processCityData: 无效的城市数据项", city);
				}
			});

			for (const initial in cityList) {
				cityList[initial].sort();
			}
			this.cityList = cityList;
			console.log("processCityData: 处理后的城市列表", this.cityList);
		},
		goBack() {
			console.log("goBack method called, fromPage:", this.fromPage);
			if (this.fromPage === 'profile-setup') {
				// 从完善资料页面来的，返回完善资料页面
				uni.navigateBack();
			} else {
				// 从首页来的，返回首页
				uni.reLaunch({
					url: '/pages/index/index'
				});
			}
		},
		locateCity() {
			uni.showToast({
				title: '定位功能待实现',
				icon: 'none'
			});
		},
		onSearchInput(e) {
			this.searchKeyword = e.detail.value;
			console.log("搜索关键字: ", this.searchKeyword);
		},
		selectCity(city) {
			uni.setStorageSync('selectedCity', city);
			uni.showToast({
				title: `已选择: ${city}`,
				icon: 'success'
			});
			setTimeout(() => {
				this.goBack();
			}, 500);
		},

	}
};
</script>

<style lang="scss">
$status-bar-height: var(--status-bar-height, 0px);
$nav-bar-height: 88rpx;
$search-bar-height: 100rpx;
$total-fixed-header-height: calc($status-bar-height + $nav-bar-height + $search-bar-height);

.city-select-page {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #FFFFFF;
}

.nav-bar-area {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: calc($status-bar-height + $nav-bar-height);
	background-color: #FFFFFF;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
	z-index: 1000;
	padding-top: $status-bar-height;
	box-sizing: border-box;

	.nav-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: $nav-bar-height;
		padding: 0 30rpx;
		color: #333;
		font-size: 34rpx;
		font-weight: bold;

		.page-title {
			flex-grow: 1;
			text-align: center;
			margin-left: -24rpx;
		}
	}
}

.search-bar-area {
	position: fixed;
	top: calc($status-bar-height + $nav-bar-height);
	left: 0;
	width: 100%;
	height: $search-bar-height;
	background-color: #FFFFFF;
	padding: 10rpx 30rpx;
	box-sizing: border-box;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	z-index: 999;

	.search-box {
		display: flex;
		align-items: center;
		background-color: #f2f2f2;
		border-radius: 40rpx;
		padding: 15rpx 30rpx;

		.uni-icons {
			margin-right: 15rpx;
			color: #999;
		}

		.search-input {
			flex-grow: 1;
			font-size: 28rpx;
			color: #333;
			&::placeholder {
				color: #bbb;
			}
		}
	}
}

.city-list-scroll {
	flex: 1;
	margin-top: $total-fixed-header-height;
	padding: 30rpx;
	box-sizing: border-box;
	position: relative;
	background-color: #FFFFFF;
}

.section-group {
	margin-bottom: 30rpx;
	background-color: #FFFFFF;
	border-radius: 20rpx;
	padding: 0 30rpx 20rpx;
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);

	&.current-city-section {
		margin-top: 0;
	}

	.section-title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333;
		padding: 0 0 20rpx 0;
		background-color: #FFFFFF;
		margin: 0 -30rpx;
		padding-left: 30rpx;
		padding-right: 30rpx;
	}

	.city-grid {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		padding-top: 10rpx;

		.city-item {
			width: calc(33.33% - 14rpx);
			background-color: #FFFFFF;
			border-radius: 15rpx;
			padding: 20rpx 0;
			text-align: center;
			font-size: 30rpx;
			color: #333;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
			border: none;
			transition: all 0.2s ease;

			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
			}
		}
	}
}

.city-list-by-initial {
	margin-bottom: 30rpx;

	.section-title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333;
		padding: 20rpx 30rpx;
		background-color: #f8f8f8;
		margin: 0;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
	}

	.city-list {
		padding: 0 30rpx;
	}
}

.city-item-full {
	padding: 30rpx;
	font-size: 30rpx;
	color: #333;
	background-color: #FFFFFF;
	border-radius: 15rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	transition: all 0.2s ease;

	&:last-child {
		margin-bottom: 0;
	}

	&:active {
		background-color: #f2f2f2;
		transform: translateY(2rpx);
		box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
	}
}


</style>