<template>
	<view class="messages-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<!-- 顶部导航 -->
		<view class="top-header">
			<view class="header-left">
				<text class="page-title">消息</text>
			</view>
			<view class="header-right">
				<view class="action-btn" @click="goToSearch">
					<uni-icons type="search" size="20" color="#333"></uni-icons>
				</view>
				<view class="action-btn" @click="showMoreMenu">
					<uni-icons type="more-filled" size="20" color="#333"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 快捷功能区 -->
		<view class="quick-actions">
			<view class="action-item" @click="goToSystemMessages">
				<view class="action-icon system">
					<uni-icons type="notification" size="24" color="#fff"></uni-icons>
				</view>
				<text class="action-text">系统消息</text>
				<view v-if="systemMessageCount > 0" class="badge">{{ systemMessageCount }}</view>
			</view>
			<view class="action-item" @click="goToLikeMessages">
				<view class="action-icon like">
					<uni-icons type="heart" size="24" color="#fff"></uni-icons>
				</view>
				<text class="action-text">赞和收藏</text>
				<view v-if="likeMessageCount > 0" class="badge">{{ likeMessageCount }}</view>
			</view>
			<view class="action-item" @click="goToCommentMessages">
				<view class="action-icon comment">
					<uni-icons type="chat" size="24" color="#fff"></uni-icons>
				</view>
				<text class="action-text">评论回复</text>
				<view v-if="commentMessageCount > 0" class="badge">{{ commentMessageCount }}</view>
			</view>
			<view class="action-item" @click="goToFollowMessages">
				<view class="action-icon follow">
					<uni-icons type="person-add" size="24" color="#fff"></uni-icons>
				</view>
				<text class="action-text">新增关注</text>
				<view v-if="followMessageCount > 0" class="badge">{{ followMessageCount }}</view>
			</view>
		</view>

		<!-- 消息列表 -->
		<view class="messages-list">
			<view class="list-header">
				<text class="list-title">聊天</text>
				<view class="filter-btn" @click="showFilterMenu">
					<uni-icons type="funnel" size="16" color="#999"></uni-icons>
				</view>
			</view>

			<!-- 消息项 -->
			<view class="message-items">
				<view
					v-for="(message, index) in messageList"
					:key="message.id"
					class="message-item"
					@click="openChat(message)"
				>
					<view class="avatar-container">
						<image class="user-avatar" :src="message.avatar" mode="aspectFill"></image>
						<view v-if="message.isOnline" class="online-dot"></view>
					</view>
					<view class="message-content">
						<view class="message-header">
							<text class="user-name">{{ message.name }}</text>
							<text class="message-time">{{ formatTime(message.time) }}</text>
						</view>
						<view class="message-preview">
							<text class="preview-text" :class="{ unread: message.unreadCount > 0 }">
								{{ message.lastMessage }}
							</text>
							<view v-if="message.unreadCount > 0" class="unread-badge">
								{{ message.unreadCount > 99 ? '99+' : message.unreadCount }}
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-if="messageList.length === 0" class="empty-state">
				<view class="empty-icon">
					<uni-icons type="chat" size="60" color="#ccc"></uni-icons>
				</view>
				<text class="empty-title">暂无消息</text>
				<text class="empty-desc">快去发现有趣的人，开始聊天吧</text>
				<view class="empty-action">
					<button class="discover-btn" @click="goToDiscover">去发现</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,

				// 消息统计
				systemMessageCount: 3,
				likeMessageCount: 12,
				commentMessageCount: 5,
				followMessageCount: 2,

				// 消息列表
				messageList: [
					{
						id: 1,
						name: '户外小达人',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						lastMessage: '周末一起去露营吗？我知道一个很棒的地方',
						time: new Date(Date.now() - 5 * 60 * 1000), // 5分钟前
						unreadCount: 2,
						isOnline: true
					},
					{
						id: 2,
						name: '跑步达人',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						lastMessage: '今晚夜跑约吗？',
						time: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
						unreadCount: 0,
						isOnline: false
					},
					{
						id: 3,
						name: 'BBQ大师',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						lastMessage: '烧烤聚会确定了，明天下午3点',
						time: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
						unreadCount: 1,
						isOnline: true
					},
					{
						id: 4,
						name: '钓鱼老王',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						lastMessage: '今天收获不错，发几张照片给你看看',
						time: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前
						unreadCount: 0,
						isOnline: false
					}
				]
			}
		},

		onLoad() {
			// 获取系统信息
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},

		methods: {
			// 跳转到搜索
			goToSearch() {
				uni.navigateTo({
					url: '/pages/search/search'
				});
			},

			// 显示更多菜单
			showMoreMenu() {
				uni.showActionSheet({
					itemList: ['清空聊天记录', '消息设置'],
					success: (res) => {
						if (res.tapIndex === 0) {
							this.clearMessages();
						} else if (res.tapIndex === 1) {
							this.goToMessageSettings();
						}
					}
				});
			},

			// 跳转到系统消息
			goToSystemMessages() {
				console.log('跳转到系统消息');
			},

			// 跳转到赞和收藏
			goToLikeMessages() {
				console.log('跳转到赞和收藏');
			},

			// 跳转到评论回复
			goToCommentMessages() {
				console.log('跳转到评论回复');
			},

			// 跳转到新增关注
			goToFollowMessages() {
				console.log('跳转到新增关注');
			},

			// 显示筛选菜单
			showFilterMenu() {
				uni.showActionSheet({
					itemList: ['全部消息', '未读消息', '在线好友'],
					success: (res) => {
						console.log('筛选类型:', res.tapIndex);
					}
				});
			},

			// 打开聊天
			openChat(message) {
				console.log('打开聊天:', message.name);
				// 这里可以跳转到聊天页面
			},

			// 跳转到发现页
			goToDiscover() {
				uni.switchTab({
					url: '/pages/discover/discover'
				});
			},

			// 格式化时间
			formatTime(time) {
				const now = new Date();
				const diff = now - time;
				const minutes = Math.floor(diff / (1000 * 60));
				const hours = Math.floor(diff / (1000 * 60 * 60));
				const days = Math.floor(diff / (1000 * 60 * 60 * 24));

				if (minutes < 1) {
					return '刚刚';
				} else if (minutes < 60) {
					return `${minutes}分钟前`;
				} else if (hours < 24) {
					return `${hours}小时前`;
				} else if (days < 7) {
					return `${days}天前`;
				} else {
					return time.toLocaleDateString();
				}
			},

			// 清空聊天记录
			clearMessages() {
				uni.showModal({
					title: '确认清空',
					content: '确定要清空所有聊天记录吗？',
					success: (res) => {
						if (res.confirm) {
							this.messageList = [];
							uni.showToast({
								title: '已清空',
								icon: 'success'
							});
						}
					}
				});
			},

			// 跳转到消息设置
			goToMessageSettings() {
				console.log('跳转到消息设置');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.messages-container {
		background: #f8f9fa;
		min-height: 100vh;
	}

	.status-bar {
		background: #fff;
	}

	// 顶部导航
	.top-header {
		background: #fff;
		padding: 20rpx 24rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);

		.header-left {
			.page-title {
				font-size: 36rpx;
				font-weight: 700;
				color: #333;
			}
		}

		.header-right {
			display: flex;
			align-items: center;
			gap: 16rpx;

			.action-btn {
				width: 44rpx;
				height: 44rpx;
				border-radius: 12rpx;
				background: #f8f9fa;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.2s ease;

				&:active {
					background: #e9ecef;
					transform: scale(0.95);
				}
			}
		}
	}

	// 快捷功能区
	.quick-actions {
		background: #fff;
		margin: 16rpx 24rpx;
		border-radius: 20rpx;
		padding: 32rpx 24rpx;
		display: flex;
		justify-content: space-between;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

		.action-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 12rpx;
			position: relative;
			flex: 1;

			.action-icon {
				width: 64rpx;
				height: 64rpx;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);

				&.system {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				}

				&.like {
					background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
				}

				&.comment {
					background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
				}

				&.follow {
					background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
				}
			}

			.action-text {
				font-size: 24rpx;
				color: #666;
				font-weight: 500;
			}

			.badge {
				position: absolute;
				top: -8rpx;
				right: 20rpx;
				background: #ff4757;
				color: #fff;
				font-size: 20rpx;
				font-weight: 600;
				padding: 4rpx 8rpx;
				border-radius: 12rpx;
				min-width: 24rpx;
				text-align: center;
				box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
			}
		}
	}

	// 消息列表
	.messages-list {
		margin: 16rpx 24rpx 0;

		.list-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 8rpx 20rpx;

			.list-title {
				font-size: 32rpx;
				font-weight: 700;
				color: #333;
			}

			.filter-btn {
				width: 36rpx;
				height: 36rpx;
				border-radius: 8rpx;
				background: #f8f9fa;
				display: flex;
				align-items: center;
				justify-content: center;

				&:active {
					background: #e9ecef;
				}
			}
		}

		.message-items {
			background: #fff;
			border-radius: 20rpx;
			overflow: hidden;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

			.message-item {
				padding: 24rpx;
				display: flex;
				align-items: center;
				gap: 20rpx;
				border-bottom: 1rpx solid #f5f5f5;
				transition: all 0.2s ease;

				&:last-child {
					border-bottom: none;
				}

				&:active {
					background: #f8f9fa;
				}

				.avatar-container {
					position: relative;
					flex-shrink: 0;

					.user-avatar {
						width: 88rpx;
						height: 88rpx;
						border-radius: 20rpx;
					}

					.online-dot {
						position: absolute;
						bottom: 4rpx;
						right: 4rpx;
						width: 20rpx;
						height: 20rpx;
						background: #52c41a;
						border-radius: 50%;
						border: 3rpx solid #fff;
					}
				}

				.message-content {
					flex: 1;
					min-width: 0;

					.message-header {
						display: flex;
						align-items: center;
						justify-content: space-between;
						margin-bottom: 8rpx;

						.user-name {
							font-size: 30rpx;
							font-weight: 600;
							color: #333;
						}

						.message-time {
							font-size: 22rpx;
							color: #999;
							flex-shrink: 0;
						}
					}

					.message-preview {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.preview-text {
							font-size: 26rpx;
							color: #666;
							flex: 1;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;

							&.unread {
								color: #333;
								font-weight: 600;
							}
						}

						.unread-badge {
							background: #ff4757;
							color: #fff;
							font-size: 20rpx;
							font-weight: 600;
							padding: 4rpx 8rpx;
							border-radius: 12rpx;
							min-width: 24rpx;
							text-align: center;
							margin-left: 12rpx;
							flex-shrink: 0;
						}
					}
				}
			}
		}

		// 空状态
		.empty-state {
			background: #fff;
			border-radius: 20rpx;
			padding: 80rpx 40rpx;
			text-align: center;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

			.empty-icon {
				margin-bottom: 24rpx;
			}

			.empty-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
				display: block;
				margin-bottom: 12rpx;
			}

			.empty-desc {
				font-size: 26rpx;
				color: #999;
				display: block;
				margin-bottom: 40rpx;
			}

			.empty-action {
				.discover-btn {
					background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
					color: #333;
					font-size: 28rpx;
					font-weight: 600;
					border: none;
					border-radius: 24rpx;
					padding: 16rpx 40rpx;
					box-shadow: 0 4rpx 16rpx rgba(255, 215, 0, 0.3);

					&:active {
						background: linear-gradient(135deg, #FFC107 0%, #FF9800 100%);
					}
				}
			}
		}
	}
</style>
