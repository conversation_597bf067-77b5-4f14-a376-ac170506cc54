<template>
	<view class="my-works-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<uni-icons type="left" size="24" color="#333"></uni-icons>
					<text class="back-text">返回</text>
				</view>
				<text class="nav-title">我的作品</text>
				<view class="nav-right">
					<view class="search-btn" @tap="searchWorks">
						<uni-icons type="search" size="20" color="#666"></uni-icons>
					</view>
					<view class="add-btn" @tap="createWork">
						<uni-icons type="plus" size="20" color="#666"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 作品统计 -->
		<view class="works-stats">
			<view class="stats-container">
				<view class="stat-item">
					<text class="stat-number">{{ totalWorks }}</text>
					<text class="stat-label">全部作品</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-number">{{ totalViews }}</text>
					<text class="stat-label">总浏览量</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-number">{{ totalLikes }}</text>
					<text class="stat-label">总点赞数</text>
				</view>
			</view>
		</view>

		<!-- 作品分类 -->
		<view class="work-tabs">
			<scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false">
				<view class="tab-item" v-for="(tab, index) in workTabs" :key="index" :class="{ active: currentTab === index }" @tap="switchTab(index)">
					<view class="tab-content">
						<uni-icons v-if="tab.icon" :type="tab.icon" size="16" :color="currentTab === index ? '#fff' : '#666'"></uni-icons>
						<text class="tab-text">{{ tab.name }}</text>
					</view>
					<view class="tab-count" v-if="tab.count > 0">{{ tab.count }}</view>
				</view>
			</scroll-view>
		</view>

		<!-- 作品列表 -->
		<scroll-view class="works-content" scroll-y="true" @scrolltolower="loadMore">
			<!-- 空状态 -->
			<view class="empty-state" v-if="currentWorks.length === 0">
				<view class="empty-icon">
					<uni-icons type="compose" size="80" color="#ddd"></uni-icons>
				</view>
				<text class="empty-title">暂无作品</text>
				<text class="empty-desc">{{ getEmptyDesc() }}</text>
				<view class="empty-action" @tap="createWork">
					<text class="action-text">创作第一个作品</text>
				</view>
			</view>

			<!-- 作品网格 -->
			<view class="works-grid" v-else>
				<view class="work-item" v-for="work in currentWorks" :key="work.id" @tap="viewWork(work)">
					<view class="work-image-container">
						<image class="work-image" :src="work.cover" mode="aspectFill"></image>
						<view class="work-type" :class="work.typeClass">
							<uni-icons :type="work.typeIcon" size="16" color="#fff"></uni-icons>
						</view>
						<view class="work-duration" v-if="work.duration">
							<text class="duration-text">{{ work.duration }}</text>
						</view>
					</view>

					<view class="work-info">
						<text class="work-title">{{ work.title }}</text>
						<view class="work-stats">
							<view class="stat-item">
								<uni-icons type="eye" size="12" color="#999"></uni-icons>
								<text class="stat-text">{{ work.views }}</text>
							</view>
							<view class="stat-item">
								<uni-icons type="heart" size="12" color="#999"></uni-icons>
								<text class="stat-text">{{ work.likes }}</text>
							</view>
							<view class="stat-item">
								<uni-icons type="chatbubble" size="12" color="#999"></uni-icons>
								<text class="stat-text">{{ work.comments }}</text>
							</view>
						</view>
						<text class="work-time">{{ work.publishTime }}</text>
					</view>

					<view class="work-actions">
						<view class="action-btn" @tap.stop="editWork(work)">
							<uni-icons type="compose" size="16" color="#666"></uni-icons>
						</view>
						<view class="action-btn" @tap.stop="shareWork(work)">
							<uni-icons type="redo" size="16" color="#666"></uni-icons>
						</view>
						<view class="action-btn" @tap.stop="deleteWork(work)">
							<uni-icons type="trash" size="16" color="#ff4757"></uni-icons>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore">
				<text class="load-text">加载更多作品...</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				currentTab: 0,
				hasMore: true,
				
				// 作品分类标签
				workTabs: [
					{ name: '全部', count: 0, icon: 'list' },
					{ name: '图文', count: 8, icon: 'image' },
					{ name: '视频', count: 5, icon: 'videocam' },
					{ name: '音频', count: 2, icon: 'sound' },
					{ name: '草稿', count: 3, icon: 'compose' }
				],

				// 所有作品数据
				allWorks: [
					{
						id: '001',
						title: '周末户外徒步记录',
						cover: 'https://s1.imagehub.cc/images/2025/05/26/hiking-cover.png',
						type: 'image',
						typeClass: 'image',
						typeIcon: 'image',
						views: 1234,
						likes: 89,
						comments: 23,
						publishTime: '2024-12-20',
						status: 'published'
					},
					{
						id: '002',
						title: '城市夜景延时摄影',
						cover: 'https://s1.imagehub.cc/images/2025/05/26/night-city.png',
						type: 'video',
						typeClass: 'video',
						typeIcon: 'videocam',
						duration: '02:35',
						views: 2567,
						likes: 156,
						comments: 45,
						publishTime: '2024-12-18',
						status: 'published'
					},
					{
						id: '003',
						title: '美食制作分享',
						cover: 'https://s1.imagehub.cc/images/2025/05/26/food.png',
						type: 'image',
						typeClass: 'image',
						typeIcon: 'image',
						views: 987,
						likes: 67,
						comments: 12,
						publishTime: '2024-12-15',
						status: 'published'
					},
					{
						id: '004',
						title: '旅行日记草稿',
						cover: 'https://s1.imagehub.cc/images/2025/05/26/travel-draft.png',
						type: 'draft',
						typeClass: 'draft',
						typeIcon: 'compose',
						views: 0,
						likes: 0,
						comments: 0,
						publishTime: '2024-12-22',
						status: 'draft'
					}
				]
			}
		},

		computed: {
			totalWorks() {
				return this.allWorks.filter(work => work.status === 'published').length;
			},

			totalViews() {
				return this.allWorks.reduce((total, work) => total + work.views, 0);
			},

			totalLikes() {
				return this.allWorks.reduce((total, work) => total + work.likes, 0);
			},

			currentWorks() {
				if (this.currentTab === 0) {
					return this.allWorks;
				}
				const typeMap = {
					1: 'image',
					2: 'video',
					3: 'audio',
					4: 'draft'
				};
				return this.allWorks.filter(work => work.type === typeMap[this.currentTab]);
			}
		},

		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},

		methods: {
			goBack() {
				uni.switchTab({
					url: '/pages/profile/profile'
				});
			},

			searchWorks() {
				uni.showToast({
					title: '搜索功能开发中',
					icon: 'none'
				});
			},

			createWork() {
				uni.showActionSheet({
					itemList: ['发布图文', '发布视频', '发布音频'],
					success: (res) => {
						const types = ['image', 'video', 'audio'];
						uni.showToast({
							title: `创建${types[res.tapIndex]}作品`,
							icon: 'none'
						});
					}
				});
			},

			switchTab(index) {
				this.currentTab = index;
			},

			getEmptyDesc() {
				const descs = [
					'还没有发布任何作品哦',
					'暂无图文作品',
					'暂无视频作品',
					'暂无音频作品',
					'暂无草稿'
				];
				return descs[this.currentTab];
			},

			viewWork(work) {
				uni.navigateTo({
					url: `/pages/works/work-detail?id=${work.id}`
				});
			},

			editWork(work) {
				uni.navigateTo({
					url: `/pages/works/edit-work?id=${work.id}`
				});
			},

			shareWork(work) {
				uni.showActionSheet({
					itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
					success: (res) => {
						uni.showToast({
							title: '分享功能开发中',
							icon: 'none'
						});
					}
				});
			},

			deleteWork(work) {
				uni.showModal({
					title: '删除作品',
					content: `确定要删除"${work.title}"吗？删除后无法恢复。`,
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '已删除',
								icon: 'success'
							});
						}
					}
				});
			},

			loadMore() {
				if (!this.hasMore) return;
				
				setTimeout(() => {
					this.hasMore = false;
				}, 1000);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.my-works-page {
		background: #f5f5f5;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.status-bar {
		background: #fff;
	}

	.header {
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;

		.nav-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;
			height: 88rpx;

			.nav-left {
				display: flex;
				align-items: center;
				gap: 8rpx;
				flex: 1;

				.back-text {
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				text-align: center;
				flex: 2;
			}

			.nav-right {
				flex: 1;
				display: flex;
				justify-content: flex-end;
				gap: 16rpx;

				.search-btn, .add-btn {
					width: 48rpx;
					height: 48rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: #f8f9fa;
					border-radius: 24rpx;
				}
			}
		}
	}

	// 作品统计
	.works-stats {
		background: #fff;
		margin: 16rpx 24rpx;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

		.stats-container {
			display: flex;
			align-items: center;
			padding: 32rpx 24rpx;

			.stat-item {
				flex: 1;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
				gap: 8rpx;
				padding: 8rpx 12rpx;
				border-radius: 12rpx;
				transition: all 0.3s ease;

				&:active {
					background: #f8f9fa;
					transform: scale(0.98);
				}

				.stat-number {
					font-size: 48rpx;
					color: #333;
					font-weight: 700;
				}

				.stat-label {
					font-size: 24rpx;
					color: #999;
					font-weight: 500;
				}
			}

			.stat-divider {
				width: 1rpx;
				height: 60rpx;
				background: #f0f0f0;
			}
		}
	}

	// 作品分类
	.work-tabs {
		background: #fff;
		margin: 0 24rpx 16rpx;
		border-radius: 16rpx;
		padding: 8rpx;

		.tabs-scroll {
			white-space: nowrap;

			.tab-item {
				display: inline-block;
				position: relative;
				padding: 16rpx 24rpx;
				margin-right: 8rpx;
				border-radius: 12rpx;
				transition: all 0.3s ease;

				&.active {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

					.tab-text {
						color: #fff;
						font-weight: 600;
					}

					.tab-count {
						background: #fff;
						color: #667eea;
					}
				}

				.tab-content {
					display: flex;
					align-items: center;
					gap: 6rpx;
				}

				.tab-text {
					font-size: 26rpx;
					color: #666;
					transition: all 0.3s ease;
				}

				.tab-count {
					position: absolute;
					top: 4rpx;
					right: 4rpx;
					background: #f0f0f0;
					color: #999;
					font-size: 18rpx;
					padding: 2rpx 6rpx;
					border-radius: 8rpx;
					min-width: 20rpx;
					text-align: center;
					line-height: 1;
				}
			}
		}
	}

	// 作品内容区域 - 响应式适配
	.works-content {
		flex: 1;
		padding: 0 24rpx 120rpx;

		// 小屏适配
		@media (max-width: 320px) {
			padding: 0 16rpx 100rpx;
		}

		// 大屏适配
		@media (min-width: 414px) {
			padding: 0 32rpx 140rpx;
		}

		// 空状态
		.empty-state {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 120rpx 40rpx;
			text-align: center;

			.empty-icon {
				margin-bottom: 32rpx;
				opacity: 0.5;
			}

			.empty-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				margin-bottom: 16rpx;
			}

			.empty-desc {
				font-size: 26rpx;
				color: #999;
				margin-bottom: 48rpx;
				line-height: 1.5;
			}

			.empty-action {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				padding: 20rpx 48rpx;
				border-radius: 32rpx;
				box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);

				.action-text {
					font-size: 28rpx;
					color: #fff;
					font-weight: 600;
				}
			}
		}

		// 作品网格 - 响应式适配
		.works-grid {
			display: flex;
			flex-wrap: wrap;
			gap: 16rpx;

			// 小屏适配
			@media (max-width: 320px) {
				gap: 12rpx;
			}

			// 大屏适配
			@media (min-width: 414px) {
				gap: 20rpx;
			}

			.work-item {
				width: calc(50% - 8rpx);
				background: #fff;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
				transition: all 0.3s ease;

				// 小屏适配
				@media (max-width: 320px) {
					width: calc(50% - 6rpx);
					border-radius: 12rpx;
				}

				// 超大屏适配 - 显示3列
				@media (min-width: 480px) {
					width: calc(33.333% - 14rpx);
				}

				&:active {
					transform: scale(0.98);
				}

				.work-image-container {
					position: relative;
					height: 240rpx;

					.work-image {
						width: 100%;
						height: 100%;
						background: #f5f5f5;
					}

					.work-type {
						position: absolute;
						top: 12rpx;
						left: 12rpx;
						width: 32rpx;
						height: 32rpx;
						border-radius: 16rpx;
						display: flex;
						align-items: center;
						justify-content: center;

						&.image {
							background: rgba(76, 205, 196, 0.9);
						}

						&.video {
							background: rgba(255, 107, 107, 0.9);
						}

						&.audio {
							background: rgba(255, 193, 7, 0.9);
						}

						&.draft {
							background: rgba(108, 117, 125, 0.9);
						}
					}

					.work-duration {
						position: absolute;
						bottom: 12rpx;
						right: 12rpx;
						background: rgba(0, 0, 0, 0.7);
						color: #fff;
						font-size: 20rpx;
						padding: 4rpx 8rpx;
						border-radius: 8rpx;

						.duration-text {
							font-size: 20rpx;
						}
					}
				}

				.work-info {
					padding: 16rpx;

					.work-title {
						font-size: 26rpx;
						color: #333;
						font-weight: 600;
						display: block;
						margin-bottom: 12rpx;
						line-height: 1.3;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;

						// 小屏字体适配
						@media (max-width: 320px) {
							font-size: 24rpx;
							margin-bottom: 10rpx;
						}

						// 大屏字体适配
						@media (min-width: 414px) {
							font-size: 28rpx;
							margin-bottom: 14rpx;
						}
					}

					.work-stats {
						display: flex;
						align-items: center;
						gap: 16rpx;
						margin-bottom: 8rpx;

						.stat-item {
							display: flex;
							align-items: center;
							gap: 4rpx;

							.stat-text {
								font-size: 20rpx;
								color: #999;
							}
						}
					}

					.work-time {
						font-size: 20rpx;
						color: #ccc;
					}
				}

				.work-actions {
					display: flex;
					justify-content: space-around;
					align-items: center;
					padding: 16rpx;
					border-top: 1rpx solid #f5f5f5;

					.action-btn {
						width: 48rpx;
						height: 48rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						background: #f8f9fa;
						border-radius: 24rpx;
						transition: all 0.3s ease;

						&:active {
							transform: scale(0.9);
							background: #e9ecef;
						}
					}
				}
			}
		}

		// 加载更多
		.load-more {
			text-align: center;
			padding: 32rpx;

			.load-text {
				font-size: 26rpx;
				color: #999;
			}
		}
	}
</style>
