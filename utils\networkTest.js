/**
 * 网络连接测试工具
 * 用于诊断和修复网络连接问题
 */

// 网络状态检测
export function checkNetworkStatus() {
	return new Promise((resolve) => {
		uni.getNetworkType({
			success: (res) => {
				console.log('网络类型:', res.networkType)
				resolve({
					isConnected: res.networkType !== 'none',
					networkType: res.networkType,
					isWifi: res.networkType === 'wifi',
					isMobile: ['2g', '3g', '4g', '5g'].includes(res.networkType)
				})
			},
			fail: () => {
				resolve({
					isConnected: false,
					networkType: 'unknown',
					isWifi: false,
					isMobile: false
				})
			}
		})
	})
}

// 测试基础网络连接
export function testBasicConnection() {
	return new Promise((resolve) => {
		const testUrls = [
			'https://www.baidu.com',
			'https://api.next.bspapp.com',
			'https://httpbin.org/get'
		]
		
		let successCount = 0
		let completedCount = 0
		const results = []
		
		testUrls.forEach((url, index) => {
			uni.request({
				url: url,
				method: 'GET',
				timeout: 10000,
				success: (res) => {
					successCount++
					results[index] = { url, success: true, status: res.statusCode }
					console.log(`网络测试成功: ${url}`)
				},
				fail: (error) => {
					results[index] = { url, success: false, error: error.errMsg }
					console.log(`网络测试失败: ${url}`, error)
				},
				complete: () => {
					completedCount++
					if (completedCount === testUrls.length) {
						resolve({
							success: successCount > 0,
							successRate: successCount / testUrls.length,
							results: results
						})
					}
				}
			})
		})
	})
}

// 测试uniCloud连接
export function testUniCloudConnection() {
	return new Promise(async (resolve) => {
		try {
			console.log('开始测试uniCloud连接...')
			
			// 测试简单云函数
			const result = await uniCloud.callFunction({
				name: 'simple-test',
				data: { 
					test: 'network-connection',
					timestamp: Date.now()
				}
			})
			
			console.log('uniCloud连接测试成功:', result)
			resolve({
				success: true,
				result: result.result,
				message: 'uniCloud连接正常'
			})
		} catch (error) {
			console.error('uniCloud连接测试失败:', error)
			resolve({
				success: false,
				error: error,
				message: 'uniCloud连接失败: ' + (error.message || error.errMsg || '未知错误')
			})
		}
	})
}

// 测试数据库连接
export function testDatabaseConnection() {
	return new Promise(async (resolve) => {
		try {
			console.log('开始测试数据库连接...')

			// 测试数据库查询
			const result = await uniCloud.callFunction({
				name: 'test-db',
				data: {
					action: 'test-connection'
				}
			})

			console.log('数据库连接测试成功:', result)
			resolve({
				success: true,
				result: result.result,
				message: '数据库连接正常'
			})
		} catch (error) {
			console.error('数据库连接测试失败:', error)
			resolve({
				success: false,
				error: error,
				message: '数据库连接失败: ' + (error.message || error.errMsg || '未知错误')
			})
		}
	})
}

// 测试云函数部署状态
export function testCloudFunctionDeployment() {
	return new Promise(async (resolve) => {
		try {
			console.log('开始测试云函数部署状态...')

			const functions = ['simple-test', 'auth', 'test-db']
			const results = {}
			let successCount = 0

			for (const funcName of functions) {
				try {
					const result = await uniCloud.callFunction({
						name: funcName,
						data: { test: 'deployment-check' }
					})

					results[funcName] = {
						deployed: true,
						response: result.result
					}
					successCount++
				} catch (error) {
					results[funcName] = {
						deployed: false,
						error: error.message || error.errMsg,
						errorCode: error.errCode
					}
				}
			}

			console.log('云函数部署测试完成:', results)
			resolve({
				success: successCount === functions.length,
				results: results,
				summary: {
					total: functions.length,
					deployed: successCount,
					failed: functions.length - successCount
				},
				message: `${successCount}/${functions.length} 个云函数部署正常`
			})
		} catch (error) {
			console.error('云函数部署测试失败:', error)
			resolve({
				success: false,
				error: error,
				message: '云函数部署测试失败: ' + (error.message || error.errMsg || '未知错误')
			})
		}
	})
}

// 综合网络诊断
export async function comprehensiveNetworkDiagnosis() {
	console.log('开始综合网络诊断...')
	
	const diagnosis = {
		timestamp: new Date().toISOString(),
		tests: {}
	}
	
	// 1. 检测网络状态
	console.log('1. 检测网络状态...')
	diagnosis.tests.networkStatus = await checkNetworkStatus()
	
	// 2. 测试基础网络连接
	console.log('2. 测试基础网络连接...')
	diagnosis.tests.basicConnection = await testBasicConnection()
	
	// 3. 测试uniCloud连接
	console.log('3. 测试uniCloud连接...')
	diagnosis.tests.uniCloudConnection = await testUniCloudConnection()
	
	// 4. 测试数据库连接
	console.log('4. 测试数据库连接...')
	diagnosis.tests.databaseConnection = await testDatabaseConnection()

	// 5. 测试云函数部署状态
	console.log('5. 测试云函数部署状态...')
	diagnosis.tests.cloudFunctionDeployment = await testCloudFunctionDeployment()

	// 分析结果
	const analysis = analyzeResults(diagnosis.tests)
	diagnosis.analysis = analysis
	
	console.log('网络诊断完成:', diagnosis)
	return diagnosis
}

// 分析诊断结果
function analyzeResults(tests) {
	const analysis = {
		overall: 'unknown',
		issues: [],
		recommendations: []
	}
	
	// 检查网络状态
	if (!tests.networkStatus.isConnected) {
		analysis.issues.push('设备未连接到网络')
		analysis.recommendations.push('请检查WiFi或移动数据连接')
		analysis.overall = 'critical'
		return analysis
	}
	
	// 检查基础网络连接
	if (!tests.basicConnection.success) {
		analysis.issues.push('基础网络连接失败')
		analysis.recommendations.push('请检查网络防火墙或代理设置')
		analysis.overall = 'critical'
		return analysis
	}
	
	// 检查uniCloud连接
	if (!tests.uniCloudConnection.success) {
		analysis.issues.push('uniCloud服务连接失败')
		analysis.recommendations.push('请检查uniCloud配置或稍后重试')
		if (analysis.overall !== 'critical') {
			analysis.overall = 'warning'
		}
	}
	
	// 检查数据库连接
	if (!tests.databaseConnection.success) {
		analysis.issues.push('数据库连接失败')
		analysis.recommendations.push('请检查数据库配置或云函数部署状态')
		if (analysis.overall !== 'critical') {
			analysis.overall = 'warning'
		}
	}

	// 检查云函数部署状态
	if (!tests.cloudFunctionDeployment.success) {
		analysis.issues.push('云函数部署不完整')
		analysis.recommendations.push('请在HBuilderX中部署所有云函数')
		if (analysis.overall !== 'critical') {
			analysis.overall = 'warning'
		}
	}
	
	// 如果没有发现问题
	if (analysis.issues.length === 0) {
		analysis.overall = 'good'
		analysis.recommendations.push('网络连接正常')
	}
	
	return analysis
}

// 显示诊断结果
export function showDiagnosisResult(diagnosis) {
	const { analysis } = diagnosis
	
	let title = '网络诊断结果'
	let icon = 'none'
	
	switch (analysis.overall) {
		case 'good':
			title = '网络连接正常'
			icon = 'success'
			break
		case 'warning':
			title = '网络连接存在问题'
			icon = 'none'
			break
		case 'critical':
			title = '网络连接失败'
			icon = 'error'
			break
	}
	
	// 显示主要结果
	uni.showToast({
		title: title,
		icon: icon,
		duration: 3000
	})
	
	// 如果有问题，显示详细信息
	if (analysis.issues.length > 0) {
		setTimeout(() => {
			uni.showModal({
				title: '网络诊断详情',
				content: `问题：${analysis.issues.join('、')}\n\n建议：${analysis.recommendations.join('、')}`,
				showCancel: false,
				confirmText: '知道了'
			})
		}, 3500)
	}
}

// 自动修复网络问题
export async function autoFixNetworkIssues() {
	console.log('开始自动修复网络问题...')

	const fixes = []

	try {
		// 1. 保存用户登录信息
		const userInfo = uni.getStorageSync('userInfo')
		const token = uni.getStorageSync('token')
		const isLoggedIn = uni.getStorageSync('isLoggedIn')

		// 2. 清除网络缓存（但保留用户信息）
		const storage = uni.getStorageInfoSync()
		storage.keys.forEach(key => {
			// 保留用户登录相关的存储
			if (!['userInfo', 'token', 'isLoggedIn'].includes(key)) {
				uni.removeStorageSync(key)
			}
		})
		fixes.push('清除网络缓存')

		// 3. 恢复用户登录信息
		if (userInfo) {
			uni.setStorageSync('userInfo', userInfo)
		}
		if (token) {
			uni.setStorageSync('token', token)
		}
		if (isLoggedIn) {
			uni.setStorageSync('isLoggedIn', isLoggedIn)
		}
		fixes.push('保留用户登录状态')

		// 4. 重新初始化uniCloud
		// 注意：这里可能需要重启应用才能生效
		fixes.push('重新初始化云服务')

		// 5. 重置网络请求配置
		fixes.push('重置网络配置')

		console.log('自动修复完成:', fixes)
		return {
			success: true,
			fixes: fixes,
			message: '已尝试修复网络问题，用户登录状态已保留'
		}
	} catch (error) {
		console.error('自动修复失败:', error)
		return {
			success: false,
			error: error,
			message: '自动修复失败，请手动检查网络设置'
		}
	}
}
