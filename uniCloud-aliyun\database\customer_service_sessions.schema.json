{"bsonType": "object", "description": "客服会话表", "required": ["user_id", "status"], "properties": {"_id": {"description": "会话ID"}, "user_id": {"bsonType": "string", "description": "用户ID", "foreignKey": "uni-id-users._id"}, "customer_service_id": {"bsonType": "string", "description": "客服ID", "foreignKey": "admins._id"}, "status": {"bsonType": "string", "description": "会话状态", "enum": ["waiting", "active", "closed"], "default": "waiting"}, "created_at": {"bsonType": "timestamp", "description": "创建时间"}, "updated_at": {"bsonType": "timestamp", "description": "更新时间"}}}