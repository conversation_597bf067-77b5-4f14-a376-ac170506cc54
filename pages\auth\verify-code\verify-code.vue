<template>
	<view class="container">
		<!-- 背景图片 -->
		<image src="/static/zhuce.png" class="background-image" mode="aspectFill"></image>

		<!-- 状态栏占位 -->
		<view class="status-bar" :style="getStatusBarStyle()"></view>

		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="back-button" @click="goBack">
				<image src="/static/fanhuilogo.png" class="back-icon" mode="aspectFit"></image>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<!-- 标题 -->
			<view class="title-section">
				<text class="title">输入验证码</text>
				<text class="subtitle">验证码已发送至 <text class="phone-highlight">{{ maskedPhone }}</text></text>
			</view>

			<!-- 验证码输入框 -->
			<view class="code-input-container">
				<!-- 隐藏的真实输入框 -->
				<input
					class="hidden-input"
					type="number"
					maxlength="4"
					v-model="verificationCodeInput"
					@input="onCodeInputChange"
					@focus="onInputFocus"
					ref="hiddenInput"
				/>
				<!-- 显示的验证码框 -->
				<view
					v-for="(item, index) in 4"
					:key="index"
					class="code-display"
					:class="{ active: index === currentIndex, filled: codeArray[index] }"
					@click="focusInput"
				>
					<text class="code-text">{{ codeArray[index] || '' }}</text>
				</view>
			</view>

			<!-- 重新发送 -->
			<view class="resend-section">
				<text class="resend-text" v-if="countdown > 0">{{ countdown }}s后可重新发送</text>
				<text class="resend-link" v-else @click="resendCode">重新发送验证码</text>
			</view>

			<!-- 立即注册按钮 -->
			<view class="register-btn" @click="completeRegister" :class="{ disabled: !canSubmit }">
				<text class="register-btn-text">立即注册</text>
			</view>
		</view>
	</view>

	<!-- 验证码发送成功弹窗 -->
	<view v-if="showSuccessModal" class="success-modal-overlay" @click="closeSuccessModal">
		<view class="success-modal" @click.stop>
			<!-- 顶部渐变标题区域 -->
			<view class="modal-header">
				<!-- 成功图标 -->
				<view class="success-icon">
					<text class="icon-text">✓</text>
				</view>
				<!-- 标题 -->
				<text class="modal-title">短信验证码</text>
			</view>

			<!-- 内容 -->
			<view class="modal-content">
				<!-- 短信内容 -->
				<view class="sms-content">
					<text class="sms-text">【趣哒同行】尊敬的用户，您的验证码为：</text>
					<text class="verification-code">{{ currentVerificationCode }}</text>
					<text class="sms-text">，有效期1分钟，请勿泄露验证码，谨防诈骗。</text>
				</view>

				<!-- 发送目标 -->
				<text class="modal-phone">发送至: {{ maskedPhone }}</text>

				<!-- 确定按钮 -->
				<view class="modal-btn" @click="closeSuccessModal">
					<text class="modal-btn-text">好的</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			mobile: '',
			codeArray: ['', '', '', ''],
			verificationCodeInput: '',
			currentIndex: 0,
			countdown: 60,
			countdownTimer: null,
			statusBarHeight: 0,
			showSuccessModal: false,
			currentVerificationCode: ''
		}
	},
	
	computed: {
		maskedPhone() {
			if (this.mobile.length === 11) {
				return this.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
			}
			return this.mobile
		},
		
		verificationCode() {
			return this.verificationCodeInput
		},
		
		canSubmit() {
			return this.verificationCode.length === 4
		}
	},
	
	onLoad(options) {
		this.mobile = options.mobile || ''
		this.startCountdown()
		
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight || 0
	},
	
	onUnload() {
		if (this.countdownTimer) {
			clearInterval(this.countdownTimer)
		}
	},
	
	methods: {
		getStatusBarStyle() {
			return {
				height: this.statusBarHeight + 'px'
			}
		},
		
		goBack() {
			uni.navigateBack()
		},
		
		onCodeInputChange(event) {
			const value = event.detail.value
			// 只保留数字，最多4位
			const cleanValue = value.replace(/\D/g, '').slice(0, 4)
			this.verificationCodeInput = cleanValue

			// 更新显示数组
			this.codeArray = ['', '', '', '']
			for (let i = 0; i < cleanValue.length; i++) {
				this.codeArray[i] = cleanValue[i]
			}

			// 更新当前索引
			this.currentIndex = cleanValue.length < 4 ? cleanValue.length : 3
		},

		onInputFocus() {
			// 输入框获得焦点时的处理
		},

		focusInput() {
			// 点击验证码显示框时，聚焦到隐藏输入框
			this.$refs.hiddenInput.focus()
		},
		
		startCountdown() {
			this.countdown = 60
			this.countdownTimer = setInterval(() => {
				this.countdown--
				if (this.countdown <= 0) {
					clearInterval(this.countdownTimer)
				}
			}, 1000)
		},
		
		async resendCode() {
			try {
				uni.showLoading({
					title: '发送中...'
				})
				
				const result = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'getVerificationCode',
						mobile: this.mobile,
						type: 'register'
					}
				})
				
				uni.hideLoading()
				
				if (result.result.code === 0) {
					// 保存验证码用于弹窗显示
					if (result.result.data && result.result.data.verificationCode) {
						this.currentVerificationCode = result.result.data.verificationCode
						console.log('新验证码:', result.result.data.verificationCode)
					}

					// 显示自定义成功弹窗
					this.showSuccessModal = true
					this.startCountdown()
				} else {
					uni.showToast({
						title: result.result.message || '发送失败',
						icon: 'none'
					})
				}
			} catch (error) {
				uni.hideLoading()
				console.error('重新发送验证码失败:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			}
		},
		


		closeSuccessModal() {
			this.showSuccessModal = false
		},
		
		async completeRegister() {
			if (!this.canSubmit) {
				return
			}
			
			try {
				uni.showLoading({
					title: '验证中...'
				})
				
				// 先测试云函数是否能正常调用
				console.log('准备调用云函数验证验证码:', {
					action: 'verifyCode',
					mobile: this.mobile,
					code: this.verificationCode,
					type: 'register'
				})

				// 先测试一个简单的调用
				try {
					const testResult = await uniCloud.callFunction({
						name: 'auth',
						data: {
							action: 'checkUserExists',
							mobile: this.mobile
						}
					})
					console.log('测试云函数调用结果:', testResult)
				} catch (testError) {
					console.error('测试云函数调用失败:', testError)
				}

				const result = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'verifyCode',
						mobile: this.mobile,
						code: this.verificationCode,
						type: 'register'
					}
				})

				uni.hideLoading()

				console.log('验证码验证结果:', result)
				console.log('result.result:', result.result)

				if (result.result.code === 0) {
					uni.showToast({
						title: '验证成功',
						icon: 'success'
					})

					// 验证成功后跳转到完善资料页面，传递手机号和验证码
					setTimeout(() => {
						uni.navigateTo({
							url: `/pages/profile-setup/profile-setup?mobile=${this.mobile}&code=${this.verificationCode}`
						})
					}, 1500)
				} else {
					uni.showToast({
						title: result.result.message || '验证码错误',
						icon: 'none'
					})
				}
			} catch (error) {
				uni.hideLoading()
				console.error('验证失败:', error)
				console.error('完整错误信息:', JSON.stringify(error))
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			}
		}
	}
}
</script>

<style scoped>
@import '@/styles/global.scss';

.container {
	width: 100%;
	min-height: 100vh;
	position: relative;
	overflow: hidden;
}

.background-image {
	position: fixed;
	top: -10px;
	left: -10px;
	width: calc(100% + 20px);
	height: calc(100% + 20px);
	z-index: -1;
}

.status-bar {
	width: 100%;
	background: transparent;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 32rpx;
	z-index: 100;
	padding-top: var(--status-bar-height);
}

.back-button {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon {
	width: 36rpx;
	height: 36rpx;
}

.content {
	padding: 160rpx 40rpx 60rpx;
	min-height: 100vh;
	box-sizing: border-box;
}

.title-section {
	text-align: center;
	margin-bottom: 80rpx;
	margin-top: 700rpx;
}

.title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333333;
	display: block;
	margin-bottom: 20rpx;
}

.subtitle {
	font-size: 28rpx;
	color: #666666;
	display: block;
}

.phone-highlight {
	color: #66D4C8;
	font-weight: bold;
}

.code-input-container {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
	padding: 0 60rpx;
	width: 100%;
	box-sizing: border-box;
}

.hidden-input {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
	z-index: 10;
}

.code-display {
	width: 88rpx;
	height: 88rpx;
	background: #FFFFFF;
	border: 3rpx solid #66D4C8;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	box-shadow: 0 4rpx 16rpx rgba(102, 212, 200, 0.2);
}

.code-display.active {
	border-color: #667eea;
	background: rgba(255, 255, 255, 0.3);
}

.code-display.filled {
	background: rgba(255, 255, 255, 0.4);
}

.code-text {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
}

.resend-section {
	text-align: center;
	margin-bottom: 60rpx;
}

.resend-text {
	font-size: 28rpx;
	color: #66D4C8;
}

.resend-link {
	font-size: 28rpx;
	color: #66D4C8;
	text-decoration: underline;
}

.username-section,
.email-section,
.password-section {
	margin-bottom: 40rpx;
}

.username-label,
.email-label,
.password-label {
	font-size: 32rpx;
	color: #333333;
	display: block;
	margin-bottom: 20rpx;
	font-weight: bold;
}

.username-input,
.email-input,
.password-input {
	width: 100%;
	height: 88rpx;
	background: #FFFFFF;
	border: 3rpx solid #66D4C8;
	border-radius: 16rpx;
	padding: 0 32rpx;
	font-size: 32rpx;
	color: #333333;
	box-sizing: border-box;
	box-shadow: 0 4rpx 16rpx rgba(102, 212, 200, 0.2);
}

.password-section {
	position: relative;
}

.password-toggle {
	position: absolute;
	right: 32rpx;
	top: 50rpx;
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.eye-icon {
	width: 36rpx;
	height: 36rpx;
}

/* 完成注册按钮 */
.register-btn {
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 100%);
	border-radius: 50rpx;
	height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 60rpx 0 40rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 212, 200, 0.3);
	transition: all 0.3s ease;
}

.register-btn.disabled {
	background: #CCCCCC;
	box-shadow: none;
}

.register-btn:active:not(.disabled) {
	transform: scale(0.98);
	box-shadow: 0 4rpx 12rpx rgba(102, 212, 200, 0.4);
}

.register-btn-text {
	font-size: 36rpx;
	font-weight: bold;
	color: #FFFFFF;
}

.complete-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 60rpx;
}

.complete-btn.disabled {
	background: rgba(255, 255, 255, 0.3);
}

.complete-btn-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #FFFFFF;
}

/* 验证码发送成功弹窗样式 */
.success-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(5rpx);
}

.success-modal {
	width: 600rpx;
	background: #FFFFFF;
	border-radius: 40rpx;
	position: relative;
	overflow: hidden;
	margin: 0 40rpx;
	box-shadow: 0 20rpx 60rpx rgba(102, 212, 200, 0.3);
}

.modal-decoration {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.deco-circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}

.deco-1 {
	width: 120rpx;
	height: 120rpx;
	top: -60rpx;
	right: -60rpx;
	background: rgba(255, 255, 255, 0.15);
}

.deco-2 {
	width: 80rpx;
	height: 80rpx;
	top: 100rpx;
	left: -40rpx;
	background: rgba(255, 255, 255, 0.1);
}

.deco-3 {
	width: 60rpx;
	height: 60rpx;
	bottom: 80rpx;
	right: 60rpx;
	background: rgba(255, 255, 255, 0.08);
}

.modal-header {
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 50%, #5DD5C9 100%);
	padding: 40rpx 40rpx 30rpx;
	text-align: center;
	border-radius: 40rpx 40rpx 0 0;
}

.modal-content {
	position: relative;
	z-index: 2;
	text-align: center;
	padding: 30rpx 40rpx 40rpx;
}

.success-icon {
	width: 120rpx;
	height: 120rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 30rpx;
	backdrop-filter: blur(10rpx);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.icon-text {
	font-size: 60rpx;
	font-weight: bold;
	color: #FFFFFF;
}

.modal-btn {
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 100%);
	border-radius: 50rpx;
	padding: 24rpx 60rpx;
	margin-top: 40rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 212, 200, 0.3);
}

.modal-btn-text {
	font-size: 28rpx;
	font-weight: 500;
	color: #FFFFFF;
	text-align: center;
	white-space: nowrap;
}

.modal-title {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #FFFFFF;
	margin-bottom: 20rpx;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
	text-align: center;
}

.sms-content {
	background: rgba(255, 255, 255, 0.15);
	padding: 30rpx;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	text-align: center;
}

.sms-text {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.5;
}

.verification-code {
	font-size: 36rpx;
	font-weight: bold;
	color: #66D4C8;
	text-shadow: 0 2rpx 8rpx rgba(102, 212, 200, 0.6);
	margin: 0 10rpx;
}

.modal-phone {
	display: block;
	font-size: 28rpx;
	color: #666666;
	text-align: center;
}
</style>
