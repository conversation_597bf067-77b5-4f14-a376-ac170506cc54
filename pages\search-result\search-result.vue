<template>
	<view class="search-result-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 搜索头部 -->
		<view class="search-header">
			<view class="back-btn" @click="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
			</view>
			<view class="search-input-container">
				<view class="search-input-wrapper">
					<uni-icons type="search" size="18" color="#999"></uni-icons>
					<input 
						class="search-input" 
						v-model="searchKeyword" 
						placeholder="搜索活动、用户、话题..." 
						@confirm="onSearchConfirm"
					/>
				</view>
			</view>
			<view class="search-btn" @click="onSearchConfirm">
				<text class="search-text">搜索</text>
			</view>
		</view>

		<!-- 搜索结果内容 -->
		<view class="search-content">
			<!-- 搜索结果统计 -->
			<view v-if="searchResults.length > 0" class="result-stats">
				<text class="stats-text">找到 {{ searchResults.length }} 个相关结果</text>
			</view>

			<!-- 搜索结果列表 -->
			<view v-if="searchResults.length > 0" class="result-list">
				<view 
					v-for="(item, index) in searchResults" 
					:key="index"
					class="result-item"
					@click="viewResult(item)"
				>
					<view class="result-icon">
						<uni-icons :type="item.icon" size="24" :color="item.iconColor"></uni-icons>
					</view>
					<view class="result-info">
						<text class="result-title">{{ item.title }}</text>
						<text class="result-desc">{{ item.desc }}</text>
						<view class="result-meta">
							<text class="meta-tag">{{ item.category }}</text>
							<text class="meta-time">{{ item.time }}</text>
						</view>
					</view>
					<view class="result-arrow">
						<uni-icons type="right" size="14" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 无搜索结果 -->
			<view v-else-if="hasSearched" class="no-result">
				<view class="no-result-icon">
					<uni-icons type="search" size="60" color="#ccc"></uni-icons>
				</view>
				<text class="no-result-title">未找到相关内容</text>
				<text class="no-result-desc">试试其他关键词或浏览推荐内容</text>
				
				<!-- 推荐内容 -->
				<view class="recommend-section">
					<text class="recommend-title">推荐内容</text>
					<view class="recommend-list">
						<view 
							v-for="(item, index) in recommendItems" 
							:key="index"
							class="recommend-item"
							@click="viewRecommend(item)"
						>
							<text class="recommend-text">{{ item.title }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 搜索提示 -->
			<view v-else class="search-tips">
				<view class="tips-icon">
					<uni-icons type="info" size="40" color="#999"></uni-icons>
				</view>
				<text class="tips-title">输入关键词开始搜索</text>
				<text class="tips-desc">可以搜索活动、用户、话题等内容</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				searchKeyword: '',
				hasSearched: false,
				searchResults: [],
				
				// 推荐内容
				recommendItems: [
					{ title: '王者荣耀五排' },
					{ title: '周末BBQ聚会' },
					{ title: '朝阳公园跑步' },
					{ title: '咖啡厅约会' },
					{ title: '户外徒步' },
					{ title: 'KTV唱歌' }
				],
				
				// 模拟搜索数据
				allData: [
					{
						title: '王者荣耀五排开黑',
						desc: '今晚20:00，缺2个队友，一起上分',
						category: '游戏',
						time: '2小时前',
						icon: 'videocam',
						iconColor: '#FF6B6B'
					},
					{
						title: '周末海边BBQ',
						desc: '金沙滩烧烤聚会，已有12人报名',
						category: '聚会',
						time: '1天前',
						icon: 'fire',
						iconColor: '#FFD700'
					},
					{
						title: '朝阳公园晨跑',
						desc: '每周三次，坚持健身打卡',
						category: '运动',
						time: '3小时前',
						icon: 'foot',
						iconColor: '#4ECDC4'
					},
					{
						title: '三里屯酒吧聚会',
						desc: '夜生活爱好者聚集地',
						category: '社交',
						time: '5小时前',
						icon: 'location',
						iconColor: '#9C27B0'
					},
					{
						title: '咖啡厅约会攻略',
						desc: '北京最适合约会的咖啡厅推荐',
						category: '话题',
						time: '1天前',
						icon: 'heart',
						iconColor: '#E91E63'
					},
					{
						title: '户外徒步爱好者',
						desc: '探索自然，享受户外生活',
						category: '户外',
						time: '2天前',
						icon: 'navigate',
						iconColor: '#8BC34A'
					}
				]
			}
		},

		onLoad(options) {
			// 获取系统信息
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
			
			// 获取搜索关键词
			if (options.keyword) {
				this.searchKeyword = decodeURIComponent(options.keyword);
				this.performSearch();
			}
		},

		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 搜索确认
			onSearchConfirm() {
				if (this.searchKeyword.trim()) {
					this.performSearch();
				}
			},

			// 执行搜索
			performSearch() {
				this.hasSearched = true;
				const keyword = this.searchKeyword.toLowerCase();
				
				// 模拟搜索逻辑
				this.searchResults = this.allData.filter(item => 
					item.title.toLowerCase().includes(keyword) ||
					item.desc.toLowerCase().includes(keyword) ||
					item.category.toLowerCase().includes(keyword)
				);
				
				console.log('搜索关键词:', keyword);
				console.log('搜索结果:', this.searchResults.length);
			},

			// 查看搜索结果
			viewResult(item) {
				console.log('查看搜索结果:', item.title);
				// 根据不同类型跳转到对应页面
			},

			// 查看推荐内容
			viewRecommend(item) {
				this.searchKeyword = item.title;
				this.performSearch();
			}
		}
	}
</script>

<style lang="scss">
	.search-result-container {
		background: #f8f9fa;
		min-height: 100vh;
	}

	.status-bar {
		background: #fff;
	}

	// 搜索头部
	.search-header {
		background: #fff;
		padding: 16rpx 24rpx;
		display: flex;
		align-items: center;
		gap: 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

		.back-btn {
			padding: 8rpx;
		}

		.search-input-container {
			flex: 1;

			.search-input-wrapper {
				background: #f5f5f5;
				border-radius: 20rpx;
				padding: 12rpx 16rpx;
				display: flex;
				align-items: center;
				gap: 8rpx;

				.search-input {
					flex: 1;
					font-size: 26rpx;
					color: #333;
					background: transparent;
					border: none;
					outline: none;

					&::placeholder {
						color: #999;
					}
				}
			}
		}

		.search-btn {
			.search-text {
				font-size: 26rpx;
				color: #FFD700;
				font-weight: 600;
			}
		}
	}

	// 搜索内容
	.search-content {
		padding: 24rpx;
	}

	// 搜索结果统计
	.result-stats {
		margin-bottom: 20rpx;

		.stats-text {
			font-size: 24rpx;
			color: #999;
		}
	}

	// 搜索结果列表
	.result-list {
		.result-item {
			background: #fff;
			border-radius: 16rpx;
			padding: 24rpx;
			margin-bottom: 16rpx;
			display: flex;
			align-items: center;
			gap: 20rpx;

			&:active {
				background: #f8f9fa;
			}

			.result-icon {
				width: 60rpx;
				height: 60rpx;
				background: #f5f5f5;
				border-radius: 12rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.result-info {
				flex: 1;

				.result-title {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
					display: block;
					margin-bottom: 8rpx;
				}

				.result-desc {
					font-size: 24rpx;
					color: #666;
					display: block;
					margin-bottom: 12rpx;
				}

				.result-meta {
					display: flex;
					align-items: center;
					gap: 16rpx;

					.meta-tag {
						background: #FFD700;
						color: #fff;
						font-size: 20rpx;
						padding: 4rpx 8rpx;
						border-radius: 8rpx;
					}

					.meta-time {
						font-size: 20rpx;
						color: #999;
					}
				}
			}

			.result-arrow {
				opacity: 0.5;
			}
		}
	}

	// 无搜索结果
	.no-result {
		text-align: center;
		padding: 80rpx 40rpx;

		.no-result-icon {
			margin-bottom: 24rpx;
		}

		.no-result-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			display: block;
			margin-bottom: 12rpx;
		}

		.no-result-desc {
			font-size: 26rpx;
			color: #999;
			display: block;
			margin-bottom: 40rpx;
		}

		.recommend-section {
			.recommend-title {
				font-size: 28rpx;
				font-weight: 600;
				color: #333;
				display: block;
				margin-bottom: 20rpx;
			}

			.recommend-list {
				display: flex;
				flex-wrap: wrap;
				gap: 16rpx;
				justify-content: center;

				.recommend-item {
					background: #fff;
					border-radius: 20rpx;
					padding: 12rpx 20rpx;
					border: 1rpx solid #e0e0e0;

					&:active {
						background: #f0f0f0;
					}

					.recommend-text {
						font-size: 24rpx;
						color: #666;
					}
				}
			}
		}
	}

	// 搜索提示
	.search-tips {
		text-align: center;
		padding: 120rpx 40rpx;

		.tips-icon {
			margin-bottom: 24rpx;
		}

		.tips-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			display: block;
			margin-bottom: 12rpx;
		}

		.tips-desc {
			font-size: 26rpx;
			color: #999;
		}
	}
</style>
