<template>
	<view class="page-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="header-content">
				<view class="back-button" @click="goBack">
					<text class="back-icon">‹</text>
				</view>
				<text class="header-title">内容标准</text>
				<view class="header-placeholder"></view>
			</view>
		</view>

		<!-- 主要内容 -->
		<view class="main-content">
			<scroll-view scroll-y="true" class="scroll-container">
				<view class="document-content">
					<!-- 文档标题 -->
					<view class="document-header">
						<text class="document-title">闲伴发布内容规范</text>
						<text class="document-subtitle">创作优质内容，传播正能量</text>
						<view class="document-info">
							<text class="info-item">版本：v2.3</text>
							<text class="info-item">发布日期：2025年6月4日</text>
							<text class="info-item">生效日期：2025年6月4日</text>
						</view>
					</view>

					<!-- 第1章 -->
					<view class="content-section">
						<text class="section-title">1. 内容基本要求</text>
						<view class="section-item">
							<text class="item-number">1.1</text>
							<view class="item-content">
								<text class="item-title">合法合规</text>
								<text class="item-text">发布的内容必须符合国家法律法规，不得包含违法、违规信息。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.2</text>
							<view class="item-content">
								<text class="item-title">真实准确</text>
								<text class="item-text">发布的信息应当真实、准确，不得故意传播虚假信息或误导性内容。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.3</text>
							<view class="item-content">
								<text class="item-title">积极向上</text>
								<text class="item-text">鼓励发布积极向上、有价值的内容，传播正能量，营造良好的社区氛围。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.4</text>
							<view class="item-content">
								<text class="item-title">尊重他人</text>
								<text class="item-text">内容应当尊重他人的人格尊严、隐私权利，不得恶意攻击或诽谤他人。</text>
							</view>
						</view>
					</view>
					
					<!-- 第2章 -->
					<view class="content-section">
						<text class="section-title">2. 禁止发布的内容</text>
						<view class="section-item">
							<text class="item-number">2.1</text>
							<view class="item-content">
								<text class="item-title">违法违规内容</text>
								<text class="item-text">包括但不限于：涉及政治敏感话题、暴力恐怖、赌博诈骗、毒品交易等违法内容。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.2</text>
							<view class="item-content">
								<text class="item-title">不良信息</text>
								<text class="item-text">包括但不限于：色情低俗、血腥暴力、封建迷信、极端思想等有害信息。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.3</text>
							<view class="item-content">
								<text class="item-title">侵权内容</text>
								<text class="item-text">未经授权使用他人的文字、图片、视频等作品，侵犯他人知识产权的内容。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.4</text>
							<view class="item-content">
								<text class="item-title">垃圾信息</text>
								<text class="item-text">包括但不限于：恶意刷屏、重复发布、无意义内容、广告推销等垃圾信息。</text>
							</view>
						</view>
					</view>
					
					<!-- 第3章 -->
					<view class="content-section">
						<text class="section-title">3. 内容质量标准</text>
						<view class="section-item">
							<text class="item-number">3.1</text>
							<view class="item-content">
								<text class="item-title">原创优先</text>
								<text class="item-text">鼓励发布原创内容，包括原创文字、图片、视频等，提升内容的独特性和价值。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.2</text>
							<view class="item-content">
								<text class="item-title">内容完整</text>
								<text class="item-text">发布的内容应当完整、清晰，避免断章取义或信息不全的情况。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.3</text>
							<view class="item-content">
								<text class="item-title">表达规范</text>
								<text class="item-text">使用规范的语言文字，避免错别字、语法错误，保持良好的表达习惯。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.4</text>
							<view class="item-content">
								<text class="item-title">格式美观</text>
								<text class="item-text">注意内容的排版和格式，使用合适的段落分隔、标点符号，提升阅读体验。</text>
							</view>
						</view>
					</view>
					
					<!-- 第4章 -->
					<view class="content-section">
						<text class="section-title">4. 特殊内容规范</text>
						<view class="section-item">
							<text class="item-number">4.1</text>
							<view class="item-content">
								<text class="item-title">活动发布</text>
								<text class="item-text">发布活动信息时，应当提供详细、准确的活动信息，包括时间、地点、费用、注意事项等。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.2</text>
							<view class="item-content">
								<text class="item-title">商业推广</text>
								<text class="item-text">商业推广内容应当明确标识，遵循相关广告法规，不得进行虚假宣传。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.3</text>
							<view class="item-content">
								<text class="item-title">个人信息</text>
								<text class="item-text">谨慎发布个人敏感信息，保护自己和他人的隐私安全。</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'ContentStandards',
	data() {
		return {
			statusBarHeight: 0
		};
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 0;
	},
	methods: {
		goBack() {
			// 尝试返回上一页，如果没有上一页则跳转到社区公约页面
			const pages = getCurrentPages();
			if (pages.length > 1) {
				uni.navigateBack({
					delta: 1
				});
			} else {
				uni.redirectTo({
					url: '/pages/community-rules/community-rules'
				});
			}
		}
	}
};
</script>

<style lang="scss">
.page-container {
	min-height: 100vh;
	background: #f8f9fa;
	display: flex;
	flex-direction: column;
}

.status-bar {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
}

.header {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
	padding: 0 32rpx 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(255, 215, 0, 0.2);

	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;

		.back-button {
			width: 64rpx;
			height: 64rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50%;
			backdrop-filter: blur(10rpx);

			.back-icon {
				font-size: 36rpx;
				color: #333;
				font-weight: bold;
				line-height: 1;
			}
		}

		.header-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			flex: 1;
			text-align: center;
		}

		.header-placeholder {
			width: 64rpx;
		}
	}
}

.main-content {
	flex: 1;
	overflow: hidden;

	.scroll-container {
		height: 100%;
	}
}

.document-content {
	padding: 32rpx;

	.document-header {
		text-align: center;
		margin-bottom: 48rpx;
		padding: 48rpx 32rpx;
		background: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

		.document-title {
			font-size: 48rpx;
			font-weight: 700;
			color: #333;
			display: block;
			margin-bottom: 16rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}

		.document-subtitle {
			font-size: 28rpx;
			color: #999;
			display: block;
			margin-bottom: 32rpx;
			font-style: italic;
		}

		.document-info {
			display: flex;
			justify-content: center;
			flex-wrap: wrap;
			gap: 24rpx;

			.info-item {
				font-size: 24rpx;
				color: #666;
				padding: 8rpx 16rpx;
				background: #f8f9fa;
				border-radius: 8rpx;
			}
		}
	}

	.content-section {
		margin-bottom: 48rpx;
		background: #fff;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

		.section-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			display: block;
			margin-bottom: 32rpx;
			padding: 24rpx 32rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
			border-radius: 12rpx;
			text-align: center;
			box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.2);
		}

		.section-item {
			display: flex;
			margin-bottom: 24rpx;
			align-items: flex-start;

			&:last-child {
				margin-bottom: 0;
			}

			.item-number {
				font-size: 28rpx;
				color: #FFD700;
				font-weight: 600;
				margin-right: 20rpx;
				flex-shrink: 0;
				margin-top: 4rpx;
				min-width: 60rpx;
			}

			.item-content {
				flex: 1;
				line-height: 1.6;

				.item-title {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
					display: block;
					margin-bottom: 12rpx;
				}

				.item-text {
					font-size: 28rpx;
					color: #666;
					line-height: 1.8;
					text-align: justify;
				}
			}
		}
	}
}
</style>
