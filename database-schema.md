# 数据库字段设计

## 用户表 (uni-id-users)

请在uniCloud控制台的数据库中添加以下字段到 `uni-id-users` 表：

### 基础字段 (uni-id自带)
- `_id`: 用户唯一标识 (自动生成)
- `username`: 用户名
- `mobile`: 手机号
- `password`: 密码 (加密存储)
- `register_date`: 注册时间
- `last_login_date`: 最后登录时间

### 自定义字段 (需要添加)
```json
{
  "planet_id": {
    "type": "string",
    "description": "7位数唯一ID",
    "required": true,
    "unique": true
  },
  "nickname": {
    "type": "string", 
    "description": "用户昵称",
    "default": ""
  },
  "avatar": {
    "type": "string",
    "description": "头像URL",
    "default": "/static/default-avatar.png"
  },
  "gender": {
    "type": "string",
    "description": "性别",
    "enum": ["male", "female", "unknown"],
    "default": "unknown"
  },
  "location": {
    "type": "string",
    "description": "所在地区",
    "default": ""
  },
  "ip_location": {
    "type": "string", 
    "description": "IP属地",
    "default": ""
  },
  "bio": {
    "type": "string",
    "description": "个人简介", 
    "default": ""
  },
  "level": {
    "type": "number",
    "description": "用户等级",
    "default": 1
  },
  "exp": {
    "type": "number",
    "description": "经验值",
    "default": 0
  },
  "is_verified": {
    "type": "boolean",
    "description": "是否实名认证",
    "default": false
  },
  "is_vip": {
    "type": "boolean", 
    "description": "是否VIP会员",
    "default": false
  },
  "vip_level": {
    "type": "number",
    "description": "VIP等级",
    "default": 0
  },
  "followers_count": {
    "type": "number",
    "description": "粉丝数",
    "default": 0
  },
  "following_count": {
    "type": "number",
    "description": "关注数", 
    "default": 0
  },
  "likes_count": {
    "type": "number",
    "description": "获赞数",
    "default": 0
  },
  "posts_count": {
    "type": "number",
    "description": "发布数",
    "default": 0
  }
}
```

## 验证码表 (verification_codes)

创建新表 `verification_codes`：

```json
{
  "_id": "自动生成",
  "code": {
    "type": "string",
    "description": "4位验证码",
    "required": true
  },
  "mobile": {
    "type": "string", 
    "description": "手机号",
    "required": true
  },
  "type": {
    "type": "string",
    "description": "验证码类型",
    "enum": ["register", "login", "reset"],
    "required": true
  },
  "expires_at": {
    "type": "date",
    "description": "过期时间",
    "required": true
  },
  "used": {
    "type": "boolean",
    "description": "是否已使用",
    "default": false
  },
  "created_at": {
    "type": "date",
    "description": "创建时间",
    "default": "now"
  }
}
```

## 操作步骤

1. 登录 uniCloud 控制台
2. 进入数据库管理
3. 找到 `uni-id-users` 表，添加上述自定义字段
4. 创建新表 `verification_codes`，添加相应字段
5. 设置索引：
   - `uni-id-users.planet_id` 设为唯一索引
   - `verification_codes.mobile` 设为普通索引
   - `verification_codes.expires_at` 设为TTL索引（自动删除过期数据）
