<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弧形指示器预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 20px 20px;
            color: white;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        /* 标签导航 */
        .tab-navigation {
            padding: 10px 0;
            background: #fff;
            margin: 10px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        }

        .tab-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: relative;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 5px;
            position: relative;
            flex: 1;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-text {
            font-size: 14px;
            color: #666;
            font-weight: 400;
            transition: all 0.3s ease;
        }

        .tab-item.active .tab-text {
            color: #333;
            font-size: 16px;
            font-weight: 600;
        }

        /* 弧形指示器 */
        .tab-indicator {
            position: absolute;
            bottom: 0px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 10px;
            overflow: hidden;
            animation: slideIn 0.3s ease;
        }

        .tab-indicator::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 20px;
            background: linear-gradient(90deg, #BBB2FF 0%, #E3D2FF 50%, #A7C0FF 100%);
            border-radius: 50%;
        }

        @keyframes slideIn {
            from {
                width: 0;
                opacity: 0;
            }
            to {
                width: 30px;
                opacity: 1;
            }
        }
        
        .content {
            padding: 20px;
            text-align: center;
            color: #666;
        }
        
        /* 弧形演示 */
        .arc-demo {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .arc-title {
            font-weight: bold;
            margin-bottom: 20px;
            font-size: 16px;
            text-align: center;
        }
        
        .arc-examples {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .arc-example {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: #fff;
            border-radius: 8px;
        }
        
        .arc-label {
            font-size: 14px;
            color: #666;
        }
        
        .arc-shape {
            position: relative;
        }
        
        /* 不同的弧形样式 */
        .arc-straight {
            width: 30px;
            height: 3px;
            background: linear-gradient(90deg, #BBB2FF 0%, #E3D2FF 50%, #A7C0FF 100%);
            border-radius: 1.5px;
        }
        
        .arc-curved {
            width: 30px;
            height: 10px;
            overflow: hidden;
        }
        
        .arc-curved::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 20px;
            background: linear-gradient(90deg, #BBB2FF 0%, #E3D2FF 50%, #A7C0FF 100%);
            border-radius: 50%;
        }
        
        .arc-u-shape {
            width: 30px;
            height: 6px;
            background: linear-gradient(90deg, #BBB2FF 0%, #E3D2FF 50%, #A7C0FF 100%);
            border-radius: 0 0 30px 30px;
        }
        
        .correct {
            border: 2px solid #4caf50;
            background: #e8f5e8 !important;
        }
        
        .demo-note {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.5;
            text-align: left;
        }
        
        .user-drawing {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
        }
        
        .drawing-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        
        .drawing-desc {
            color: #856404;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>趣嗒同行</h1>
            <p>弧形指示器效果</p>
        </div>
        
        <!-- 标签导航 -->
        <div class="tab-navigation">
            <div class="tab-container">
                <div class="tab-item active" onclick="switchTab(this, 0)">
                    <span class="tab-text">首页推荐</span>
                    <div class="tab-indicator"></div>
                </div>
                <div class="tab-item" onclick="switchTab(this, 1)">
                    <span class="tab-text">组局约伴</span>
                </div>
                <div class="tab-item" onclick="switchTab(this, 2)">
                    <span class="tab-text">城市玩伴</span>
                </div>
                <div class="tab-item" onclick="switchTab(this, 3)">
                    <span class="tab-text">游戏玩伴</span>
                </div>
            </div>
        </div>
        
        <div class="content">
            <h3 id="content-title">首页推荐</h3>
            <p>当前选中的标签内容</p>
            
            <div class="user-drawing">
                <div class="drawing-title">📝 用户手绘效果</div>
                <div class="drawing-desc">
                    根据你画的弧形，应该是一个简单的弧形线条<br>
                    不是椭圆，不是U型，就是自然的弧形
                </div>
            </div>
            
            <!-- 弧形对比 -->
            <div class="arc-demo">
                <div class="arc-title">🎯 弧形效果对比</div>
                <div class="arc-examples">
                    <div class="arc-example">
                        <div class="arc-label">❌ 直线</div>
                        <div class="arc-shape">
                            <div class="arc-straight"></div>
                        </div>
                    </div>
                    
                    <div class="arc-example">
                        <div class="arc-label">❌ U型</div>
                        <div class="arc-shape">
                            <div class="arc-u-shape"></div>
                        </div>
                    </div>
                    
                    <div class="arc-example correct">
                        <div class="arc-label">✅ 自然弧形</div>
                        <div class="arc-shape">
                            <div class="arc-curved"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="demo-note">
                <strong>✅ 弧形指示器实现原理：</strong><br>
                • 容器：宽度30px，高度10px，overflow: hidden<br>
                • 伪元素：宽度30px，高度20px，border-radius: 50%<br>
                • 位置：伪元素底部对齐，只显示上半部分<br>
                • 效果：形成自然的弧形线条<br>
                • 渐变色：#BBB2FF → #E3D2FF → #A7C0FF
            </div>
        </div>
    </div>

    <script>
        const tabNames = ['首页推荐', '组局约伴', '城市玩伴', '游戏玩伴'];
        
        function switchTab(element, index) {
            // 移除所有active状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
                const indicator = item.querySelector('.tab-indicator');
                if (indicator) {
                    indicator.remove();
                }
            });
            
            // 添加active状态到当前选中的标签
            element.classList.add('active');
            
            // 添加指示器
            const indicator = document.createElement('div');
            indicator.className = 'tab-indicator';
            element.appendChild(indicator);
            
            // 更新内容标题
            document.getElementById('content-title').textContent = tabNames[index];
        }
    </script>
</body>
</html>
