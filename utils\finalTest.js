/**
 * 最终测试和验证工具
 * 用于验证所有网络修复是否生效
 */

import { comprehensiveNetworkDiagnosis } from './networkTest.js'

// 测试配置
const TEST_CONFIG = {
	// 测试手机号（用于注册测试）
	testMobile: '13800138000',
	// 测试密码
	testPassword: '123456',
	// 测试用户名
	testUsername: '测试用户',
	// 测试超时时间
	timeout: 30000
}

/**
 * 执行完整的功能测试
 */
export async function runCompleteTest() {
	console.log('开始执行完整功能测试...')
	
	const testResults = {
		timestamp: new Date().toISOString(),
		tests: {},
		summary: {
			total: 0,
			passed: 0,
			failed: 0
		},
		overall: false
	}
	
	try {
		// 1. 网络诊断测试
		console.log('1. 执行网络诊断测试...')
		testResults.tests.networkDiagnosis = await testNetworkDiagnosis()
		testResults.summary.total++
		
		// 2. 云函数连接测试
		console.log('2. 执行云函数连接测试...')
		testResults.tests.cloudFunctionTest = await testCloudFunctions()
		testResults.summary.total++
		
		// 3. 数据库操作测试
		console.log('3. 执行数据库操作测试...')
		testResults.tests.databaseTest = await testDatabaseOperations()
		testResults.summary.total++
		
		// 4. 用户注册流程测试
		console.log('4. 执行用户注册流程测试...')
		testResults.tests.registrationTest = await testUserRegistration()
		testResults.summary.total++
		
		// 5. 用户登录流程测试
		console.log('5. 执行用户登录流程测试...')
		testResults.tests.loginTest = await testUserLogin()
		testResults.summary.total++
		
		// 统计结果
		Object.values(testResults.tests).forEach(test => {
			if (test.success) {
				testResults.summary.passed++
			} else {
				testResults.summary.failed++
			}
		})
		
		testResults.overall = testResults.summary.failed === 0
		
		console.log('完整功能测试完成:', testResults.summary)
		return testResults
		
	} catch (error) {
		console.error('完整功能测试失败:', error)
		return {
			...testResults,
			error: error.message,
			overall: false
		}
	}
}

/**
 * 测试网络诊断功能
 */
async function testNetworkDiagnosis() {
	try {
		const diagnosis = await comprehensiveNetworkDiagnosis()
		
		return {
			success: diagnosis.analysis.overall !== 'critical',
			data: diagnosis,
			message: `网络诊断${diagnosis.analysis.overall === 'critical' ? '发现严重问题' : '正常'}`
		}
	} catch (error) {
		return {
			success: false,
			error: error.message,
			message: '网络诊断测试失败'
		}
	}
}

/**
 * 测试云函数连接
 */
async function testCloudFunctions() {
	try {
		const functions = ['simple-test', 'auth', 'test-db']
		const results = {}
		let successCount = 0
		
		for (const funcName of functions) {
			try {
				const result = await uniCloud.callFunction({
					name: funcName,
					data: { test: 'final-test' }
				})
				
				results[funcName] = {
					success: true,
					response: result.result
				}
				successCount++
			} catch (error) {
				results[funcName] = {
					success: false,
					error: error.message
				}
			}
		}
		
		return {
			success: successCount === functions.length,
			data: results,
			message: `${successCount}/${functions.length} 个云函数测试通过`
		}
	} catch (error) {
		return {
			success: false,
			error: error.message,
			message: '云函数连接测试失败'
		}
	}
}

/**
 * 测试数据库操作
 */
async function testDatabaseOperations() {
	try {
		// 测试连接
		const connectionTest = await uniCloud.callFunction({
			name: 'test-db',
			data: { action: 'test-connection' }
		})
		
		if (connectionTest.result.code !== 0) {
			throw new Error('数据库连接测试失败')
		}
		
		// 测试插入
		const insertTest = await uniCloud.callFunction({
			name: 'test-db',
			data: { action: 'test-insert' }
		})
		
		if (insertTest.result.code !== 0) {
			throw new Error('数据库插入测试失败')
		}
		
		// 清理测试数据
		await uniCloud.callFunction({
			name: 'test-db',
			data: { action: 'cleanup-test' }
		})
		
		return {
			success: true,
			data: {
				connection: connectionTest.result,
				insert: insertTest.result
			},
			message: '数据库操作测试通过'
		}
	} catch (error) {
		return {
			success: false,
			error: error.message,
			message: '数据库操作测试失败'
		}
	}
}

/**
 * 测试用户注册流程
 */
async function testUserRegistration() {
	try {
		// 生成测试手机号
		const testMobile = '138' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0')
		
		// 1. 检查用户是否存在
		const checkResult = await uniCloud.callFunction({
			name: 'auth',
			data: {
				action: 'checkUserExists',
				mobile: testMobile
			}
		})
		
		if (checkResult.result.code !== 0) {
			throw new Error('用户存在性检查失败')
		}
		
		// 2. 获取验证码
		const codeResult = await uniCloud.callFunction({
			name: 'auth',
			data: {
				action: 'getVerificationCode',
				mobile: testMobile,
				type: 'register'
			}
		})
		
		if (codeResult.result.code !== 0) {
			throw new Error('获取验证码失败')
		}
		
		// 3. 注册用户
		const registerResult = await uniCloud.callFunction({
			name: 'auth',
			data: {
				action: 'register',
				username: TEST_CONFIG.testUsername,
				mobile: testMobile,
				code: codeResult.result.data.verificationCode,
				password: TEST_CONFIG.testPassword
			}
		})
		
		if (registerResult.result.code !== 0) {
			throw new Error('用户注册失败')
		}
		
		return {
			success: true,
			data: {
				mobile: testMobile,
				userInfo: registerResult.result.data
			},
			message: '用户注册流程测试通过'
		}
	} catch (error) {
		return {
			success: false,
			error: error.message,
			message: '用户注册流程测试失败'
		}
	}
}

/**
 * 测试用户登录流程
 */
async function testUserLogin() {
	try {
		// 使用预设的测试账号登录
		const loginResult = await uniCloud.callFunction({
			name: 'auth',
			data: {
				action: 'login',
				mobile: TEST_CONFIG.testMobile,
				password: TEST_CONFIG.testPassword
			}
		})
		
		// 如果测试账号不存在，先创建
		if (loginResult.result.code !== 0) {
			// 创建测试账号
			const codeResult = await uniCloud.callFunction({
				name: 'auth',
				data: {
					action: 'getVerificationCode',
					mobile: TEST_CONFIG.testMobile,
					type: 'register'
				}
			})
			
			if (codeResult.result.code === 0) {
				await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'register',
						username: TEST_CONFIG.testUsername,
						mobile: TEST_CONFIG.testMobile,
						code: codeResult.result.data.verificationCode,
						password: TEST_CONFIG.testPassword
					}
				})
				
				// 重新尝试登录
				const retryLogin = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'login',
						mobile: TEST_CONFIG.testMobile,
						password: TEST_CONFIG.testPassword
					}
				})
				
				if (retryLogin.result.code !== 0) {
					throw new Error('登录测试失败')
				}
				
				return {
					success: true,
					data: retryLogin.result,
					message: '用户登录流程测试通过（新建账号）'
				}
			}
		}
		
		return {
			success: true,
			data: loginResult.result,
			message: '用户登录流程测试通过'
		}
	} catch (error) {
		return {
			success: false,
			error: error.message,
			message: '用户登录流程测试失败'
		}
	}
}

/**
 * 显示测试结果
 */
export function showTestResults(results) {
	const { summary, tests } = results
	
	let title = '功能测试完成'
	let content = `总计: ${summary.total}\n通过: ${summary.passed}\n失败: ${summary.failed}\n\n`
	
	// 添加详细结果
	Object.keys(tests).forEach(testName => {
		const test = tests[testName]
		const status = test.success ? '✓' : '✗'
		const name = getTestDisplayName(testName)
		content += `${status} ${name}: ${test.message}\n`
	})
	
	if (!results.overall) {
		content += '\n建议：\n1. 检查网络连接\n2. 确认云函数部署\n3. 验证数据库配置\n4. 重新运行测试'
	}
	
	uni.showModal({
		title: title,
		content: content,
		showCancel: false,
		confirmText: '知道了'
	})
}

/**
 * 获取测试显示名称
 */
function getTestDisplayName(testName) {
	const names = {
		networkDiagnosis: '网络诊断',
		cloudFunctionTest: '云函数连接',
		databaseTest: '数据库操作',
		registrationTest: '用户注册',
		loginTest: '用户登录'
	}
	return names[testName] || testName
}
