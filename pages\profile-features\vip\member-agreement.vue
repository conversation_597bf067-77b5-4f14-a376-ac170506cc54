<template>
	<view class="agreement-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<uni-icons type="left" size="24" color="#FFD700"></uni-icons>
					<text class="back-text">返回</text>
				</view>
				<text class="nav-title">会员服务协议</text>
				<view class="nav-right"></view>
			</view>
		</view>

		<!-- 协议内容 -->
		<scroll-view class="content-scroll" scroll-y="true">
			<view class="agreement-content">
				<!-- 协议标题 -->
				<view class="agreement-header">
					<text class="agreement-title">闲伴会员服务协议</text>
					<view class="agreement-meta">
						<text class="version">版本号：v2.1</text>
						<text class="publish-date">发布日期：2024年12月20日</text>
						<text class="effective-date">生效日期：2024年12月25日</text>
					</view>
				</view>

				<!-- 协议正文 -->
				<view class="agreement-body">
					<!-- 前言 -->
					<view class="section">
						<text class="section-title">前言</text>
						<text class="section-content">
							欢迎使用闲伴会员服务！本协议是您（用户）与上海闲伴网络科技有限公司（以下简称"我们"或"闲伴"）之间关于闲伴会员服务使用的法律协议。请您仔细阅读本协议的全部条款，特别是免除或者限制责任的条款、法律适用和争议解决条款。如您不同意本协议的任意内容，请不要注册、使用闲伴会员服务。
						</text>
					</view>

					<!-- 第一条：定义 -->
					<view class="section">
						<text class="section-title">第一条 定义</text>
						<view class="subsection">
							<text class="subsection-title">1.1 闲伴会员服务</text>
							<text class="subsection-content">
								指由闲伴提供的付费增值服务，包括但不限于专属身份标识、优先匹配推荐、高级筛选功能、去除广告、专属客服通道等特权服务。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">1.2 会员等级</text>
							<text class="subsection-content">
								闲伴会员服务分为黄金会员、钻石会员、白金会员、黑金会员四个等级，不同等级享有不同的服务内容和特权。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">1.3 会员期限</text>
							<text class="subsection-content">
								指用户购买会员服务的有效期限，包括月度会员（1个月）、季度会员（3个月）、年度会员（12个月）等不同时长选项。
							</text>
						</view>
					</view>

					<!-- 第二条：服务内容 -->
					<view class="section">
						<text class="section-title">第二条 服务内容</text>
						<view class="subsection">
							<text class="subsection-title">2.1 基础服务</text>
							<text class="subsection-content">
								所有会员等级均享有：专属身份标识、优先匹配推荐、基础筛选功能、去除广告等基础特权。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">2.2 进阶服务</text>
							<text class="subsection-content">
								钻石及以上会员享有：无限点赞特权、高级筛选功能、专属客服通道、消息已读显示等进阶特权。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">2.3 高级服务</text>
							<text class="subsection-content">
								白金及以上会员享有：数据统计分析、专属活动邀请、隐身浏览模式、超级曝光等高级特权。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">2.4 至尊服务</text>
							<text class="subsection-content">
								黑金会员专享：专属管家服务、定制化推荐、线下活动优先权、至尊身份标识等顶级特权。
							</text>
						</view>
					</view>

					<!-- 第三条：费用与支付 -->
					<view class="section">
						<text class="section-title">第三条 费用与支付</text>
						<view class="subsection">
							<text class="subsection-title">3.1 费用标准</text>
							<text class="subsection-content">
								会员服务费用按照闲伴平台公布的价格标准执行，具体价格以购买时显示的金额为准。闲伴有权根据市场情况调整价格，调整后的价格对新购买用户生效。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">3.2 支付方式</text>
							<text class="subsection-content">
								支持微信支付、支付宝等第三方支付平台。用户应确保支付账户安全，因用户支付账户问题导致的损失由用户自行承担。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">3.3 发票服务</text>
							<text class="subsection-content">
								用户可在购买后申请电子发票，发票内容为"信息服务费"。发票申请需在购买后30天内提出，逾期不予受理。
							</text>
						</view>
					</view>

					<!-- 第四条：会员权益 -->
					<view class="section">
						<text class="section-title">第四条 会员权益</text>
						<view class="subsection">
							<text class="subsection-title">4.1 权益生效</text>
							<text class="subsection-content">
								会员权益自付款成功后立即生效，有效期按购买的会员时长计算。会员到期后，相关特权自动失效。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">4.2 权益变更</text>
							<text class="subsection-content">
								闲伴有权根据业务发展需要调整会员权益内容，调整前会提前30天通知用户。权益调整不影响用户已购买的会员服务。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">4.3 权益限制</text>
							<text class="subsection-content">
								会员权益仅限本人使用，不得转让、出售或以其他方式提供给第三方使用。违反此规定的，闲伴有权终止会员服务。
							</text>
						</view>
					</view>

					<!-- 第五条：续费与退费 -->
					<view class="section">
						<text class="section-title">第五条 续费与退费</text>
						<view class="subsection">
							<text class="subsection-title">5.1 自动续费</text>
							<text class="subsection-content">
								用户可选择开启自动续费功能，系统将在会员到期前自动扣费续费。用户可随时关闭自动续费功能，关闭后不影响当前会员期限。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">5.2 退费政策</text>
							<text class="subsection-content">
								会员服务一经购买，原则上不予退费。但在以下情况下，用户可申请退费：
								<text class="list-item">• 因闲伴系统故障导致无法正常使用会员服务</text>
								<text class="list-item">• 重复购买同一会员服务</text>
								<text class="list-item">• 其他因闲伴原因导致的服务异常</text>
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">5.3 退费流程</text>
							<text class="subsection-content">
								用户需在购买后7天内联系客服申请退费，提供相关证明材料。闲伴将在收到申请后5个工作日内处理，退费金额将原路返回用户支付账户。
							</text>
						</view>
					</view>

					<!-- 第六条：用户义务 -->
					<view class="section">
						<text class="section-title">第六条 用户义务</text>
						<view class="subsection">
							<text class="subsection-title">6.1 合规使用</text>
							<text class="subsection-content">
								用户应遵守国家法律法规，不得利用会员服务从事违法违规活动。包括但不限于：发布违法信息、侵犯他人权益、进行欺诈活动等。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">6.2 账户安全</text>
							<text class="subsection-content">
								用户应妥善保管账户信息，不得将会员账户借给他人使用。因用户账户安全问题导致的损失由用户自行承担。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">6.3 真实信息</text>
							<text class="subsection-content">
								用户应提供真实、准确的个人信息，如信息发生变更应及时更新。提供虚假信息的，闲伴有权终止会员服务。
							</text>
						</view>
					</view>

					<!-- 第七条：服务限制 -->
					<view class="section">
						<text class="section-title">第七条 服务限制</text>
						<view class="subsection">
							<text class="subsection-title">7.1 技术限制</text>
							<text class="subsection-content">
								会员服务可能因技术维护、系统升级等原因暂时中断，闲伴将尽力减少服务中断时间，但不承担因此造成的损失。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">7.2 使用限制</text>
							<text class="subsection-content">
								用户不得通过技术手段破解、修改会员服务，不得恶意刷取会员权益。违反者将被终止会员服务且不予退费。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">7.3 地域限制</text>
							<text class="subsection-content">
								会员服务仅在中国大陆地区提供，用户在境外使用可能受到限制。因地域限制导致的服务异常不在退费范围内。
							</text>
						</view>
					</view>

					<!-- 第八条：知识产权 -->
					<view class="section">
						<text class="section-title">第八条 知识产权</text>
						<view class="subsection">
							<text class="subsection-title">8.1 平台权益</text>
							<text class="subsection-content">
								闲伴平台的所有内容，包括但不限于文字、图片、音频、视频、软件、程序、版面设计等均受知识产权法保护，归闲伴或相关权利人所有。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">8.2 使用授权</text>
							<text class="subsection-content">
								用户购买会员服务仅获得服务使用权，不获得任何知识产权。用户不得复制、传播、修改平台内容用于商业用途。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">8.3 侵权处理</text>
							<text class="subsection-content">
								如发现侵犯知识产权行为，闲伴有权立即停止相关服务，并保留追究法律责任的权利。
							</text>
						</view>
					</view>

					<!-- 第九条：隐私保护 -->
					<view class="section">
						<text class="section-title">第九条 隐私保护</text>
						<view class="subsection">
							<text class="subsection-title">9.1 信息收集</text>
							<text class="subsection-content">
								为提供会员服务，闲伴可能收集用户的基本信息、使用行为数据等。具体收集内容详见《隐私政策》。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">9.2 信息使用</text>
							<text class="subsection-content">
								收集的用户信息仅用于提供会员服务、改善用户体验、进行数据分析等合法用途，不会用于其他商业目的。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">9.3 信息保护</text>
							<text class="subsection-content">
								闲伴采用行业标准的安全措施保护用户信息，但不能保证绝对安全。用户应配合做好信息保护工作。
							</text>
						</view>
					</view>

					<!-- 第十条：责任限制 -->
					<view class="section">
						<text class="section-title">第十条 责任限制</text>
						<view class="subsection">
							<text class="subsection-title">10.1 服务性质</text>
							<text class="subsection-content">
								会员服务为增值服务，闲伴尽力提供稳定、优质的服务，但不保证服务不中断、无错误。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">10.2 损失承担</text>
							<text class="subsection-content">
								除法律明确规定外，闲伴对因使用会员服务产生的任何直接、间接、偶然、特殊或后果性损失不承担责任。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">10.3 赔偿限额</text>
							<text class="subsection-content">
								在任何情况下，闲伴的赔偿责任不超过用户已支付的会员服务费用总额。
							</text>
						</view>
					</view>

					<!-- 第十一条：协议变更 -->
					<view class="section">
						<text class="section-title">第十一条 协议变更</text>
						<view class="subsection">
							<text class="subsection-title">11.1 变更权利</text>
							<text class="subsection-content">
								闲伴有权根据业务发展需要修改本协议，修改后的协议将在平台公布，用户继续使用服务视为同意修改后的协议。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">11.2 通知义务</text>
							<text class="subsection-content">
								协议修改将提前30天通过平台公告、站内信等方式通知用户。重大变更将通过邮件或短信方式通知。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">11.3 异议处理</text>
							<text class="subsection-content">
								用户对协议修改有异议的，可在修改生效前停止使用服务。继续使用服务的，视为接受修改后的协议。
							</text>
						</view>
					</view>

					<!-- 第十二条：争议解决 -->
					<view class="section">
						<text class="section-title">第十二条 争议解决</text>
						<view class="subsection">
							<text class="subsection-title">12.1 协商解决</text>
							<text class="subsection-content">
								因本协议产生的争议，双方应首先通过友好协商解决。协商不成的，可通过以下方式解决争议。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">12.2 管辖法院</text>
							<text class="subsection-content">
								本协议的签订、履行、解释及争议解决均适用中华人民共和国法律。争议由上海市崇明区人民法院管辖。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">12.3 法律适用</text>
							<text class="subsection-content">
								本协议条款如与法律法规冲突，以法律法规为准，但不影响其他条款的效力。
							</text>
						</view>
					</view>

					<!-- 第十三条：其他条款 -->
					<view class="section">
						<text class="section-title">第十三条 其他条款</text>
						<view class="subsection">
							<text class="subsection-title">13.1 协议生效</text>
							<text class="subsection-content">
								本协议自用户点击同意并完成会员服务购买时生效，至会员服务期满或协议终止时失效。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">13.2 可分割性</text>
							<text class="subsection-content">
								本协议任何条款被认定无效或不可执行，不影响其他条款的效力。无效条款将被有效的类似条款替代。
							</text>
						</view>
						<view class="subsection">
							<text class="subsection-title">13.3 联系方式</text>
							<text class="subsection-content">
								如对本协议有任何疑问，请联系闲伴客服：
								<text class="list-item">• 客服邮箱：<EMAIL></text>
								<text class="list-item">• 客服电话：400-888-0000</text>
								<text class="list-item">• 公司地址：上海市崇明区北沿公路2111号3幢</text>
							</text>
						</view>
					</view>

					<!-- 协议结尾 -->
					<view class="agreement-footer">
						<text class="footer-text">
							感谢您选择闲伴会员服务！我们将竭诚为您提供优质的服务体验。
						</text>
						<view class="company-info">
							<text class="company-name">上海闲伴网络科技有限公司</text>
							<text class="update-date">最后更新：2024年12月20日</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0
			}
		},
		
		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},
		
		methods: {
			goBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style lang="scss" scoped>
	.agreement-page {
		background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 100%);
		min-height: 100vh;
		color: #fff;
	}

	.status-bar {
		background: transparent;
	}

	.header {
		position: sticky;
		top: 0;
		z-index: 100;
		background: rgba(10, 10, 10, 0.95);
		backdrop-filter: blur(20rpx);
		border-bottom: 1rpx solid rgba(255, 215, 0, 0.2);

		.nav-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;
			height: 88rpx;

			.nav-left {
				display: flex;
				align-items: center;
				gap: 8rpx;
				flex: 1;

				.back-text {
					font-size: 28rpx;
					color: #FFD700;
					font-weight: 500;
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: #fff;
				font-weight: 600;
				text-align: center;
				flex: 2;
			}

			.nav-right {
				flex: 1;
			}
		}
	}

	.content-scroll {
		height: calc(100vh - 88rpx);
	}

	.agreement-content {
		padding: 32rpx 24rpx 120rpx;

		.agreement-header {
			text-align: center;
			margin-bottom: 48rpx;
			padding-bottom: 32rpx;
			border-bottom: 2rpx solid rgba(255, 215, 0, 0.2);

			.agreement-title {
				font-size: 48rpx;
				color: #FFD700;
				font-weight: 700;
				display: block;
				margin-bottom: 24rpx;
				text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
			}

			.agreement-meta {
				display: flex;
				flex-direction: column;
				gap: 8rpx;

				text {
					font-size: 24rpx;
					color: rgba(255, 255, 255, 0.6);
				}
			}
		}

		.agreement-body {
			.section {
				margin-bottom: 48rpx;

				.section-title {
					font-size: 36rpx;
					color: #FFD700;
					font-weight: 700;
					display: block;
					margin-bottom: 24rpx;
					padding-left: 16rpx;
					border-left: 6rpx solid #FFD700;
				}

				.section-content {
					font-size: 28rpx;
					color: rgba(255, 255, 255, 0.9);
					line-height: 1.8;
					display: block;
					margin-bottom: 24rpx;
					text-align: justify;
				}

				.subsection {
					margin-bottom: 32rpx;
					padding-left: 24rpx;

					.subsection-title {
						font-size: 30rpx;
						color: #fff;
						font-weight: 600;
						display: block;
						margin-bottom: 16rpx;
					}

					.subsection-content {
						font-size: 26rpx;
						color: rgba(255, 255, 255, 0.8);
						line-height: 1.7;
						display: block;
						text-align: justify;

						.list-item {
							display: block;
							margin: 12rpx 0;
							padding-left: 24rpx;
							position: relative;
						}
					}
				}
			}

			.agreement-footer {
				margin-top: 64rpx;
				padding: 32rpx;
				background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
				border: 1rpx solid rgba(255, 215, 0, 0.2);
				border-radius: 16rpx;
				text-align: center;

				.footer-text {
					font-size: 28rpx;
					color: rgba(255, 255, 255, 0.9);
					line-height: 1.6;
					display: block;
					margin-bottom: 24rpx;
				}

				.company-info {
					display: flex;
					flex-direction: column;
					gap: 8rpx;

					.company-name {
						font-size: 26rpx;
						color: #FFD700;
						font-weight: 600;
					}

					.update-date {
						font-size: 22rpx;
						color: rgba(255, 255, 255, 0.6);
					}
				}
			}
		}
	}
</style>
