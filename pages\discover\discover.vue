<template>
	<view class="discover-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<!-- 顶部导航栏 -->
		<view class="top-header">
			<!-- 左侧菜单切换 -->
			<view class="nav-tabs">
				<!-- 菜单图标（仅在推荐页面显示） -->
				<view v-if="currentTab === 'recommend'" class="menu-icon" @click="showSideMenu">
					<view class="menu-line"></view>
					<view class="menu-line"></view>
					<view class="menu-line"></view>
				</view>

				<view class="nav-tab" v-for="(tab, index) in navTabs" :key="tab.id" :class="{ active: currentTab === tab.id }" @click="switchTab(tab.id)"
				>
					<text class="tab-text">{{ tab.name }}</text>
					<view class="tab-indicator" v-if="currentTab === tab.id"></view>
				</view>
			</view>

			<!-- 右侧功能按钮 -->
			<view class="header-actions">
				<view class="action-btn" @click="goToSearch">
					<uni-icons type="search" size="20" color="#333"></uni-icons>
				</view>
				<view class="action-btn publish-btn" @click="goToPublish">
					<uni-icons type="plus" size="20" color="#fff"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 推荐页面的子菜单 -->
		<view v-if="currentTab === 'recommend'" class="sub-menu-container">
			<scroll-view class="sub-menu-scroll" scroll-x>
				<view class="sub-menu-list">
					<view class="sub-menu-item" v-for="(item, index) in recommendSubMenus" :key="item.id" :class="{ active: currentSubMenu === item.id }" @click="switchSubMenu(item.id)"
					>
						<text class="sub-menu-text">{{ item.name }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 内容区域 -->
		<view class="content-container">
			<!-- 推荐页面 -->
			<view v-if="currentTab === 'recommend'" class="tab-content">
				<view class="waterfall-container">
					<view class="waterfall-column">
						<view class="post-card" v-for="(post, index) in leftColumnPosts" :key="'left-' + post.id" @click="viewPost(post)">
							<view class="post-image-container">
								<image class="post-image" :src="post.image" mode="aspectFill"></image>
								<view class="post-overlay">
									<view class="post-stats">
										<view class="stat-item">
											<uni-icons type="heart" size="14" color="#fff"></uni-icons>
											<text class="stat-text">{{ post.likes }}</text>
										</view>
									</view>
								</view>
							</view>
							<view class="post-info">
								<text class="post-title">{{ post.title }}</text>
								<view class="post-author">
									<image class="author-avatar" :src="post.author.avatar" mode="aspectFill"></image>
									<text class="author-name">{{ post.author.name }}</text>
								</view>
							</view>
						</view>
					</view>

					<view class="waterfall-column">
						<view class="post-card" v-for="(post, index) in rightColumnPosts" :key="'right-' + post.id" @click="viewPost(post)">
							<view class="post-image-container">
								<image class="post-image" :src="post.image" mode="aspectFill"></image>
								<view class="post-overlay">
									<view class="post-stats">
										<view class="stat-item">
											<uni-icons type="heart" size="14" color="#fff"></uni-icons>
											<text class="stat-text">{{ post.likes }}</text>
										</view>
									</view>
								</view>
							</view>
							<view class="post-info">
								<text class="post-title">{{ post.title }}</text>
								<view class="post-author">
									<image class="author-avatar" :src="post.author.avatar" mode="aspectFill"></image>
									<text class="author-name">{{ post.author.name }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 朋友页面 -->
			<view v-if="currentTab === 'friends'" class="tab-content">
				<view class="empty-state">
					<view class="empty-icon">
						<uni-icons type="person" size="60" color="#ccc"></uni-icons>
					</view>
					<text class="empty-text">暂无朋友动态</text>
					<text class="empty-desc">快去添加好友，看看他们的精彩分享吧</text>
				</view>
			</view>

			<!-- 关注页面 -->
			<view v-if="currentTab === 'following'" class="tab-content">
				<view class="empty-state">
					<view class="empty-icon">
						<uni-icons type="star" size="60" color="#ccc"></uni-icons>
					</view>
					<text class="empty-text">暂无关注动态</text>
					<text class="empty-desc">关注感兴趣的用户，获取最新动态</text>
				</view>
			</view>

			<!-- 精选页面 -->
			<view v-if="currentTab === 'featured'" class="tab-content">
				<view class="featured-list">
					<view class="featured-card" v-for="post in featuredPosts" :key="post.id" @click="viewPost(post)">
						<image class="featured-image" :src="post.image" mode="aspectFill"></image>
						<view class="featured-content">
							<text class="featured-title">{{ post.title }}</text>
							<text class="featured-desc">{{ post.description }}</text>
							<view class="featured-meta">
								<view class="featured-author">
									<image class="author-avatar" :src="post.author.avatar" mode="aspectFill"></image>
									<text class="author-name">{{ post.author.name }}</text>
								</view>
								<view class="featured-stats">
									<view class="stat-item">
										<uni-icons type="heart" size="14" color="#999"></uni-icons>
										<text class="stat-text">{{ post.likes }}</text>
									</view>
									<view class="stat-item">
										<uni-icons type="chat" size="14" color="#999"></uni-icons>
										<text class="stat-text">{{ post.comments }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 侧边菜单 -->
	<view v-if="showSideMenuModal" class="side-menu-overlay" @click="hideSideMenu">
		<view class="side-menu" @click.stop>
			<view class="side-menu-header">
				<view class="user-info">
					<image class="user-avatar" :src="userInfo.avatar" mode="aspectFill"></image>
					<view class="user-details">
						<text class="user-name">{{ userInfo.name }}</text>
						<text class="user-desc">{{ userInfo.desc }}</text>
					</view>
				</view>
			</view>

			<view class="side-menu-content">
				<view class="menu-section">
					<view class="menu-item" v-for="item in sideMenuItems" :key="item.id" @click="handleMenuClick(item)">
						<uni-icons :type="item.icon" size="20" color="#666"></uni-icons>
						<text class="menu-text">{{ item.name }}</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'DiscoverPage',
	data() {
		return {
			statusBarHeight: 0,
			currentTab: 'recommend',
			currentSubMenu: 'all', // 当前选中的子菜单
			showSideMenuModal: false, // 侧边菜单显示状态
			isLoggedIn: false, // 登录状态
			navTabs: [
				{ id: 'recommend', name: '推荐' },
				{ id: 'friends', name: '朋友' },
				{ id: 'following', name: '关注' },
				{ id: 'featured', name: '精选' }
			],

			// 侧边菜单项
			sideMenuItems: [
				{ id: 'myWorks', name: '我的作品', icon: 'compose' },
				{ id: 'creatorCenter', name: '创作者中心', icon: 'star' },
				{ id: 'communityRules', name: '社区公约', icon: 'list' }
			],

			// 推荐页面的子菜单
			recommendSubMenus: [
				{ id: 'all', name: '全部' },
				{ id: 'camping', name: '露营' },
				{ id: 'fishing', name: '钓鱼' },
				{ id: 'bbq', name: 'BBQ' },
				{ id: 'hiking', name: '徒步' },
				{ id: 'nightrun', name: '夜跑' },
				{ id: 'party', name: '聚会' }
			],

			// 模拟数据 - 按分类组织
			postsData: {
				all: [
					{
						id: 1,
						title: '周末露营攻略分享',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 128,
						category: 'camping',
						author: {
							name: '户外小达人',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					},
					{
						id: 2,
						title: '夜跑装备推荐',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 89,
						category: 'nightrun',
						author: {
							name: '跑步达人',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					},
					{
						id: 3,
						title: 'BBQ聚会必备清单',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 256,
						category: 'bbq',
						author: {
							name: '烧烤大师',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					},
					{
						id: 4,
						title: '钓鱼新手入门指南',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 167,
						category: 'fishing',
						author: {
							name: '钓鱼老王',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					},
					{
						id: 5,
						title: '徒步路线推荐',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 92,
						category: 'hiking',
						author: {
							name: '徒步爱好者',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					},
					{
						id: 6,
						title: '生日聚会策划分享',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 203,
						category: 'party',
						author: {
							name: '聚会达人',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					}
				],
				camping: [
					{
						id: 11,
						title: '秋季露营装备清单',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 345,
						category: 'camping',
						author: {
							name: '露营专家',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					},
					{
						id: 12,
						title: '野外生火技巧',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 234,
						category: 'camping',
						author: {
							name: '户外老司机',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					}
				],
				fishing: [
					{
						id: 21,
						title: '钓鱼最佳时间分析',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 189,
						category: 'fishing',
						author: {
							name: '钓鱼高手',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					},
					{
						id: 22,
						title: '鱼饵制作秘籍',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 156,
						category: 'fishing',
						author: {
							name: '钓鱼达人',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					}
				],
				bbq: [
					{
						id: 31,
						title: '烧烤调料搭配指南',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 278,
						category: 'bbq',
						author: {
							name: 'BBQ大师',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					},
					{
						id: 32,
						title: '户外烧烤安全须知',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 145,
						category: 'bbq',
						author: {
							name: '安全专家',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					}
				],
				hiking: [
					{
						id: 41,
						title: '徒步装备选择指南',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 198,
						category: 'hiking',
						author: {
							name: '徒步教练',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					},
					{
						id: 42,
						title: '山地徒步注意事项',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 167,
						category: 'hiking',
						author: {
							name: '登山向导',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					}
				],
				nightrun: [
					{
						id: 51,
						title: '夜跑安全装备推荐',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 223,
						category: 'nightrun',
						author: {
							name: '夜跑达人',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					},
					{
						id: 52,
						title: '夜跑路线规划技巧',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 134,
						category: 'nightrun',
						author: {
							name: '跑步教练',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					}
				],
				party: [
					{
						id: 61,
						title: '聚会游戏推荐',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 312,
						category: 'party',
						author: {
							name: '聚会策划师',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					},
					{
						id: 62,
						title: '主题聚会布置指南',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						likes: 189,
						category: 'party',
						author: {
							name: '装饰达人',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						}
					}
				]
			},

			featuredPosts: [
				{
					id: 101,
					title: '2024年度最佳露营地推荐',
					description: '精选全国10个最美露营地，每一个都值得你去体验',
					image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
					likes: 1024,
					comments: 156,
					author: {
						name: '旅行编辑部',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
					}
				},
				{
					id: 102,
					title: '城市青年社交指南',
					description: '如何在陌生城市快速找到志同道合的朋友',
					image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
					likes: 768,
					comments: 89,
					author: {
						name: '社交达人',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
					}
				}
			]
		}
	},

	computed: {
		// 当前显示的帖子数据
		currentPosts() {
			return this.postsData[this.currentSubMenu] || [];
		},

		// 瀑布流左列数据
		leftColumnPosts() {
			return this.currentPosts.filter((_, index) => index % 2 === 0);
		},

		// 瀑布流右列数据
		rightColumnPosts() {
			return this.currentPosts.filter((_, index) => index % 2 === 1);
		},

		// 用户信息
		userInfo() {
			if (this.isLoggedIn) {
				return {
					name: '闲伴用户',
					desc: '发现生活的美好',
					avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
				};
			} else {
				return {
					name: '未登录',
					desc: '探索有趣的生活',
					avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
				};
			}
		}
	},

	onLoad() {
		// 获取系统信息，计算状态栏高度
		uni.getSystemInfo({
			success: (res) => {
				this.statusBarHeight = res.statusBarHeight;
			}
		});

		// 检查登录状态
		this.checkLoginStatus();
	},

	onPullDownRefresh() {
		// 模拟刷新数据
		setTimeout(() => {
			uni.stopPullDownRefresh();
			uni.showToast({
				title: '刷新成功',
				icon: 'success'
			});
		}, 1500);
	},

	onReachBottom() {
		// 模拟加载更多
		uni.showToast({
			title: '加载更多...',
			icon: 'loading'
		});
	},

	onShow() {
		// 页面显示时重新检查登录状态
		this.checkLoginStatus();
	},

	methods: {
		// 检查登录状态
		checkLoginStatus() {
			try {
				const userToken = uni.getStorageSync('userToken');
				const userInfo = uni.getStorageSync('userInfo');
				this.isLoggedIn = !!(userToken || userInfo);
			} catch (error) {
				console.error('检查登录状态失败:', error);
				this.isLoggedIn = false;
			}
		},

		// 切换标签页
		switchTab(tabId) {
			this.currentTab = tabId;
			// 切换到推荐页面时重置子菜单为全部
			if (tabId === 'recommend') {
				this.currentSubMenu = 'all';
			}
		},

		// 切换子菜单
		switchSubMenu(subMenuId) {
			this.currentSubMenu = subMenuId;
			console.log('切换子菜单到:', subMenuId);
			console.log('当前显示的帖子数量:', this.currentPosts.length);

			// 显示切换提示
			const menuName = this.recommendSubMenus.find(item => item.id === subMenuId)?.name || '';
			if (menuName) {
				uni.showToast({
					title: `切换到${menuName}`,
					icon: 'none',
					duration: 1000
				});
			}
		},

		// 显示侧边菜单
		showSideMenu() {
			this.showSideMenuModal = true;
		},

		// 隐藏侧边菜单
		hideSideMenu() {
			this.showSideMenuModal = false;
		},

		// 处理菜单点击
		handleMenuClick(item) {
			this.hideSideMenu();

			switch(item.id) {
				case 'myWorks':
					uni.showToast({
						title: '我的作品功能开发中',
						icon: 'none'
					});
					break;
				case 'creatorCenter':
					uni.showToast({
						title: '创作者中心功能开发中',
						icon: 'none'
					});
					break;
				case 'communityRules':
					// 跳转到社区公约页面
					uni.navigateTo({
						url: '/pages/community-rules/community-rules'
					});
					break;
			}
		},

		// 跳转到搜索页面
		goToSearch() {
			console.log('跳转到搜索页面');
			uni.showToast({
				title: '搜索功能开发中',
				icon: 'none'
			});
		},

		// 跳转到发布页面
		goToPublish() {
			console.log('跳转到发布页面');
			uni.navigateTo({
				url: '/pages/publish/publish'
			});
		},

		// 查看帖子详情
		viewPost(post) {
			console.log('查看帖子:', post.title);
			uni.showToast({
				title: '帖子详情开发中',
				icon: 'none'
			});
			// 未来可以跳转到帖子详情页
			// uni.navigateTo({
			//     url: `/pages/post-detail/post-detail?id=${post.id}`
			// });
		}
	}
}
</script>

<style lang="scss" scoped>
.discover-container {
	background: #f8f9fa;
	min-height: 100vh;
}

.status-bar {
	background: #fff;
}

// 顶部导航栏
.top-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16rpx 24rpx;
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;

	.nav-tabs {
		display: flex;
		align-items: center;
		gap: 32rpx;

		// 菜单图标
		.menu-icon {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 40rpx;
			height: 32rpx;
			margin-right: 16rpx;
			cursor: pointer;

			.menu-line {
				width: 20rpx;
				height: 3rpx;
				background: #333;
				margin: 2rpx 0;
				border-radius: 2rpx;
				transition: all 0.3s ease;
			}

			&:active {
				.menu-line {
					background: #FFD700;
				}
			}
		}

		.nav-tab {
			position: relative;
			padding: 8rpx 0;
			cursor: pointer;

			.tab-text {
				font-size: 32rpx;
				color: #999;
				font-weight: 500;
				transition: all 0.3s ease;
			}

			&.active {
				.tab-text {
					color: #333;
					font-weight: 600;
				}
			}

			.tab-indicator {
				position: absolute;
				bottom: -16rpx;
				left: 50%;
				transform: translateX(-50%);
				width: 40rpx;
				height: 6rpx;
				background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
				border-radius: 3rpx;
			}
		}
	}

	.header-actions {
		display: flex;
		align-items: center;
		gap: 16rpx;

		.action-btn {
			width: 72rpx;
			height: 72rpx;
			border-radius: 36rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #f5f5f5;
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.95);
			}

			&.publish-btn {
				background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
				box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
			}
		}
	}
}

// 子菜单容器
.sub-menu-container {
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	padding: 16rpx 0;

	.sub-menu-scroll {
		white-space: nowrap;

		.sub-menu-list {
			display: flex;
			padding: 0 24rpx;
			gap: 16rpx;

			.sub-menu-item {
				flex-shrink: 0;
				padding: 12rpx 24rpx;
				border-radius: 24rpx;
				background: #f8f9fa;
				transition: all 0.3s ease;
				border: 2rpx solid transparent;

				&:active {
					transform: scale(0.95);
				}

				&.active {
					background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
					border-color: #FFD700;
					box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);

					.sub-menu-text {
						color: #333;
						font-weight: 600;
					}
				}

				.sub-menu-text {
					font-size: 28rpx;
					color: #666;
					white-space: nowrap;
					line-height: 1.2;
				}
			}
		}
	}
}

// 内容容器
.content-container {
	flex: 1;
	padding: 16rpx 12rpx;
}

.tab-content {
	min-height: calc(100vh - 200rpx);
}

// 瀑布流布局
.waterfall-container {
	display: flex;
	gap: 12rpx;

	.waterfall-column {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}
}

// 帖子卡片
.post-card {
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
	}

	.post-image-container {
		position: relative;
		width: 100%;

		.post-image {
			width: 100%;
			height: 400rpx;
			object-fit: cover;
		}

		.post-overlay {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(transparent 60%, rgba(0, 0, 0, 0.3));
			display: flex;
			align-items: flex-end;
			padding: 16rpx;

			.post-stats {
				display: flex;
				gap: 16rpx;

				.stat-item {
					display: flex;
					align-items: center;
					gap: 6rpx;

					.stat-text {
						font-size: 24rpx;
						color: #fff;
						font-weight: 500;
					}
				}
			}
		}
	}

	.post-info {
		padding: 16rpx;

		.post-title {
			font-size: 28rpx;
			color: #333;
			line-height: 1.4;
			margin-bottom: 12rpx;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
		}

		.post-author {
			display: flex;
			align-items: center;
			gap: 8rpx;

			.author-avatar {
				width: 32rpx;
				height: 32rpx;
				border-radius: 16rpx;
			}

			.author-name {
				font-size: 24rpx;
				color: #666;
			}
		}
	}
}

// 空状态
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;

	.empty-icon {
		margin-bottom: 24rpx;
		opacity: 0.6;
	}

	.empty-text {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 12rpx;
		font-weight: 500;
	}

	.empty-desc {
		font-size: 28rpx;
		color: #999;
		line-height: 1.5;
	}
}

// 精选列表
.featured-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;

	.featured-card {
		background: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
		}

		.featured-image {
			width: 100%;
			height: 320rpx;
			object-fit: cover;
		}

		.featured-content {
			padding: 24rpx;

			.featured-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				line-height: 1.4;
				margin-bottom: 12rpx;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}

			.featured-desc {
				font-size: 28rpx;
				color: #666;
				line-height: 1.5;
				margin-bottom: 20rpx;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}

			.featured-meta {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.featured-author {
					display: flex;
					align-items: center;
					gap: 12rpx;

					.author-avatar {
						width: 40rpx;
						height: 40rpx;
						border-radius: 20rpx;
					}

					.author-name {
						font-size: 26rpx;
						color: #666;
						font-weight: 500;
					}
				}

				.featured-stats {
					display: flex;
					gap: 20rpx;

					.stat-item {
						display: flex;
						align-items: center;
						gap: 6rpx;

						.stat-text {
							font-size: 24rpx;
							color: #999;
						}
					}
				}
			}
		}
	}
}

// 侧边菜单
.side-menu-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-start;
}

.side-menu {
	width: 600rpx;
	height: 100vh;
	background: #fff;
	animation: slideInLeft 0.3s ease;
	display: flex;
	flex-direction: column;

	.side-menu-header {
		padding: 40rpx 32rpx 32rpx;
		border-bottom: 1rpx solid #f0f0f0;
		background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);

		.user-info {
			display: flex;
			align-items: center;
			gap: 20rpx;

			.user-avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 40rpx;
				border: 3rpx solid rgba(255, 255, 255, 0.3);
			}

			.user-details {
				.user-name {
					font-size: 32rpx;
					font-weight: 600;
					color: #333;
					display: block;
					margin-bottom: 8rpx;
				}

				.user-desc {
					font-size: 26rpx;
					color: #666;
					display: block;
				}
			}
		}
	}

	.side-menu-content {
		flex: 1;
		padding: 32rpx 0;

		.menu-section {
			.menu-item {
				display: flex;
				align-items: center;
				gap: 24rpx;
				padding: 24rpx 32rpx;
				transition: all 0.3s ease;

				&:active {
					background: #f8f9fa;
				}

				.menu-text {
					flex: 1;
					font-size: 32rpx;
					color: #333;
				}
			}
		}
	}
}

@keyframes slideInLeft {
	from {
		transform: translateX(-100%);
	}
	to {
		transform: translateX(0);
	}
}
</style>
