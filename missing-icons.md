# 需要添加的图标文件

请在 `/static/icons/` 文件夹中添加以下图标：

## 登录注册页面需要的图标

1. **left.png** - 返回按钮图标
2. **phone.png** - 手机号图标
3. **lock.png** - 密码图标
4. **user.png** - 用户名图标 (已有)
5. **shield.png** - 验证码/安全图标
6. **eye-open.png** - 显示密码图标
7. **eye-close.png** - 隐藏密码图标
8. **check.png** - 勾选图标
9. **wechat.png** - 微信图标
10. **qq.png** - QQ图标

## 图标规格要求

- **格式**: PNG
- **尺寸**: 建议 48x48px 或 64x64px
- **背景**: 透明背景
- **颜色**: 单色图标，便于使用CSS filter调色
- **风格**: 简洁现代，线条清晰

## 图标用途说明

### 导航图标
- `left.png` - 页面返回按钮

### 表单图标
- `phone.png` - 手机号输入框
- `lock.png` - 密码输入框
- `user.png` - 用户名输入框
- `shield.png` - 验证码输入框

### 交互图标
- `eye-open.png` - 显示密码状态
- `eye-close.png` - 隐藏密码状态
- `check.png` - 协议勾选状态

### 第三方登录图标
- `wechat.png` - 微信登录
- `qq.png` - QQ登录

## 注意事项

1. 所有图标应保持统一的设计风格
2. 图标线条粗细要一致
3. 建议使用单色设计，方便后期调色
4. 确保图标在小尺寸下依然清晰可见
