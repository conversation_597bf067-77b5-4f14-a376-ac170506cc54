/**
 * 全局样式文件
 * 包含适配、状态栏、返回按钮等统一样式
 */

/* ==================== 基础变量 ==================== */
:root {
	/* 主题色 */
	--primary-color: #667eea;
	--primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
	
	/* 状态栏高度（通过JS动态设置） */
	--status-bar-height: 44px;
	--navigation-bar-height: 88px;
	--bottom-safe-height: 0px;
	
	/* 响应式断点 */
	--screen-sm: 375px;
	--screen-md: 414px;
	--screen-lg: 768px;
	
	/* 间距系统 */
	--spacing-xs: 8rpx;
	--spacing-sm: 16rpx;
	--spacing-md: 24rpx;
	--spacing-lg: 32rpx;
	--spacing-xl: 48rpx;
	--spacing-xxl: 64rpx;
	
	/* 字体大小 */
	--font-size-xs: 24rpx;
	--font-size-sm: 28rpx;
	--font-size-md: 32rpx;
	--font-size-lg: 36rpx;
	--font-size-xl: 40rpx;
	--font-size-xxl: 48rpx;
	
	/* 圆角 */
	--border-radius-sm: 8rpx;
	--border-radius-md: 16rpx;
	--border-radius-lg: 24rpx;
	--border-radius-xl: 32rpx;
	
	/* 阴影 */
	--shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	--shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
	--shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
}

/* ==================== 状态栏适配 ==================== */
.status-bar {
	width: 100%;
	height: var(--status-bar-height);
	background: transparent;
}

.status-bar-placeholder {
	width: 100%;
	height: var(--status-bar-height);
}

/* 状态栏背景色适配 */
.status-bar-transparent {
	background: transparent;
}

.status-bar-white {
	background: #ffffff;
}

.status-bar-primary {
	background: var(--primary-color);
}

.status-bar-gradient {
	background: var(--primary-gradient);
}

/* ==================== 页面容器 ==================== */
.page-container {
	min-height: 100vh;
	background: #ffffff;
	position: relative;
	overflow-x: hidden;
	border-radius: 0 !important;
}

.page-container-with-bg {
	min-height: 100vh;
	position: relative;
	overflow-x: hidden;
	border-radius: 0 !important;
}

/* 确保页面和body没有圆角和边距 */
page, body, uni-page-body, html {
	margin: 0 !important;
	padding: 0 !important;
	border-radius: 0 !important;
	overflow-x: hidden;
}

.page-content {
	position: relative;
	z-index: 2;
	min-height: calc(100vh - var(--status-bar-height) - var(--bottom-safe-height));
}

/* ==================== 统一返回按钮 ==================== */
.back-button {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.5);
	border-radius: 50%;
	backdrop-filter: blur(10rpx);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	transition: all 0.3s ease;
	position: relative;
	z-index: 100;
}

.back-button:active {
	transform: scale(0.95);
	background: rgba(0, 0, 0, 0.7);
}

.back-button-light {
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
}

.back-button-light:active {
	background: #e9ecef;
}

.back-icon {
	width: 40rpx;
	height: 40rpx;
	filter: brightness(0) invert(1);
}

.back-icon-dark {
	filter: none;
}

/* ==================== 手势返回指示器 ==================== */
.gesture-indicator {
	position: fixed;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20rpx;
	background: rgba(0, 0, 0, 0.7);
	padding: 40rpx;
	border-radius: 20rpx;
	backdrop-filter: blur(10rpx);
	z-index: 200;
	transition: opacity 0.2s ease;
}

.gesture-icon {
	width: 60rpx;
	height: 60rpx;
	filter: brightness(0) invert(1);
}

.gesture-text {
	color: #FFFFFF;
	font-size: 28rpx;
	font-weight: 500;
}

/* ==================== 导航栏 ==================== */
.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: var(--navigation-bar-height);
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
	padding: 0 32rpx 16rpx;
	z-index: 100;
	background: transparent;
}

.nav-bar-with-bg {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
}

/* ==================== 响应式布局 ==================== */
@media screen and (max-width: 375px) {
	:root {
		--spacing-md: 20rpx;
		--spacing-lg: 28rpx;
		--font-size-md: 30rpx;
		--font-size-lg: 34rpx;
	}
}

@media screen and (min-width: 414px) {
	:root {
		--spacing-md: 28rpx;
		--spacing-lg: 36rpx;
		--font-size-md: 34rpx;
		--font-size-lg: 38rpx;
	}
}

@media screen and (min-width: 768px) {
	:root {
		--spacing-md: 32rpx;
		--spacing-lg: 40rpx;
		--font-size-md: 36rpx;
		--font-size-lg: 42rpx;
	}
}

/* ==================== 安全区域适配 ==================== */
.safe-area-bottom {
	padding-bottom: var(--bottom-safe-height);
}

.safe-area-top {
	padding-top: var(--status-bar-height);
}

/* ==================== 通用组件样式 ==================== */
.container-padding {
	padding-left: var(--spacing-lg);
	padding-right: var(--spacing-lg);
}

.section-spacing {
	margin-bottom: var(--spacing-xl);
}

.text-primary {
	color: var(--primary-color);
}

.bg-gradient {
	background: var(--primary-gradient);
}

/* ==================== 动画 ==================== */
.fade-in {
	animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(20rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.slide-up {
	animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}
