<template>
	<view class="test-page">
		<!-- 导航栏 -->
		<view class="nav-bar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="nav-left" @click="goBack">
				<text class="nav-icon">←</text>
			</view>
			<text class="nav-title">云函数测试</text>
		</view>

		<!-- 测试内容 -->
		<view class="test-content">
			<!-- 快速测试 -->
			<view class="test-section">
				<text class="section-title">快速测试</text>
				<view class="test-buttons">
					<view class="test-btn" @click="testSimpleFunction">
						<text class="btn-text">测试 simple-test</text>
					</view>
					<view class="test-btn" @click="testAuthFunction">
						<text class="btn-text">测试 auth</text>
					</view>
					<view class="test-btn" @click="testDbFunction">
						<text class="btn-text">测试 test-db</text>
					</view>
				</view>
			</view>

			<!-- 测试结果 -->
			<view class="result-section" v-if="testResults.length > 0">
				<text class="section-title">测试结果</text>
				<view class="result-list">
					<view class="result-item" v-for="(result, index) in testResults" :key="index">
						<view class="result-header">
							<text class="result-name">{{ result.name }}</text>
							<text class="result-status" :class="{ success: result.success, error: !result.success }">
								{{ result.success ? '成功' : '失败' }}
							</text>
						</view>
						<text class="result-message">{{ result.message }}</text>
						<view class="result-detail" v-if="result.detail">
							<text class="detail-text">{{ result.detail }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="action-section">
				<view class="action-btn primary" @click="testAllFunctions">
					<text class="btn-text">测试所有云函数</text>
				</view>
				<view class="action-btn secondary" @click="clearResults">
					<text class="btn-text">清除结果</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				testResults: []
			}
		},
		
		onLoad() {
			this.getSystemInfo()
		},
		
		methods: {
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync()
				this.statusBarHeight = systemInfo.statusBarHeight || 0
			},
			
			async testSimpleFunction() {
				await this.testCloudFunction('simple-test', { test: 'manual-test' })
			},
			
			async testAuthFunction() {
				await this.testCloudFunction('auth', { 
					action: 'checkUserExists', 
					mobile: '13800138000' 
				})
			},
			
			async testDbFunction() {
				await this.testCloudFunction('test-db', { 
					action: 'test-connection' 
				})
			},
			
			async testCloudFunction(functionName, data) {
				const startTime = Date.now()
				
				try {
					uni.showLoading({
						title: `测试 ${functionName}...`
					})
					
					const result = await uniCloud.callFunction({
						name: functionName,
						data: data
					})
					
					const duration = Date.now() - startTime
					
					this.addTestResult({
						name: functionName,
						success: true,
						message: `调用成功 (${duration}ms)`,
						detail: JSON.stringify(result.result, null, 2)
					})
					
					uni.hideLoading()
					
				} catch (error) {
					const duration = Date.now() - startTime
					
					let errorMessage = '未知错误'
					if (error.errCode) {
						switch (error.errCode) {
							case 'FUNCTION_NOT_FOUND':
								errorMessage = '云函数未找到，请检查部署状态'
								break
							case 'FUNCTION_EXECUTION_FAIL':
								errorMessage = '云函数执行失败'
								break
							case 'NETWORK_ERROR':
								errorMessage = '网络连接失败'
								break
							default:
								errorMessage = `错误代码: ${error.errCode}`
						}
					} else if (error.message) {
						errorMessage = error.message
					}
					
					this.addTestResult({
						name: functionName,
						success: false,
						message: `调用失败 (${duration}ms)`,
						detail: errorMessage
					})
					
					uni.hideLoading()
				}
			},
			
			async testAllFunctions() {
				const functions = [
					{ name: 'simple-test', data: { test: 'batch-test' } },
					{ name: 'auth', data: { action: 'checkUserExists', mobile: '13800138000' } },
					{ name: 'test-db', data: { action: 'test-connection' } }
				]
				
				this.clearResults()
				
				for (const func of functions) {
					await this.testCloudFunction(func.name, func.data)
					// 短暂延迟
					await new Promise(resolve => setTimeout(resolve, 500))
				}
				
				// 显示汇总
				const successCount = this.testResults.filter(r => r.success).length
				const totalCount = this.testResults.length
				
				uni.showToast({
					title: `完成: ${successCount}/${totalCount} 成功`,
					icon: successCount === totalCount ? 'success' : 'none',
					duration: 2000
				})
			},
			
			addTestResult(result) {
				this.testResults.unshift({
					...result,
					timestamp: new Date().toLocaleTimeString()
				})
			},
			
			clearResults() {
				this.testResults = []
				uni.showToast({
					title: '结果已清除',
					icon: 'success'
				})
			},
			
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss" scoped>
.test-page {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.nav-bar {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 32rpx;
	position: relative;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-icon {
	font-size: 36rpx;
	color: white;
	font-weight: bold;
}

.nav-title {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	font-size: 36rpx;
	font-weight: 600;
	color: white;
}

.test-content {
	padding: 32rpx;
}

.test-section {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: white;
	margin-bottom: 24rpx;
	display: block;
}

.test-buttons {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.test-btn {
	height: 80rpx;
	background: rgba(255, 255, 255, 0.1);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
}

.test-btn:active {
	transform: scale(0.95);
	background: rgba(255, 255, 255, 0.2);
}

.result-section {
	margin-bottom: 40rpx;
}

.result-list {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 24rpx;
	padding: 24rpx;
	backdrop-filter: blur(10rpx);
}

.result-item {
	padding: 16rpx 0;
	border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.result-item:last-child {
	border-bottom: none;
}

.result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8rpx;
}

.result-name {
	font-size: 28rpx;
	color: white;
	font-weight: 600;
}

.result-status {
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-weight: 500;
}

.result-status.success {
	background: rgba(76, 175, 80, 0.3);
	color: #4CAF50;
}

.result-status.error {
	background: rgba(244, 67, 54, 0.3);
	color: #F44336;
}

.result-message {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
	margin-bottom: 8rpx;
}

.result-detail {
	background: rgba(0, 0, 0, 0.3);
	border-radius: 8rpx;
	padding: 12rpx;
}

.detail-text {
	font-size: 22rpx;
	color: rgba(255, 255, 255, 0.7);
	font-family: monospace;
	line-height: 1.4;
}

.action-section {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.action-btn {
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.action-btn.primary {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);
}

.action-btn.secondary {
	background: rgba(255, 255, 255, 0.1);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	backdrop-filter: blur(10rpx);
}

.action-btn:active {
	transform: scale(0.95);
}

.btn-text {
	font-size: 32rpx;
	font-weight: 600;
	color: white;
}
</style>
