'use strict';

exports.main = async (event, context) => {
	console.log('upload-avatar event:', event);

	const { action, ...params } = event;

	switch (action) {
		case 'uploadAvatar':
			return await uploadAvatar(params);
		case 'updateUserAvatar':
			return await updateUserAvatar(params);
		default:
			return {
				code: -1,
				message: '未知操作'
			};
	}
};

/**
 * 上传头像到云存储
 */
async function uploadAvatar({ filePath, uid }) {
	try {
		
		// 生成唯一的文件名
		const timestamp = Date.now();
		const randomStr = Math.random().toString(36).substring(2, 8);
		const cloudPath = `avatars/${uid}_${timestamp}_${randomStr}.jpg`;
		
		// 上传文件到云存储
		const uploadResult = await uniCloud.uploadFile({
			filePath: filePath,
			cloudPath: cloudPath,
			fileType: 'image'
		});
		
		if (uploadResult.fileID) {
			return {
				code: 0,
				message: '头像上传成功',
				data: {
					fileID: uploadResult.fileID,
					cloudPath: cloudPath
				}
			};
		} else {
			return {
				code: -1,
				message: '头像上传失败'
			};
		}
	} catch (error) {
		console.error('上传头像失败:', error);
		return {
			code: -1,
			message: '上传头像失败，请重试'
		};
	}
}

/**
 * 更新用户头像URL到数据库
 */
async function updateUserAvatar({ uid, avatarUrl }) {
	try {
		// 直接更新数据库
		const db = uniCloud.database();
		const userCollection = db.collection('uni-id-users');

		const updateResult = await userCollection.doc(uid).update({
			avatar: avatarUrl
		});

		console.log('头像URL更新结果:', updateResult);

		if (updateResult.updated > 0) {
			return {
				code: 0,
				message: '头像更新成功',
				data: {
					avatar: avatarUrl
				}
			};
		} else {
			return {
				code: -1,
				message: '头像更新失败：用户不存在'
			};
		}
	} catch (error) {
		console.error('更新用户头像失败:', error);
		return {
			code: -1,
			message: '更新头像失败，请重试'
		};
	}
}
