<template>
	<view class="settings-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<uni-icons type="left" size="24" color="#333"></uni-icons>
					<text class="back-text">返回</text>
				</view>
				<text class="nav-title">设置</text>
				<view class="nav-right"></view>
			</view>
		</view>

		<!-- 设置内容 -->
		<scroll-view class="settings-content" scroll-y="true">
			<!-- 账户设置 -->
			<view class="setting-section">
				<text class="section-title">账户设置</text>
				<view class="setting-list">
					<view class="setting-item" @tap="goToProfile">
						<view class="item-left">
							<view class="item-icon account">
								<uni-icons type="person" size="20" color="#fff"></uni-icons>
							</view>
							<text class="item-title">个人资料</text>
						</view>
						<view class="item-right">
							<text class="item-desc">编辑个人信息</text>
							<uni-icons type="right" size="16" color="#ccc"></uni-icons>
						</view>
					</view>

					<view class="setting-item" @tap="goToSecurity">
						<view class="item-left">
							<view class="item-icon security">
								<uni-icons type="locked" size="20" color="#fff"></uni-icons>
							</view>
							<text class="item-title">账户安全</text>
						</view>
						<view class="item-right">
							<text class="item-desc">密码、手机号</text>
							<uni-icons type="right" size="16" color="#ccc"></uni-icons>
						</view>
					</view>

					<view class="setting-item" @tap="goToPrivacy">
						<view class="item-left">
							<view class="item-icon privacy">
								<uni-icons type="eye-slash" size="20" color="#fff"></uni-icons>
							</view>
							<text class="item-title">隐私设置</text>
						</view>
						<view class="item-right">
							<text class="item-desc">谁能看到我</text>
							<uni-icons type="right" size="16" color="#ccc"></uni-icons>
						</view>
					</view>
				</view>
			</view>

			<!-- 通知设置 -->
			<view class="setting-section">
				<text class="section-title">通知设置</text>
				<view class="setting-list">
					<view class="setting-item">
						<view class="item-left">
							<view class="item-icon notification">
								<uni-icons type="bell" size="20" color="#fff"></uni-icons>
							</view>
							<text class="item-title">推送通知</text>
						</view>
						<view class="item-right">
							<switch :checked="notificationEnabled" @change="toggleNotification" color="#667eea"></switch>
						</view>
					</view>

					<view class="setting-item">
						<view class="item-left">
							<view class="item-icon message">
								<uni-icons type="chatbubble" size="20" color="#fff"></uni-icons>
							</view>
							<text class="item-title">消息提醒</text>
						</view>
						<view class="item-right">
							<switch :checked="messageEnabled" @change="toggleMessage" color="#667eea"></switch>
						</view>
					</view>

					<view class="setting-item">
						<view class="item-left">
							<view class="item-icon sound">
								<uni-icons type="sound" size="20" color="#fff"></uni-icons>
							</view>
							<text class="item-title">声音提醒</text>
						</view>
						<view class="item-right">
							<switch :checked="soundEnabled" @change="toggleSound" color="#667eea"></switch>
						</view>
					</view>
				</view>
			</view>

			<!-- 通用设置 -->
			<view class="setting-section">
				<text class="section-title">通用设置</text>
				<view class="setting-list">
					<view class="setting-item" @tap="selectLanguage">
						<view class="item-left">
							<view class="item-icon language">
								<uni-icons type="globe" size="20" color="#fff"></uni-icons>
							</view>
							<text class="item-title">语言</text>
						</view>
						<view class="item-right">
							<text class="item-desc">{{ currentLanguage }}</text>
							<uni-icons type="right" size="16" color="#ccc"></uni-icons>
						</view>
					</view>

					<view class="setting-item" @tap="selectTheme">
						<view class="item-left">
							<view class="item-icon theme">
								<uni-icons type="color-palette" size="20" color="#fff"></uni-icons>
							</view>
							<text class="item-title">主题模式</text>
						</view>
						<view class="item-right">
							<text class="item-desc">{{ currentTheme }}</text>
							<uni-icons type="right" size="16" color="#ccc"></uni-icons>
						</view>
					</view>

					<view class="setting-item" @tap="clearCache">
						<view class="item-left">
							<view class="item-icon cache">
								<uni-icons type="trash" size="20" color="#fff"></uni-icons>
							</view>
							<text class="item-title">清理缓存</text>
						</view>
						<view class="item-right">
							<text class="item-desc">{{ cacheSize }}</text>
							<uni-icons type="right" size="16" color="#ccc"></uni-icons>
						</view>
					</view>
				</view>
			</view>

			<!-- 其他设置 -->
			<view class="setting-section">
				<text class="section-title">其他</text>
				<view class="setting-list">
					<view class="setting-item" @tap="checkUpdate">
						<view class="item-left">
							<view class="item-icon update">
								<uni-icons type="loop" size="20" color="#fff"></uni-icons>
							</view>
							<text class="item-title">检查更新</text>
						</view>
						<view class="item-right">
							<text class="item-desc">v1.0.0</text>
							<uni-icons type="right" size="16" color="#ccc"></uni-icons>
						</view>
					</view>

					<view class="setting-item" @tap="goToAbout">
						<view class="item-left">
							<view class="item-icon about">
								<uni-icons type="info" size="20" color="#fff"></uni-icons>
							</view>
							<text class="item-title">关于闲伴</text>
						</view>
						<view class="item-right">
							<uni-icons type="right" size="16" color="#ccc"></uni-icons>
						</view>
					</view>

					<view class="setting-item" @tap="logout">
						<view class="item-left">
							<view class="item-icon logout">
								<uni-icons type="back" size="20" color="#fff"></uni-icons>
							</view>
							<text class="item-title">退出登录</text>
						</view>
						<view class="item-right">
							<uni-icons type="right" size="16" color="#ccc"></uni-icons>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				notificationEnabled: true,
				messageEnabled: true,
				soundEnabled: false,
				currentLanguage: '简体中文',
				currentTheme: '跟随系统',
				cacheSize: '23.5MB'
			}
		},

		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},

		methods: {
			goBack() {
				uni.switchTab({
					url: '/pages/profile/profile'
				});
			},

			goToProfile() {
				uni.showToast({
					title: '个人资料页面开发中',
					icon: 'none'
				});
			},

			goToSecurity() {
				uni.showToast({
					title: '账户安全页面开发中',
					icon: 'none'
				});
			},

			goToPrivacy() {
				uni.showToast({
					title: '隐私设置页面开发中',
					icon: 'none'
				});
			},

			toggleNotification(e) {
				this.notificationEnabled = e.detail.value;
				uni.showToast({
					title: this.notificationEnabled ? '已开启推送通知' : '已关闭推送通知',
					icon: 'none'
				});
			},

			toggleMessage(e) {
				this.messageEnabled = e.detail.value;
				uni.showToast({
					title: this.messageEnabled ? '已开启消息提醒' : '已关闭消息提醒',
					icon: 'none'
				});
			},

			toggleSound(e) {
				this.soundEnabled = e.detail.value;
				uni.showToast({
					title: this.soundEnabled ? '已开启声音提醒' : '已关闭声音提醒',
					icon: 'none'
				});
			},

			selectLanguage() {
				uni.showActionSheet({
					itemList: ['简体中文', 'English', '繁體中文'],
					success: (res) => {
						const languages = ['简体中文', 'English', '繁體中文'];
						this.currentLanguage = languages[res.tapIndex];
						uni.showToast({
							title: `已切换到${this.currentLanguage}`,
							icon: 'none'
						});
					}
				});
			},

			selectTheme() {
				uni.showActionSheet({
					itemList: ['跟随系统', '浅色模式', '深色模式'],
					success: (res) => {
						const themes = ['跟随系统', '浅色模式', '深色模式'];
						this.currentTheme = themes[res.tapIndex];
						uni.showToast({
							title: `已切换到${this.currentTheme}`,
							icon: 'none'
						});
					}
				});
			},

			clearCache() {
				uni.showModal({
					title: '清理缓存',
					content: '确定要清理应用缓存吗？这将删除临时文件和图片缓存。',
					success: (res) => {
						if (res.confirm) {
							uni.showLoading({
								title: '清理中...'
							});
							setTimeout(() => {
								uni.hideLoading();
								this.cacheSize = '0MB';
								uni.showToast({
									title: '缓存清理完成',
									icon: 'success'
								});
							}, 2000);
						}
					}
				});
			},

			checkUpdate() {
				uni.showLoading({
					title: '检查中...'
				});
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: '已是最新版本',
						icon: 'success'
					});
				}, 1500);
			},

			goToAbout() {
				uni.navigateTo({
					url: '/pages/profile-features/about/about'
				});
			},

			logout() {
				uni.showModal({
					title: '退出登录',
					content: '确定要退出当前账户吗？',
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '已退出登录',
								icon: 'success'
							});
							// 这里可以添加退出登录的逻辑
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.settings-page {
		background: #f5f5f5;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.status-bar {
		background: #fff;
	}

	.header {
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;

		.nav-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;
			height: 88rpx;

			.nav-left {
				display: flex;
				align-items: center;
				gap: 8rpx;
				flex: 1;

				.back-text {
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				text-align: center;
				flex: 2;
			}

			.nav-right {
				flex: 1;
			}
		}
	}

	// 设置内容区域
	.settings-content {
		flex: 1;
		padding: 16rpx 24rpx 120rpx;

		.setting-section {
			margin-bottom: 32rpx;

			.section-title {
				font-size: 28rpx;
				color: #333;
				font-weight: 600;
				margin-bottom: 16rpx;
				padding-left: 8rpx;
				display: block;
			}

			.setting-list {
				background: #fff;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.setting-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 24rpx;
					border-bottom: 1rpx solid #f5f5f5;
					transition: all 0.3s ease;

					&:last-child {
						border-bottom: none;
					}

					&:active {
						background: #f8f9fa;
					}

					.item-left {
						display: flex;
						align-items: center;
						gap: 16rpx;
						flex: 1;

						.item-icon {
							width: 48rpx;
							height: 48rpx;
							border-radius: 24rpx;
							display: flex;
							align-items: center;
							justify-content: center;

							&.account {
								background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
							}

							&.security {
								background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
							}

							&.privacy {
								background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
							}

							&.notification {
								background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
							}

							&.message {
								background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
							}

							&.sound {
								background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
							}

							&.language {
								background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
							}

							&.theme {
								background: linear-gradient(135deg, #ff8a80 0%, #ea80fc 100%);
							}

							&.cache {
								background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
							}

							&.update {
								background: linear-gradient(135deg, #a6c1ee 0%, #fbc2eb 100%);
							}

							&.about {
								background: linear-gradient(135deg, #fdcbf1 0%, #fdcbf1 100%);
							}

							&.logout {
								background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
							}
						}

						.item-title {
							font-size: 30rpx;
							color: #333;
							font-weight: 500;
						}
					}

					.item-right {
						display: flex;
						align-items: center;
						gap: 8rpx;

						.item-desc {
							font-size: 26rpx;
							color: #999;
						}
					}
				}
			}
		}
	}
</style>
