<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告栏图标修改预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 20px 20px;
            color: white;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 20px;
        }
        
        /* 公告栏样式 */
        .notice-section {
            margin-bottom: 20px;
        }
        
        .notice-container {
            display: flex;
            align-items: center;
            background-color: #f8f9ff;
            border-radius: 8px;
            padding: 10px 12px;
            border-left: 3px solid #007AFF;
        }
        
        .notice-icon {
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .notice-icon-img {
            width: 16px;
            height: 16px;
            object-fit: contain;
        }
        
        .notice-content {
            flex: 1;
        }
        
        .notice-text {
            font-size: 13px;
            color: #333;
            line-height: 1.4;
        }
        
        .notice-close {
            margin-left: 8px;
            padding: 4px;
            cursor: pointer;
            color: #999;
            font-size: 14px;
        }
        
        .notice-close:hover {
            color: #666;
        }
        
        /* 对比展示 */
        .comparison {
            display: flex;
            gap: 20px;
            margin-top: 30px;
        }
        
        .comparison-item {
            flex: 1;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .old-version {
            background: #ffe6e6;
            border: 2px solid #ffcccc;
        }
        
        .new-version {
            background: #e6ffe6;
            border: 2px solid #ccffcc;
        }
        
        .comparison-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .old-version .comparison-title {
            color: #cc0000;
        }
        
        .new-version .comparison-title {
            color: #006600;
        }
        
        .old-notice {
            background-color: #f8f9ff;
            border-radius: 8px;
            padding: 10px 12px;
            border-left: 3px solid #007AFF;
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .old-icon {
            margin-right: 8px;
            color: #007AFF;
            font-size: 16px;
        }
        
        .demo-note {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .icon-preview {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 10px;
        }
        
        .icon-large {
            width: 48px;
            height: 48px;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>趣嗒同行</h1>
            <p>公告栏图标修改预览</p>
        </div>
        
        <div class="content">
            <h3>✅ 修改后的公告栏</h3>
            
            <!-- 新版公告栏 -->
            <div class="notice-section">
                <div class="notice-container">
                    <div class="notice-icon">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMUMxMS44NjYgMSAxNSA0LjEzNCAxNSA4QzE1IDExLjg2NiAxMS44NjYgMTUgOCAxNUM0LjEzNCAxNSAxIDExLjg2NiAxIDhDMSA0LjEzNCA0LjEzNCAxIDggMVoiIGZpbGw9IiMwMDdBRkYiLz4KPHBhdGggZD0iTTggNEMxMC4yMDkgNCA0IDYuNzkxIDQgOUM0IDExLjIwOSA2Ljc5MSA0IDkgNEM5IDYuNzkxIDExLjIwOSA0IDkgNEM2Ljc5MSA0IDQgNi43OTEgNCA5WiIgZmlsbD0iI0ZGRiIvPgo8L3N2Zz4K" class="notice-icon-img" alt="公告图标">
                    </div>
                    <div class="notice-content">
                        <div class="notice-text">欢迎来到趣嗒同行，发现身边有趣的人和事</div>
                    </div>
                    <div class="notice-close">×</div>
                </div>
            </div>
            
            <!-- 图标预览 -->
            <div class="icon-preview">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTI0IDNDMzUuNTk4IDMgNDUgMTIuNDAyIDQ1IDI0QzQ1IDM1LjU5OCAzNS41OTggNDUgMjQgNDVDMTIuNDAyIDQ1IDMgMzUuNTk4IDMgMjRDMyAxMi40MDIgMTIuNDAyIDMgMjQgM1oiIGZpbGw9IiMwMDdBRkYiLz4KPHBhdGggZD0iTTI0IDEyQzMwLjYyNyAxMiAzNiAxNy4zNzMgMzYgMjRDMzYgMzAuNjI3IDMwLjYyNyAzNiAyNCAzNkMxNy4zNzMgMzYgMTIgMzAuNjI3IDEyIDI0QzEyIDE3LjM3MyAxNy4zNzMgMTIgMjQgMTJaIiBmaWxsPSIjRkZGIi8+CjxwYXRoIGQ9Ik0yNCAyMUMyNS42NTcgMjEgMjcgMjIuMzQzIDI3IDI0QzI3IDI1LjY1NyAyNS42NTcgMjcgMjQgMjdDMjIuMzQzIDI3IDIxIDI1LjY1NyAyMSAyNEMyMSAyMi4zNDMgMjIuMzQzIDIxIDI0IDIxWiIgZmlsbD0iIzAwN0FGRiIvPgo8L3N2Zz4K" class="icon-large" alt="wctf图标预览">
            </div>
            
            <!-- 对比展示 -->
            <div class="comparison">
                <div class="comparison-item old-version">
                    <div class="comparison-title">❌ 修改前</div>
                    <div class="old-notice">
                        <div class="old-icon">🔊</div>
                        <div style="flex: 1; font-size: 13px; color: #333;">
                            uni-icons sound图标
                        </div>
                    </div>
                    <div style="font-size: 12px; color: #666;">
                        使用系统图标，不够美观
                    </div>
                </div>
                
                <div class="comparison-item new-version">
                    <div class="comparison-title">✅ 修改后</div>
                    <div class="notice-container" style="margin-bottom: 10px;">
                        <div class="notice-icon">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMUMxMS44NjYgMSAxNSA0LjEzNCAxNSA4QzE1IDExLjg2NiAxMS44NjYgMTUgOCAxNUM0LjEzNCAxNSAxIDExLjg2NiAxIDhDMSA0LjEzNCA0LjEzNCAxIDggMVoiIGZpbGw9IiMwMDdBRkYiLz4KPHBhdGggZD0iTTggNEMxMC4yMDkgNCA0IDYuNzkxIDQgOUM0IDExLjIwOSA2Ljc5MSA0IDkgNEM5IDYuNzkxIDExLjIwOSA0IDkgNEM2Ljc5MSA0IDQgNi43OTEgNCA5WiIgZmlsbD0iI0ZGRiIvPgo8L3N2Zz4K" class="notice-icon-img" alt="wctf图标">
                        </div>
                        <div style="flex: 1; font-size: 13px; color: #333;">
                            自定义wctf.png图标
                        </div>
                    </div>
                    <div style="font-size: 12px; color: #666;">
                        使用自定义图标，更加美观
                    </div>
                </div>
            </div>
            
            <div class="demo-note">
                <strong>🔧 修改内容：</strong><br>
                • 将 uni-icons 的 sound 图标替换为自定义的 wctf.png 图片<br>
                • 图标路径：/static/icons/wctf.png<br>
                • 图标尺寸：32rpx × 32rpx（16px × 16px）<br>
                • 保持原有的公告栏样式和布局<br>
                • 图标居中对齐，与文字完美配合
            </div>
        </div>
    </div>
</body>
</html>
