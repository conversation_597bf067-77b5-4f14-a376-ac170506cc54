<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轻盈动画效果预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 20px 20px;
            color: white;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        /* 标签导航 */
        .tab-navigation {
            padding: 10px 0;
            background: #fff;
            margin: 10px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        }

        .tab-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: relative;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 5px;
            position: relative;
            flex: 1;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-text {
            font-size: 14px;
            color: #666;
            font-weight: 400;
            transition: all 0.3s ease;
        }

        .tab-item.active .tab-text {
            color: #333;
            font-size: 16px;
            font-weight: 600;
        }

        /* 轻盈弧形指示器 */
        .tab-indicator {
            position: absolute;
            bottom: 0px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 10px;
            overflow: hidden;
            animation: fadeInUp 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .tab-indicator::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 20px;
            background: linear-gradient(90deg, #BBB2FF 0%, #E3D2FF 50%, #A7C0FF 100%);
            border-radius: 50%;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(5px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0) scale(1);
            }
        }
        
        .content {
            padding: 20px;
            color: #666;
        }
        
        /* 推荐搭子区域 */
        .recommended-partners {
            margin-bottom: 20px;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .section-title {
            font-size: 18px;
            color: #333;
            font-weight: 600;
        }
        
        .section-more {
            font-size: 14px;
            color: #007AFF;
            cursor: pointer;
        }
        
        .partner-list {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding-bottom: 10px;
        }
        
        .partner-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 80px;
            cursor: pointer;
        }
        
        .partner-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 8px;
            object-fit: cover;
        }
        
        .partner-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .partner-tags {
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        
        /* 广告banner */
        .ad-banner-section {
            margin: 20px 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .ad-banner-img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            transition: transform 0.2s ease;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .ad-banner-img:hover {
            transform: scale(1.02);
        }

        .ad-banner-img:active {
            transform: scale(0.98);
        }
        
        .demo-note {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.5;
            text-align: left;
        }
        
        .animation-comparison {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 20px 0;
        }
        
        .animation-demo {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .animation-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .old-animation {
            border: 2px solid #ffcccc;
            background: #ffe6e6;
        }
        
        .new-animation {
            border: 2px solid #ccffcc;
            background: #e6ffe6;
        }
        
        .old-animation .animation-title {
            color: #cc0000;
        }
        
        .new-animation .animation-title {
            color: #006600;
        }
        
        .animation-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>趣嗒同行</h1>
            <p>轻盈动画效果 + 广告Banner</p>
        </div>
        
        <!-- 标签导航 -->
        <div class="tab-navigation">
            <div class="tab-container">
                <div class="tab-item active" onclick="switchTab(this, 0)">
                    <span class="tab-text">首页推荐</span>
                    <div class="tab-indicator"></div>
                </div>
                <div class="tab-item" onclick="switchTab(this, 1)">
                    <span class="tab-text">组局约伴</span>
                </div>
                <div class="tab-item" onclick="switchTab(this, 2)">
                    <span class="tab-text">城市玩伴</span>
                </div>
                <div class="tab-item" onclick="switchTab(this, 3)">
                    <span class="tab-text">游戏玩伴</span>
                </div>
            </div>
        </div>
        
        <div class="content">
            <h3 id="content-title">首页推荐</h3>
            
            <!-- 推荐搭子区域 -->
            <div class="recommended-partners">
                <div class="section-header">
                    <div class="section-title">推荐搭子</div>
                    <div class="section-more">查看更多</div>
                </div>
                <div class="partner-list">
                    <div class="partner-item">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMzAiIGZpbGw9IiM2NjdlZWEiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxOCIgeT0iMTgiPgo8cGF0aCBkPSJNMTIgMTJDMTQuMjA5MSAxMiAxNiA5LjIwOTE0IDE2IDdDMTYgNC43OTA4NiAxNC4yMDkxIDMgMTIgM0M5Ljc5MDg2IDMgOCA0Ljc5MDg2IDggN0M4IDkuMjA5MTQgOS43OTA4NiAxMiAxMiAxMloiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzIDQgMTQuMzQgNCAyMVYyMkgyMFYyMUMyMCAxNC4zNCAxNC42NyAxMyAxMiAxNFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K" class="partner-avatar" alt="小阳哥">
                        <div class="partner-name">小阳哥</div>
                        <div class="partner-tags">运动 · 摄影</div>
                    </div>
                    <div class="partner-item">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMzAiIGZpbGw9IiNmMDkzZmIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxOCIgeT0iMTgiPgo8cGF0aCBkPSJNMTIgMTJDMTQuMjA5MSAxMiAxNiA5LjIwOTE0IDE2IDdDMTYgNC43OTA4NiAxNC4yMDkxIDMgMTIgM0M5Ljc5MDg2IDMgOCA0Ljc5MDg2IDggN0M4IDkuMjA5MTQgOS43OTA4NiAxMiAxMiAxMloiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzIDQgMTQuMzQgNCAyMVYyMkgyMFYyMUMyMCAxNC4zNCAxNC42NyAxMyAxMiAxNFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K" class="partner-avatar" alt="小美">
                        <div class="partner-name">小美</div>
                        <div class="partner-tags">美食 · 旅行</div>
                    </div>
                    <div class="partner-item">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMzAiIGZpbGw9IiM0ZWNkYzQiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxOCIgeT0iMTgiPgo8cGF0aCBkPSJNMTIgMTJDMTQuMjA5MSAxMiAxNiA5LjIwOTE0IDE2IDdDMTYgNC43OTA4NiAxNC4yMDkxIDMgMTIgM0M5Ljc5MDg2IDMgOCA0Ljc5MDg2IDggN0M4IDkuMjA5MTQgOS43OTA4NiAxMiAxMiAxMloiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzIDQgMTQuMzQgNCAyMVYyMkgyMFYyMUMyMCAxNC4zNCAxNC42NyAxMyAxMiAxNFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K" class="partner-avatar" alt="阿强">
                        <div class="partner-name">阿强</div>
                        <div class="partner-tags">音乐 · 电影</div>
                    </div>
                </div>
            </div>
            
            <!-- 广告banner -->
            <div class="ad-banner-section">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjgwIiB2aWV3Qm94PSIwIDAgMzAwIDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjgwIiByeD0iNDAiIGZpbGw9InVybCgjZ3JhZGllbnQwXzEyXzMpIiBmaWxsLW9wYWNpdHk9IjAuOSIvPgo8dGV4dCB4PSIxNTAiIHk9IjQ1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTgiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+8J+OiSDnsr7lk4HlubPlj7AgLSDlj5HnjrDouqvovrnmnInotKPnmoTkurogIPCfmI48L3RleHQ+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50MF8xMl8zIiB4MT0iMCIgeTE9IjAiIHgyPSIzMDAiIHkyPSI4MCI+CjxzdG9wIHN0b3AtY29sb3I9IiM2NjdlZWEiLz4KPHN0b3Agb2Zmc2V0PSIwLjUiIHN0b3AtY29sb3I9IiM3NjRiYTIiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjZjA5M2ZiIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+Cg==" class="ad-banner-img" alt="广告banner" onclick="showAdClick()">
            </div>
            
            <!-- 动画对比 -->
            <div class="animation-comparison">
                <div class="animation-demo old-animation">
                    <div class="animation-title">❌ 之前：抖动动画</div>
                    <div class="animation-desc">
                        slideIn动画，宽度从0到80rpx<br>
                        感觉笨重，有抖动效果
                    </div>
                </div>
                
                <div class="animation-demo new-animation">
                    <div class="animation-title">✅ 现在：轻盈动画</div>
                    <div class="animation-desc">
                        fadeInUp动画，淡入+上移+缩放<br>
                        cubic-bezier缓动，更加轻盈自然
                    </div>
                </div>
            </div>
            
            <div class="demo-note">
                <strong>✅ 优化内容：</strong><br>
                • <strong>动画效果</strong>：从slideIn改为fadeInUp，更轻盈<br>
                • <strong>缓动函数</strong>：cubic-bezier(0.25, 0.46, 0.45, 0.94)<br>
                • <strong>动画时长</strong>：从0.3s减少到0.25s，更快响应<br>
                • <strong>广告Banner</strong>：添加在推荐搭子下方<br>
                • <strong>图片路径</strong>：/static/banner/one.png<br>
                • <strong>交互效果</strong>：hover放大，active缩小
            </div>
        </div>
    </div>

    <script>
        const tabNames = ['首页推荐', '组局约伴', '城市玩伴', '游戏玩伴'];
        
        function switchTab(element, index) {
            // 移除所有active状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
                const indicator = item.querySelector('.tab-indicator');
                if (indicator) {
                    indicator.remove();
                }
            });
            
            // 添加active状态到当前选中的标签
            element.classList.add('active');
            
            // 添加指示器
            const indicator = document.createElement('div');
            indicator.className = 'tab-indicator';
            element.appendChild(indicator);
            
            // 更新内容标题
            document.getElementById('content-title').textContent = tabNames[index];
        }
        
        function showAdClick() {
            alert('广告banner点击！');
        }
    </script>
</body>
</html>
