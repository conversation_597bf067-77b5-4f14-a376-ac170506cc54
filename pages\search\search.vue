<template>
	<view class="search-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<!-- 搜索头部 -->
		<view class="search-header">
			<view class="back-btn" @click="goBack">
				<uni-icons type="left" size="22" color="#333"></uni-icons>
			</view>
			<view class="search-input-container">
				<view class="search-input-wrapper">
					<uni-icons type="search" size="18" color="#999"></uni-icons>
					<input
						class="search-input"
						v-model="searchKeyword"
						placeholder="搜索你感兴趣的内容..."
						:focus="true"
						@input="onSearchInput"
						@confirm="onSearchConfirm"
					/>
					<view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
						<uni-icons type="clear" size="16" color="#999"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 搜索内容区域 -->
		<scroll-view class="search-content" scroll-y="true">
			<!-- 搜索建议 -->
			<view v-if="searchKeyword && suggestions.length > 0" class="suggestions-section">
				<view
					v-for="(suggestion, index) in suggestions"
					:key="index"
					class="suggestion-item"
					@click="selectSuggestion(suggestion)"
				>
					<uni-icons type="search" size="16" color="#FFD700"></uni-icons>
					<text class="suggestion-text">{{ suggestion }}</text>
					<uni-icons type="top-left" size="14" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 历史搜索 -->
			<view v-if="!searchKeyword && searchHistory.length > 0" class="history-section">
				<view class="section-header">
					<view class="header-left">
						<uni-icons type="clock" size="18" color="#FFD700"></uni-icons>
						<text class="section-title">最近搜索</text>
					</view>
					<view class="clear-btn" @click="clearHistory">
						<text class="clear-text">清空</text>
					</view>
				</view>
				<view class="tag-container">
					<view
						v-for="(item, index) in searchHistory"
						:key="index"
						class="tag-item history-tag"
						@click="selectHistory(item)"
					>
						<text class="tag-text">{{ item }}</text>
					</view>
				</view>
			</view>

			<!-- 热门搜索 -->
			<view v-if="!searchKeyword" class="hot-section">
				<view class="section-header">
					<view class="header-left">
						<uni-icons type="fire" size="18" color="#FF6B6B"></uni-icons>
						<text class="section-title">热门搜索</text>
					</view>
				</view>
				<view class="tag-container">
					<view
						v-for="(item, index) in hotSearches"
						:key="index"
						class="tag-item hot-tag"
						:class="{ 'top-rank': index < 3 }"
						@click="selectHotSearch(item)"
					>
						<text class="rank-number" v-if="index < 3">{{ index + 1 }}</text>
						<text class="tag-text">{{ item.keyword }}</text>
						<view v-if="item.isHot" class="hot-badge">HOT</view>
					</view>
				</view>
			</view>

			<!-- 快速入口 -->
			<view v-if="!searchKeyword" class="quick-section">
				<view class="section-header">
					<view class="header-left">
						<uni-icons type="star" size="18" color="#9C27B0"></uni-icons>
						<text class="section-title">快速发现</text>
					</view>
				</view>
				<view class="quick-grid">
					<view
						v-for="(item, index) in quickItems"
						:key="index"
						class="quick-item"
						@click="selectDiscover(item)"
					>
						<view class="quick-icon" :style="{ background: item.bgColor }">
							<uni-icons :type="item.icon" size="24" color="#fff"></uni-icons>
						</view>
						<text class="quick-title">{{ item.title }}</text>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				searchKeyword: '',
				
				// 搜索历史
				searchHistory: [],
				
				// 搜索建议
				suggestions: [],
				
				// 热门搜索
				hotSearches: [
					{ keyword: '王者荣耀', isHot: true },
					{ keyword: '周末BBQ', isHot: false },
					{ keyword: '朝阳公园跑步', isHot: true },
					{ keyword: '咖啡厅约会', isHot: false },
					{ keyword: '和平精英', isHot: true },
					{ keyword: '户外徒步', isHot: false },
					{ keyword: '桌游聚会', isHot: false },
					{ keyword: 'KTV唱歌', isHot: false }
				],

				// 快速发现
				quickItems: [
					{
						title: '附近的人',
						icon: 'location',
						bgColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
					},
					{
						title: '热门活动',
						icon: 'fire',
						bgColor: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
					},
					{
						title: '兴趣圈子',
						icon: 'heart',
						bgColor: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
					},
					{
						title: '话题广场',
						icon: 'chatbubble',
						bgColor: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
					}
				]
			}
		},

		onLoad() {
			// 获取系统信息
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
			
			// 加载搜索历史
			this.loadSearchHistory();
		},

		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 搜索输入
			onSearchInput(e) {
				const keyword = e.detail.value;
				this.searchKeyword = keyword;
				
				if (keyword.trim()) {
					this.generateSuggestions(keyword);
				} else {
					this.suggestions = [];
				}
			},

			// 搜索确认
			onSearchConfirm() {
				if (this.searchKeyword.trim()) {
					this.performSearch(this.searchKeyword.trim());
				}
			},

			// 清空搜索
			clearSearch() {
				this.searchKeyword = '';
				this.suggestions = [];
			},

			// 生成搜索建议
			generateSuggestions(keyword) {
				// 模拟搜索建议
				const allSuggestions = [
					'王者荣耀五排', '王者荣耀陪玩', '王者荣耀上分',
					'BBQ聚会', 'BBQ烧烤', 'BBQ户外',
					'跑步健身', '跑步减肥', '跑步打卡',
					'咖啡厅约会', '咖啡厅聊天', '咖啡厅工作',
					'电影院看片', '电影院约会', '电影院新片',
					'KTV唱歌', 'KTV聚会', 'KTV包厢'
				];
				
				this.suggestions = allSuggestions
					.filter(item => item.includes(keyword))
					.slice(0, 8);
			},

			// 选择搜索建议
			selectSuggestion(suggestion) {
				this.searchKeyword = suggestion;
				this.performSearch(suggestion);
			},

			// 选择历史搜索
			selectHistory(keyword) {
				this.searchKeyword = keyword;
				this.performSearch(keyword);
			},

			// 选择热门搜索
			selectHotSearch(item) {
				this.searchKeyword = item.keyword;
				this.performSearch(item.keyword);
			},

			// 选择搜索发现
			selectDiscover(item) {
				console.log('选择搜索发现:', item.title);
				// 根据不同类型跳转到对应页面
				switch(item.title) {
					case '附近的人':
						// 跳转到附近的人页面
						break;
					case '热门活动':
						// 跳转到热门活动页面
						break;
					case '兴趣圈子':
						// 跳转到兴趣圈子页面
						break;
					case '话题广场':
						// 跳转到话题广场页面
						break;
				}
			},

			// 执行搜索
			performSearch(keyword) {
				console.log('执行搜索:', keyword);
				
				// 保存到搜索历史
				this.saveToHistory(keyword);
				
				// 跳转到搜索结果页面
				uni.navigateTo({
					url: `/pages/search-result/search-result?keyword=${encodeURIComponent(keyword)}`
				});
			},

			// 保存到搜索历史
			saveToHistory(keyword) {
				// 移除重复项
				const index = this.searchHistory.indexOf(keyword);
				if (index > -1) {
					this.searchHistory.splice(index, 1);
				}
				
				// 添加到开头
				this.searchHistory.unshift(keyword);
				
				// 限制历史记录数量
				if (this.searchHistory.length > 10) {
					this.searchHistory = this.searchHistory.slice(0, 10);
				}
				
				// 保存到本地存储
				uni.setStorageSync('searchHistory', this.searchHistory);
			},

			// 加载搜索历史
			loadSearchHistory() {
				const history = uni.getStorageSync('searchHistory');
				if (history && Array.isArray(history)) {
					this.searchHistory = history;
				}
			},

			// 清空搜索历史
			clearHistory() {
				uni.showModal({
					title: '提示',
					content: '确定要清空搜索历史吗？',
					success: (res) => {
						if (res.confirm) {
							this.searchHistory = [];
							uni.removeStorageSync('searchHistory');
							uni.showToast({
								title: '已清空',
								icon: 'success'
							});
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.search-container {
		background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
		min-height: 100vh;
		position: relative;
		z-index: 1;
	}

	.status-bar {
		background: transparent;
	}

	// 搜索头部
	.search-header {
		background: #fff;
		padding: 20rpx 24rpx;
		display: flex;
		align-items: center;
		gap: 20rpx;
		box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.06);
		border-radius: 0 0 32rpx 32rpx;

		.back-btn {
			width: 44rpx;
			height: 44rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 12rpx;
			background: #f8f9fa;
			transition: all 0.3s ease;

			&:active {
				background: #e9ecef;
				transform: scale(0.95);
			}
		}

		.search-input-container {
			flex: 1;

			.search-input-wrapper {
				background: #f8f9fa;
				border-radius: 28rpx;
				padding: 18rpx 24rpx;
				display: flex;
				align-items: center;
				gap: 16rpx;
				border: 2rpx solid transparent;
				transition: all 0.3s ease;

				&:focus-within {
					background: #fff;
					border-color: #FFD700;
					box-shadow: 0 4rpx 20rpx rgba(255, 215, 0, 0.2);
				}

				.search-input {
					flex: 1;
					font-size: 30rpx;
					color: #333;
					background: transparent;
					border: none;
					outline: none;

					&::placeholder {
						color: #999;
						font-weight: 400;
					}
				}

				.clear-btn {
					width: 32rpx;
					height: 32rpx;
					border-radius: 50%;
					background: rgba(0, 0, 0, 0.1);
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.2s ease;

					&:active {
						background: rgba(0, 0, 0, 0.2);
						transform: scale(0.9);
					}
				}
			}
		}
	}

	// 搜索内容
	.search-content {
		flex: 1;
		padding: 32rpx 24rpx;
	}

	// 搜索建议
	.suggestions-section {
		background: #fff;
		border-radius: 24rpx;
		overflow: hidden;
		margin-bottom: 32rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

		.suggestion-item {
			padding: 24rpx 28rpx;
			display: flex;
			align-items: center;
			gap: 20rpx;
			border-bottom: 1rpx solid #f5f5f5;
			transition: all 0.2s ease;

			&:last-child {
				border-bottom: none;
			}

			&:active {
				background: #f8f9fa;
				transform: scale(0.98);
			}

			.suggestion-text {
				flex: 1;
				font-size: 30rpx;
				color: #333;
				font-weight: 500;
			}
		}
	}

	// 通用区块样式
	.section-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 24rpx;

		.header-left {
			display: flex;
			align-items: center;
			gap: 12rpx;
		}

		.section-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}

		.clear-btn {
			padding: 8rpx 16rpx;
			border-radius: 20rpx;
			background: #f8f9fa;
			transition: all 0.2s ease;

			&:active {
				background: #e9ecef;
				transform: scale(0.95);
			}

			.clear-text {
				font-size: 24rpx;
				color: #666;
			}
		}
	}

	// 标签容器通用样式
	.tag-container {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;

		.tag-item {
			border-radius: 24rpx;
			padding: 16rpx 24rpx;
			transition: all 0.3s ease;
			position: relative;
			overflow: hidden;

			&:active {
				transform: scale(0.95);
			}

			.tag-text {
				font-size: 28rpx;
				font-weight: 500;
			}
		}
	}

	// 历史搜索
	.history-section {
		margin-bottom: 40rpx;

		.history-tag {
			background: #fff;
			border: 2rpx solid #f0f0f0;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

			&:active {
				border-color: #FFD700;
				box-shadow: 0 4rpx 20rpx rgba(255, 215, 0, 0.2);
			}

			.tag-text {
				color: #666;
			}
		}
	}

	// 热门搜索
	.hot-section {
		margin-bottom: 40rpx;

		.hot-tag {
			background: #fff;
			border: 2rpx solid #f0f0f0;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
			display: flex;
			align-items: center;
			gap: 12rpx;

			&.top-rank {
				background: linear-gradient(135deg, #fff5e6 0%, #ffe0b2 100%);
				border-color: #FFD700;
			}

			&:active {
				border-color: #FFD700;
				box-shadow: 0 4rpx 20rpx rgba(255, 215, 0, 0.2);
			}

			.rank-number {
				width: 28rpx;
				height: 28rpx;
				background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
				color: #fff;
				font-size: 18rpx;
				font-weight: 700;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
			}

			.tag-text {
				flex: 1;
				color: #333;
			}

			.hot-badge {
				background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
				color: #fff;
				font-size: 18rpx;
				font-weight: 700;
				padding: 4rpx 8rpx;
				border-radius: 8rpx;
				box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
			}
		}
	}

	// 快速发现
	.quick-section {
		.quick-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 20rpx;

			.quick-item {
				background: #fff;
				border-radius: 24rpx;
				padding: 32rpx 24rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 16rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
				transition: all 0.3s ease;

				&:active {
					transform: translateY(-4rpx);
					box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
				}

				.quick-icon {
					width: 80rpx;
					height: 80rpx;
					border-radius: 20rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
				}

				.quick-title {
					font-size: 28rpx;
					font-weight: 600;
					color: #333;
				}
			}
		}
	}
</style>
