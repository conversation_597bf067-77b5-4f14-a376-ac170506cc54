/**
 * 屏幕适配工具类
 * 用于处理不同屏幕尺寸的适配问题
 */

class ScreenAdapter {
	constructor() {
		this.systemInfo = uni.getSystemInfoSync()
		this.init()
	}

	init() {
		// 获取系统信息
		this.screenWidth = this.systemInfo.screenWidth
		this.screenHeight = this.systemInfo.screenHeight
		this.statusBarHeight = this.systemInfo.statusBarHeight || 0
		this.platform = this.systemInfo.platform
		this.pixelRatio = this.systemInfo.pixelRatio || 1
		
		// 计算安全区域
		this.safeArea = this.systemInfo.safeArea || {}
		this.safeAreaInsets = this.systemInfo.safeAreaInsets || {}
		
		// 设计稿基准尺寸（以iPhone 6/7/8为基准：375x667）
		this.designWidth = 375
		this.designHeight = 667
		
		// 计算缩放比例
		this.scaleX = this.screenWidth / this.designWidth
		this.scaleY = this.screenHeight / this.designHeight
		this.scale = Math.min(this.scaleX, this.scaleY)
		
		// 导航栏高度（状态栏 + 标题栏）
		this.navigationBarHeight = this.getNavigationBarHeight()
		
		// 底部安全区域高度
		this.bottomSafeHeight = this.getBottomSafeHeight()
	}

	/**
	 * 获取导航栏总高度
	 */
	getNavigationBarHeight() {
		// 不同平台的导航栏高度
		const titleBarHeight = this.platform === 'ios' ? 44 : 48
		return this.statusBarHeight + titleBarHeight
	}

	/**
	 * 获取底部安全区域高度
	 */
	getBottomSafeHeight() {
		if (this.safeAreaInsets && this.safeAreaInsets.bottom) {
			return this.safeAreaInsets.bottom
		}
		// iPhone X系列底部安全区域约34px
		if (this.platform === 'ios' && this.screenHeight >= 812) {
			return 34
		}
		return 0
	}

	/**
	 * rpx转px
	 */
	rpxToPx(rpx) {
		return (rpx * this.screenWidth) / 750
	}

	/**
	 * px转rpx
	 */
	pxToRpx(px) {
		return (px * 750) / this.screenWidth
	}

	/**
	 * 获取适配后的尺寸
	 */
	getAdaptedSize(size, type = 'width') {
		const scale = type === 'width' ? this.scaleX : this.scaleY
		return Math.round(size * scale)
	}

	/**
	 * 获取状态栏样式
	 */
	getStatusBarStyle(backgroundColor = 'transparent') {
		return {
			height: this.statusBarHeight + 'px',
			backgroundColor: backgroundColor
		}
	}

	/**
	 * 获取安全区域样式
	 */
	getSafeAreaStyle() {
		return {
			paddingTop: this.statusBarHeight + 'px',
			paddingBottom: this.bottomSafeHeight + 'px'
		}
	}

	/**
	 * 获取页面容器样式
	 */
	getPageContainerStyle(hasStatusBar = true) {
		return {
			minHeight: '100vh',
			paddingTop: hasStatusBar ? this.statusBarHeight + 'px' : '0',
			paddingBottom: this.bottomSafeHeight + 'px'
		}
	}

	/**
	 * 获取响应式字体大小
	 */
	getResponsiveFontSize(baseFontSize) {
		// 基于屏幕宽度的字体缩放
		const minScale = 0.8
		const maxScale = 1.2
		let scale = this.screenWidth / this.designWidth
		scale = Math.max(minScale, Math.min(maxScale, scale))
		return Math.round(baseFontSize * scale)
	}

	/**
	 * 获取响应式间距
	 */
	getResponsiveSpacing(baseSpacing) {
		return Math.round(baseSpacing * this.scale)
	}

	/**
	 * 判断是否为小屏设备
	 */
	isSmallScreen() {
		return this.screenWidth < 375 || this.screenHeight < 667
	}

	/**
	 * 判断是否为大屏设备
	 */
	isLargeScreen() {
		return this.screenWidth > 414 || this.screenHeight > 896
	}

	/**
	 * 获取设备类型
	 */
	getDeviceType() {
		if (this.screenWidth < 375) return 'small'
		if (this.screenWidth > 414) return 'large'
		return 'medium'
	}
}

// 创建全局实例
const screenAdapter = new ScreenAdapter()

export default screenAdapter
