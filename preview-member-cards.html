<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲伴 - 会员卡片滑动系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 100%);
            min-height: 100vh;
            color: #fff;
            overflow-x: hidden;
        }
        
        .member-card-section {
            padding: 12px 0;
        }
        
        .level-indicator {
            padding: 0 12px 12px;
            text-align: center;
        }
        
        .indicator-line {
            position: relative;
            height: 2px;
            margin-bottom: 8px;
        }
        
        .indicator-track {
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1px;
        }
        
        .indicator-dot {
            position: absolute;
            top: -3px;
            width: 8px;
            height: 8px;
            background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
            border-radius: 50%;
            box-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }
        
        .current-level-text {
            font-size: 14px;
            color: #FFD700;
            font-weight: 700;
            text-shadow: 0 1px 4px rgba(255, 215, 0, 0.3);
        }
        
        .member-cards-container {
            position: relative;
            height: 200px;
            overflow: hidden;
        }
        
        .cards-wrapper {
            display: flex;
            transition: transform 0.3s ease;
            height: 100%;
        }
        
        .card-item {
            min-width: 85%;
            padding: 0 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .member-card {
            width: 100%;
            height: 180px;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            transform: scale(0.9);
            opacity: 0.7;
            cursor: pointer;
        }
        
        .member-card.active {
            transform: scale(1);
            opacity: 1;
        }
        
        .member-card.current {
            box-shadow: 0 8px 24px rgba(255, 215, 0, 0.4);
        }
        
        .member-card.gold {
            background: linear-gradient(135deg, #FFD700 0%, #FFA000 50%, #FF8F00 100%);
        }
        
        .member-card.diamond {
            background: linear-gradient(135deg, #00E5FF 0%, #00BCD4 50%, #0097A7 100%);
        }
        
        .member-card.platinum {
            background: linear-gradient(135deg, #E8EAF6 0%, #C5CAE9 50%, #9FA8DA 100%);
        }
        
        .member-card.black-gold {
            background: linear-gradient(135deg, #212121 0%, #424242 30%, #FFD700 70%, #FFA000 100%);
        }
        
        .card-decorations {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }
        
        .decoration-pattern {
            position: absolute;
            border-radius: 50%;
            opacity: 0.1;
        }
        
        .pattern-1 {
            width: 100px;
            height: 100px;
            top: -50px;
            right: -25px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: float 8s ease-in-out infinite;
        }
        
        .pattern-2 {
            width: 60px;
            height: 60px;
            bottom: -30px;
            left: -15px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
            animation: float 10s ease-in-out infinite reverse;
        }
        
        .pattern-3 {
            width: 40px;
            height: 40px;
            top: 50%;
            left: 20%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        .level-watermark {
            position: absolute;
            bottom: 10px;
            right: 10px;
            font-size: 60px;
            font-weight: 900;
            color: rgba(255, 255, 255, 0.05);
            line-height: 1;
        }
        
        .card-content {
            position: relative;
            z-index: 2;
            padding: 12px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        
        .level-badge {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            color: white;
            font-size: 16px;
        }
        
        .card-status {
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }
        
        .card-status:not(.locked) {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
        }
        
        .card-status.locked {
            background: rgba(158, 158, 158, 0.2);
            color: #999;
        }
        
        .member-info {
            flex: 1;
        }
        
        .level-name {
            font-size: 18px;
            font-weight: 700;
            color: white;
            margin-bottom: 4px;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
        }
        
        .level-desc {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 8px;
            line-height: 1.4;
        }
        
        .level-price {
            display: flex;
            align-items: baseline;
            gap: 2px;
        }
        
        .price-text {
            font-size: 20px;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
        }
        
        .price-unit {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .user-avatar-section {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 8px;
            backdrop-filter: blur(10px);
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.5);
            object-fit: cover;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-size: 12px;
            color: white;
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .expire-date {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .privileges-preview {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .privilege-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .navigation-dots {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 16px;
        }
        
        .nav-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-dot.active {
            background: #FFD700;
            box-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-5px);
            }
        }
        
        .demo-info {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            color: #FFD700;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="member-card-section">
        <div class="level-indicator">
            <div class="indicator-line">
                <div class="indicator-dot" id="indicatorDot"></div>
                <div class="indicator-track"></div>
            </div>
            <div class="current-level-text" id="currentLevelText">黄金会员</div>
        </div>
        
        <div class="member-cards-container">
            <div class="cards-wrapper" id="cardsWrapper">
                <!-- 黄金会员卡片 -->
                <div class="card-item">
                    <div class="member-card gold active" data-level="1">
                        <div class="card-decorations">
                            <div class="decoration-pattern pattern-1"></div>
                            <div class="decoration-pattern pattern-2"></div>
                            <div class="decoration-pattern pattern-3"></div>
                            <div class="level-watermark">1</div>
                        </div>
                        <div class="card-content">
                            <div class="card-header">
                                <div class="level-badge">🏅</div>
                                <div class="card-status">未解锁</div>
                            </div>
                            <div class="member-info">
                                <div class="level-name">黄金会员</div>
                                <div class="level-desc">入门级会员，享受基础特权</div>
                                <div class="level-price">
                                    <span class="price-text">¥19</span>
                                    <span class="price-unit">/月</span>
                                </div>
                            </div>
                            <div class="privileges-preview">
                                <div class="privilege-item">
                                    <span>⭐</span>
                                    <span>专属身份标识</span>
                                </div>
                                <div class="privilege-item">
                                    <span>🔥</span>
                                    <span>优先匹配推荐</span>
                                </div>
                                <div class="privilege-item">
                                    <span>❌</span>
                                    <span>去除广告</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 钻石会员卡片 -->
                <div class="card-item">
                    <div class="member-card diamond current" data-level="2">
                        <div class="card-decorations">
                            <div class="decoration-pattern pattern-1"></div>
                            <div class="decoration-pattern pattern-2"></div>
                            <div class="decoration-pattern pattern-3"></div>
                            <div class="level-watermark">2</div>
                        </div>
                        <div class="card-content">
                            <div class="card-header">
                                <div class="level-badge">⭐</div>
                                <div class="card-status">当前</div>
                            </div>
                            <div class="member-info">
                                <div class="level-name">钻石会员</div>
                                <div class="level-desc">进阶会员，解锁更多功能</div>
                                <div class="level-price">
                                    <span class="price-text">¥39</span>
                                    <span class="price-unit">/月</span>
                                </div>
                            </div>
                            <div class="user-avatar-section">
                                <img class="user-avatar" src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="用户头像">
                                <div class="user-info">
                                    <div class="user-name">闲伴用户</div>
                                    <div class="expire-date">有效期至 2025-03-15</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 白金会员卡片 -->
                <div class="card-item">
                    <div class="member-card platinum" data-level="3">
                        <div class="card-decorations">
                            <div class="decoration-pattern pattern-1"></div>
                            <div class="decoration-pattern pattern-2"></div>
                            <div class="decoration-pattern pattern-3"></div>
                            <div class="level-watermark">3</div>
                        </div>
                        <div class="card-content">
                            <div class="card-header">
                                <div class="level-badge">👑</div>
                                <div class="card-status locked">未解锁</div>
                            </div>
                            <div class="member-info">
                                <div class="level-name">白金会员</div>
                                <div class="level-desc">高级会员，尊享专属服务</div>
                                <div class="level-price">
                                    <span class="price-text">¥69</span>
                                    <span class="price-unit">/月</span>
                                </div>
                            </div>
                            <div class="privileges-preview">
                                <div class="privilege-item">
                                    <span>📊</span>
                                    <span>数据统计分析</span>
                                </div>
                                <div class="privilege-item">
                                    <span>📅</span>
                                    <span>专属活动邀请</span>
                                </div>
                                <div class="privilege-item">
                                    <span>👻</span>
                                    <span>隐身浏览模式</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 黑金会员卡片 -->
                <div class="card-item">
                    <div class="member-card black-gold" data-level="4">
                        <div class="card-decorations">
                            <div class="decoration-pattern pattern-1"></div>
                            <div class="decoration-pattern pattern-2"></div>
                            <div class="decoration-pattern pattern-3"></div>
                            <div class="level-watermark">4</div>
                        </div>
                        <div class="card-content">
                            <div class="card-header">
                                <div class="level-badge">💼</div>
                                <div class="card-status locked">未解锁</div>
                            </div>
                            <div class="member-info">
                                <div class="level-name">黑金会员</div>
                                <div class="level-desc">顶级会员，至尊无上体验</div>
                                <div class="level-price">
                                    <span class="price-text">¥99</span>
                                    <span class="price-unit">/月</span>
                                </div>
                            </div>
                            <div class="privileges-preview">
                                <div class="privilege-item">
                                    <span>👨‍💼</span>
                                    <span>专属管家服务</span>
                                </div>
                                <div class="privilege-item">
                                    <span>🤖</span>
                                    <span>定制化推荐</span>
                                </div>
                                <div class="privilege-item">
                                    <span>🎪</span>
                                    <span>线下活动优先</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="navigation-dots">
            <div class="nav-dot active" data-index="0"></div>
            <div class="nav-dot" data-index="1"></div>
            <div class="nav-dot" data-index="2"></div>
            <div class="nav-dot" data-index="3"></div>
        </div>
    </div>
    
    <div class="demo-info">
        🎨 会员卡片滑动系统 - 左右滑动查看不同等级
    </div>
    
    <script>
        const cardsWrapper = document.getElementById('cardsWrapper');
        const indicatorDot = document.getElementById('indicatorDot');
        const currentLevelText = document.getElementById('currentLevelText');
        const navDots = document.querySelectorAll('.nav-dot');
        const memberCards = document.querySelectorAll('.member-card');
        
        const levelNames = ['黄金会员', '钻石会员', '白金会员', '黑金会员'];
        let currentIndex = 0;
        
        function updateIndicator(index) {
            const position = (index / 3) * 100;
            indicatorDot.style.left = position + '%';
            currentLevelText.textContent = levelNames[index];
        }
        
        function updateCards(index) {
            const translateX = -index * 85;
            cardsWrapper.style.transform = `translateX(${translateX}%)`;
            
            memberCards.forEach((card, i) => {
                card.classList.toggle('active', i === index);
            });
            
            navDots.forEach((dot, i) => {
                dot.classList.toggle('active', i === index);
            });
        }
        
        function goToSlide(index) {
            currentIndex = index;
            updateIndicator(index);
            updateCards(index);
        }
        
        // 导航点击事件
        navDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                goToSlide(index);
            });
        });
        
        // 卡片点击事件
        memberCards.forEach((card, index) => {
            card.addEventListener('click', () => {
                goToSlide(index);
            });
        });
        
        // 触摸滑动支持
        let startX = 0;
        let isDragging = false;
        
        cardsWrapper.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            isDragging = true;
        });
        
        cardsWrapper.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            e.preventDefault();
        });
        
        cardsWrapper.addEventListener('touchend', (e) => {
            if (!isDragging) return;
            isDragging = false;
            
            const endX = e.changedTouches[0].clientX;
            const diffX = startX - endX;
            
            if (Math.abs(diffX) > 50) {
                if (diffX > 0 && currentIndex < 3) {
                    goToSlide(currentIndex + 1);
                } else if (diffX < 0 && currentIndex > 0) {
                    goToSlide(currentIndex - 1);
                }
            }
        });
        
        // 初始化
        updateIndicator(0);
        updateCards(0);
    </script>
</body>
</html>
