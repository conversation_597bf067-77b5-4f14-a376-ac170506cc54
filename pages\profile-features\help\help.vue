<template>
	<view class="help-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<uni-icons type="left" size="24" color="#333"></uni-icons>
					<text class="back-text">返回</text>
				</view>
				<text class="nav-title">帮助与反馈</text>
				<view class="nav-right"></view>
			</view>
		</view>

		<!-- 搜索框 -->
		<view class="search-section">
			<view class="search-box">
				<uni-icons type="search" size="20" color="#999"></uni-icons>
				<input class="search-input" placeholder="搜索帮助内容" v-model="searchKeyword" @input="searchHelp" />
			</view>
		</view>

		<!-- 帮助内容 -->
		<scroll-view class="help-content" scroll-y="true">
			<!-- 常见问题 -->
			<view class="help-section">
				<text class="section-title">常见问题</text>
				<view class="faq-list">
					<view class="faq-item" v-for="faq in filteredFaqs" :key="faq.id" @tap="toggleFaq(faq.id)">
						<view class="faq-question">
							<text class="question-text">{{ faq.question }}</text>
							<uni-icons :type="faq.expanded ? 'up' : 'down'" size="16" color="#666"></uni-icons>
						</view>
						<view class="faq-answer" v-if="faq.expanded">
							<text class="answer-text">{{ faq.answer }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 功能指南 -->
			<view class="help-section">
				<text class="section-title">功能指南</text>
				<view class="guide-list">
					<view class="guide-item" v-for="guide in guides" :key="guide.id" @tap="viewGuide(guide)">
						<view class="guide-icon" :class="guide.iconClass">
							<uni-icons :type="guide.icon" size="24" color="#fff"></uni-icons>
						</view>
						<view class="guide-info">
							<text class="guide-title">{{ guide.title }}</text>
							<text class="guide-desc">{{ guide.description }}</text>
						</view>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 联系我们 -->
			<view class="help-section">
				<text class="section-title">联系我们</text>
				<view class="contact-list">
					<view class="contact-item" @tap="contactCustomerService">
						<view class="contact-icon customer-service">
							<uni-icons type="chatbubble-filled" size="24" color="#fff"></uni-icons>
						</view>
						<view class="contact-info">
							<text class="contact-title">在线客服</text>
							<text class="contact-desc">7x24小时在线服务</text>
						</view>
						<view class="contact-status online">
							<text class="status-text">在线</text>
						</view>
					</view>

					<view class="contact-item" @tap="callPhone">
						<view class="contact-icon phone">
							<uni-icons type="phone-filled" size="24" color="#fff"></uni-icons>
						</view>
						<view class="contact-info">
							<text class="contact-title">客服热线</text>
							<text class="contact-desc">************</text>
						</view>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>

					<view class="contact-item" @tap="sendEmail">
						<view class="contact-icon email">
							<uni-icons type="email-filled" size="24" color="#fff"></uni-icons>
						</view>
						<view class="contact-info">
							<text class="contact-title">邮箱反馈</text>
							<text class="contact-desc"><EMAIL></text>
						</view>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 意见反馈 -->
			<view class="help-section">
				<text class="section-title">意见反馈</text>
				<view class="feedback-form">
					<view class="form-item">
						<text class="form-label">反馈类型</text>
						<picker :value="feedbackTypeIndex" :range="feedbackTypes" @change="selectFeedbackType">
							<view class="picker-content">
								<text class="picker-text">{{ feedbackTypes[feedbackTypeIndex] }}</text>
								<uni-icons type="down" size="16" color="#999"></uni-icons>
							</view>
						</picker>
					</view>

					<view class="form-item">
						<text class="form-label">问题描述</text>
						<textarea 
							class="feedback-textarea" 
							placeholder="请详细描述您遇到的问题或建议..." 
							v-model="feedbackContent"
							maxlength="500">
						</textarea>
						<text class="char-count">{{ feedbackContent.length }}/500</text>
					</view>

					<view class="form-item">
						<text class="form-label">联系方式（选填）</text>
						<input class="feedback-input" placeholder="手机号或邮箱" v-model="contactInfo" />
					</view>

					<view class="submit-btn" @tap="submitFeedback">
						<text class="btn-text">提交反馈</text>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				searchKeyword: '',
				feedbackTypeIndex: 0,
				feedbackTypes: ['功能建议', '问题反馈', '界面优化', '性能问题', '其他'],
				feedbackContent: '',
				contactInfo: '',
				
				// 常见问题
				faqs: [
					{
						id: 1,
						question: '如何修改个人资料？',
						answer: '进入个人主页，点击编辑按钮即可修改头像、昵称、个人简介等信息。',
						expanded: false
					},
					{
						id: 2,
						question: '如何参加活动？',
						answer: '在发现页面浏览活动，点击感兴趣的活动进入详情页，然后点击"立即参加"按钮即可报名。',
						expanded: false
					},
					{
						id: 3,
						question: '如何成为VIP会员？',
						answer: '进入个人主页，点击VIP中心，选择合适的会员套餐进行购买即可享受VIP特权。',
						expanded: false
					},
					{
						id: 4,
						question: '如何发布作品？',
						answer: '在发现页面点击发布按钮，选择图文、视频或音频类型，编辑内容后发布即可。',
						expanded: false
					},
					{
						id: 5,
						question: '忘记密码怎么办？',
						answer: '在登录页面点击"忘记密码"，通过手机验证码或邮箱验证重置密码。',
						expanded: false
					}
				],

				// 功能指南
				guides: [
					{
						id: 1,
						title: '新手入门',
						description: '快速了解闲伴的基本功能',
						icon: 'star',
						iconClass: 'guide-star'
					},
					{
						id: 2,
						title: '活动参与',
						description: '如何参加和组织活动',
						icon: 'calendar',
						iconClass: 'guide-calendar'
					},
					{
						id: 3,
						title: '内容创作',
						description: '发布优质内容的技巧',
						icon: 'compose',
						iconClass: 'guide-compose'
					},
					{
						id: 4,
						title: '社交互动',
						description: '结识更多志同道合的朋友',
						icon: 'person-add',
						iconClass: 'guide-social'
					}
				]
			}
		},

		computed: {
			filteredFaqs() {
				if (!this.searchKeyword) {
					return this.faqs;
				}
				return this.faqs.filter(faq => 
					faq.question.includes(this.searchKeyword) || 
					faq.answer.includes(this.searchKeyword)
				);
			}
		},

		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},

		methods: {
			goBack() {
				uni.switchTab({
					url: '/pages/profile/profile'
				});
			},

			searchHelp() {
				// 搜索逻辑已在computed中实现
			},

			toggleFaq(id) {
				const faq = this.faqs.find(item => item.id === id);
				if (faq) {
					faq.expanded = !faq.expanded;
				}
			},

			viewGuide(guide) {
				uni.showToast({
					title: `${guide.title}指南开发中`,
					icon: 'none'
				});
			},

			contactCustomerService() {
				uni.showModal({
					title: '联系客服',
					content: '即将为您转接在线客服，请稍候...',
					showCancel: false,
					success: () => {
						uni.showToast({
							title: '客服功能开发中',
							icon: 'none'
						});
					}
				});
			},

			callPhone() {
				uni.makePhoneCall({
					phoneNumber: '************',
					fail: () => {
						uni.showToast({
							title: '拨号失败',
							icon: 'none'
						});
					}
				});
			},

			sendEmail() {
				uni.setClipboardData({
					data: '<EMAIL>',
					success: () => {
						uni.showToast({
							title: '邮箱地址已复制',
							icon: 'success'
						});
					}
				});
			},

			selectFeedbackType(e) {
				this.feedbackTypeIndex = e.detail.value;
			},

			submitFeedback() {
				if (!this.feedbackContent.trim()) {
					uni.showToast({
						title: '请填写问题描述',
						icon: 'none'
					});
					return;
				}

				uni.showLoading({
					title: '提交中...'
				});

				// 模拟提交
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: '反馈提交成功',
						icon: 'success'
					});
					
					// 清空表单
					this.feedbackContent = '';
					this.contactInfo = '';
					this.feedbackTypeIndex = 0;
				}, 2000);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.help-page {
		background: #f5f5f5;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.status-bar {
		background: #fff;
	}

	.header {
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;

		.nav-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;
			height: 88rpx;

			.nav-left {
				display: flex;
				align-items: center;
				gap: 8rpx;
				flex: 1;

				.back-text {
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				text-align: center;
				flex: 2;
			}

			.nav-right {
				flex: 1;
			}
		}
	}

	// 搜索区域
	.search-section {
		background: #fff;
		padding: 16rpx 24rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.search-box {
			background: #f8f9fa;
			border-radius: 24rpx;
			padding: 16rpx 20rpx;
			display: flex;
			align-items: center;
			gap: 12rpx;

			.search-input {
				flex: 1;
				font-size: 28rpx;
				color: #333;

				&::placeholder {
					color: #999;
				}
			}
		}
	}

	// 帮助内容区域
	.help-content {
		flex: 1;
		padding: 16rpx 24rpx 120rpx;

		.help-section {
			margin-bottom: 32rpx;

			.section-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 700;
				margin-bottom: 16rpx;
				padding-left: 8rpx;
				display: block;
			}

			// 常见问题
			.faq-list {
				background: #fff;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.faq-item {
					border-bottom: 1rpx solid #f5f5f5;

					&:last-child {
						border-bottom: none;
					}

					.faq-question {
						padding: 24rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;
						transition: all 0.3s ease;

						&:active {
							background: #f8f9fa;
						}

						.question-text {
							font-size: 28rpx;
							color: #333;
							font-weight: 500;
							flex: 1;
							line-height: 1.4;
						}
					}

					.faq-answer {
						padding: 0 24rpx 24rpx;
						animation: fadeIn 0.3s ease;

						.answer-text {
							font-size: 26rpx;
							color: #666;
							line-height: 1.6;
						}
					}
				}
			}

			// 功能指南
			.guide-list {
				background: #fff;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.guide-item {
					display: flex;
					align-items: center;
					padding: 24rpx;
					border-bottom: 1rpx solid #f5f5f5;
					transition: all 0.3s ease;

					&:last-child {
						border-bottom: none;
					}

					&:active {
						background: #f8f9fa;
					}

					.guide-icon {
						width: 56rpx;
						height: 56rpx;
						border-radius: 28rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 16rpx;

						&.guide-star {
							background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
						}

						&.guide-calendar {
							background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
						}

						&.guide-compose {
							background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
						}

						&.guide-social {
							background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
						}
					}

					.guide-info {
						flex: 1;

						.guide-title {
							font-size: 30rpx;
							color: #333;
							font-weight: 600;
							display: block;
							margin-bottom: 8rpx;
						}

						.guide-desc {
							font-size: 24rpx;
							color: #999;
							line-height: 1.4;
						}
					}
				}
			}

			// 联系我们
			.contact-list {
				background: #fff;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.contact-item {
					display: flex;
					align-items: center;
					padding: 24rpx;
					border-bottom: 1rpx solid #f5f5f5;
					transition: all 0.3s ease;

					&:last-child {
						border-bottom: none;
					}

					&:active {
						background: #f8f9fa;
					}

					.contact-icon {
						width: 56rpx;
						height: 56rpx;
						border-radius: 28rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 16rpx;

						&.customer-service {
							background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
						}

						&.phone {
							background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
						}

						&.email {
							background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
						}
					}

					.contact-info {
						flex: 1;

						.contact-title {
							font-size: 30rpx;
							color: #333;
							font-weight: 600;
							display: block;
							margin-bottom: 8rpx;
						}

						.contact-desc {
							font-size: 24rpx;
							color: #999;
							line-height: 1.4;
						}
					}

					.contact-status {
						margin-right: 16rpx;

						&.online {
							background: #4ecdc4;
							color: #fff;
							font-size: 20rpx;
							padding: 4rpx 12rpx;
							border-radius: 12rpx;

							.status-text {
								font-size: 20rpx;
							}
						}
					}
				}
			}

			// 意见反馈表单
			.feedback-form {
				background: #fff;
				border-radius: 16rpx;
				padding: 24rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.form-item {
					margin-bottom: 24rpx;
					position: relative;

					&:last-child {
						margin-bottom: 0;
					}

					.form-label {
						font-size: 28rpx;
						color: #333;
						font-weight: 600;
						display: block;
						margin-bottom: 12rpx;
					}

					.picker-content {
						background: #f8f9fa;
						border-radius: 12rpx;
						padding: 16rpx 20rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.picker-text {
							font-size: 28rpx;
							color: #333;
						}
					}

					.feedback-textarea {
						background: #f8f9fa;
						border-radius: 12rpx;
						padding: 16rpx 20rpx;
						font-size: 28rpx;
						color: #333;
						min-height: 200rpx;
						width: 100%;
						box-sizing: border-box;

						&::placeholder {
							color: #999;
						}
					}

					.feedback-input {
						background: #f8f9fa;
						border-radius: 12rpx;
						padding: 16rpx 20rpx;
						font-size: 28rpx;
						color: #333;

						&::placeholder {
							color: #999;
						}
					}

					.char-count {
						position: absolute;
						bottom: 12rpx;
						right: 20rpx;
						font-size: 22rpx;
						color: #999;
					}
				}

				.submit-btn {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					border-radius: 24rpx;
					padding: 20rpx;
					text-align: center;
					margin-top: 32rpx;
					box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);

					&:active {
						transform: scale(0.98);
					}

					.btn-text {
						font-size: 32rpx;
						color: #fff;
						font-weight: 600;
					}
				}
			}
		}
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(-10rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
</style>
