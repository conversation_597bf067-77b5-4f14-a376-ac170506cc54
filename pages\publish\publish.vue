<template>
	<view class="publish-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<!-- 顶部导航栏 -->
		<view class="top-header">
			<view class="header-left" @click="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
				<text class="back-text">取消</text>
			</view>
			<text class="header-title">发布作品</text>
			<view class="header-right" @click="publishPost">
				<text class="publish-text" :class="{ disabled: !canPublish }">发布</text>
			</view>
		</view>

		<!-- 内容类型选择 -->
		<view class="content-type-tabs">
			<view class="type-tab" v-for="type in contentTypes" :key="type.id" :class="{ active: currentContentType === type.id }" @click="switchContentType(type.id)">
				<uni-icons :type="type.icon" size="20" :color="currentContentType === type.id ? '#FFD700' : '#999'"></uni-icons>
				<text class="type-text">{{ type.name }}</text>
			</view>
		</view>

		<!-- 内容区域 -->
		<scroll-view class="content-area" scroll-y>
			<!-- 标题输入 -->
			<view class="title-section">
				<input class="title-input" v-model="postTitle" placeholder="添加标题，让更多人发现你的作品" :maxlength="50" />
				<text class="title-count">{{ postTitle.length }}/50</text>
			</view>

			<!-- 描述输入 -->
			<view class="desc-section">
				<textarea class="desc-input" v-model="postDescription" placeholder="添加描述..." :maxlength="1000" auto-height></textarea>
				<text class="desc-count">{{ postDescription.length }}/1000</text>
			</view>

			<!-- 媒体内容区域 -->
			<view class="media-section">
				<!-- 图片/图集模式 -->
				<view v-if="currentContentType === 'image'" class="image-content">
					<view class="media-header">
						<text class="media-title">图片内容</text>
						<text class="media-tip">最多选择9张图片</text>
					</view>
					<view class="image-grid">
						<view class="image-item" v-for="(image, index) in selectedImages" :key="index">
							<image class="preview-image" :src="image.url" mode="aspectFill"></image>
							<view class="image-actions">
								<view class="action-btn" @click="editImageCover(index)" v-if="index === 0">
									<uni-icons type="image" size="14" color="#fff"></uni-icons>
									<text class="action-text">封面</text>
								</view>
								<view class="action-btn delete" @click="removeImage(index)">
									<uni-icons type="close" size="14" color="#fff"></uni-icons>
								</view>
							</view>
						</view>

						<!-- 添加图片按钮 -->
						<view
							class="add-media-btn"
							v-if="selectedImages.length < 9"
							@click="chooseImage"
						>
							<uni-icons type="plus" size="32" color="#999"></uni-icons>
							<text class="add-text">添加图片</text>
						</view>
					</view>
				</view>

				<!-- 视频模式 -->
				<view v-if="currentContentType === 'video'" class="video-content">
					<view class="media-header">
						<text class="media-title">视频内容</text>
						<text class="media-tip">支持mp4格式，最大100MB</text>
					</view>
					<view v-if="selectedVideo" class="video-preview">
						<video
							class="preview-video"
							:src="selectedVideo.url"
							controls
							:poster="selectedVideo.cover"
						></video>
						<view class="video-actions">
							<view class="action-btn" @click="editVideoCover">
								<uni-icons type="image" size="14" color="#fff"></uni-icons>
								<text class="action-text">编辑封面</text>
							</view>
							<view class="action-btn delete" @click="removeVideo">
								<uni-icons type="close" size="14" color="#fff"></uni-icons>
							</view>
						</view>
					</view>
					<view v-else class="add-media-btn large" @click="chooseVideo">
						<uni-icons type="videocam" size="48" color="#999"></uni-icons>
						<text class="add-text">选择视频</text>
					</view>
				</view>

				<!-- 纯文字模式 -->
				<view v-if="currentContentType === 'text'" class="text-content">
					<view class="media-header">
						<text class="media-title">文字内容</text>
						<text class="media-tip">分享你的想法和故事</text>
					</view>
					<textarea
						class="text-content-input"
						v-model="textContent"
						placeholder="在这里写下你想分享的内容..."
						:maxlength="2000"
						auto-height
					></textarea>
					<text class="text-count">{{ textContent.length }}/2000</text>
				</view>
			</view>

			<!-- 功能选项 -->
			<view class="options-section">
				<!-- @朋友 -->
				<view class="option-item" @click="mentionFriends">
					<view class="option-left">
						<uni-icons type="person-filled" size="20" color="#FFD700"></uni-icons>
						<text class="option-text">@朋友</text>
					</view>
					<view class="option-right">
						<text class="option-value" v-if="mentionedFriends.length > 0">
							已选择{{ mentionedFriends.length }}位朋友
						</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>

				<!-- 添加位置 -->
				<view class="option-item" @click="chooseLocation">
					<view class="option-left">
						<uni-icons type="location-filled" size="20" color="#FFD700"></uni-icons>
						<text class="option-text">添加位置</text>
					</view>
					<view class="option-right">
						<text class="option-value">{{ currentLocation || '选择位置' }}</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>

				<!-- 添加话题 -->
				<view class="option-item" @click="addTopic">
					<view class="option-left">
						<uni-icons type="chatboxes-filled" size="20" color="#FFD700"></uni-icons>
						<text class="option-text">添加话题</text>
					</view>
					<view class="option-right">
						<text class="option-value" v-if="selectedTopics.length > 0">
							已选择{{ selectedTopics.length }}个话题
						</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>

				<!-- 隐私设置 -->
				<view class="option-item" @click="showPrivacySettings">
					<view class="option-left">
						<uni-icons type="eye-filled" size="20" color="#FFD700"></uni-icons>
						<text class="option-text">隐私设置</text>
					</view>
					<view class="option-right">
						<text class="option-value">{{ privacySettings[currentPrivacy].name }}</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 已选择的话题 -->
			<view class="selected-topics" v-if="selectedTopics.length > 0">
				<text class="topics-title">已选择的话题：</text>
				<view class="topics-list">
					<view
						class="topic-tag selected"
						v-for="topicId in selectedTopics"
						:key="topicId"
						@click="removeTopic(topicId)"
					>
						<text class="topic-text"># {{ getTopicName(topicId) }}</text>
						<uni-icons type="close" size="14" color="#333"></uni-icons>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 隐私设置弹窗 -->
		<view v-if="showPrivacyModal" class="modal-overlay" @click="hidePrivacySettings">
			<view class="privacy-modal" @click.stop>
				<view class="modal-header">
					<text class="modal-title">隐私设置</text>
					<view class="close-btn" @click="hidePrivacySettings">
						<uni-icons type="close" size="20" color="#999"></uni-icons>
					</view>
				</view>
				<view class="privacy-options">
					<view
						class="privacy-option"
						v-for="(setting, key) in privacySettings"
						:key="key"
						:class="{ active: currentPrivacy === key }"
						@click="selectPrivacy(key)"
					>
						<view class="option-info">
							<uni-icons :type="setting.icon" size="24" :color="currentPrivacy === key ? '#FFD700' : '#666'"></uni-icons>
							<view class="option-content">
								<text class="option-name">{{ setting.name }}</text>
								<text class="option-desc">{{ setting.description }}</text>
							</view>
						</view>
						<view class="option-check" v-if="currentPrivacy === key">
							<uni-icons type="checkmarkempty" size="20" color="#FFD700"></uni-icons>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PublishPage',
	data() {
		return {
			statusBarHeight: 0,

			// 内容类型
			currentContentType: 'image', // image, video, text
			contentTypes: [
				{ id: 'image', name: '图片', icon: 'image' },
				{ id: 'video', name: '视频', icon: 'videocam' },
				{ id: 'text', name: '文字', icon: 'compose' }
			],

			// 基本信息
			postTitle: '',
			postDescription: '',
			textContent: '',

			// 媒体内容
			selectedImages: [],
			selectedVideo: null,

			// 功能选项
			currentLocation: '',
			selectedTopics: [],
			mentionedFriends: [],

			// 隐私设置
			currentPrivacy: 'public',
			showPrivacyModal: false,
			privacySettings: {
				public: {
					name: '公开',
					description: '所有人都可以看到',
					icon: 'eye'
				},
				friends: {
					name: '互相关注的人可见',
					description: '只有互相关注的朋友可以看到',
					icon: 'person'
				},
				private: {
					name: '私密·仅自己可见',
					description: '只有自己可以看到',
					icon: 'locked'
				}
			},

			// 话题数据
			allTopics: [
				{ id: 1, name: '露营' },
				{ id: 2, name: '美食' },
				{ id: 3, name: '旅行' },
				{ id: 4, name: '健身' },
				{ id: 5, name: '摄影' },
				{ id: 6, name: '生活' },
				{ id: 7, name: '音乐' },
				{ id: 8, name: '电影' },
				{ id: 9, name: '读书' },
				{ id: 10, name: '游戏' }
			]
		}
	},

	computed: {
		// 是否可以发布
		canPublish() {
			if (this.currentContentType === 'image') {
				return this.selectedImages.length > 0 || this.postTitle.trim() || this.postDescription.trim();
			} else if (this.currentContentType === 'video') {
				return this.selectedVideo || this.postTitle.trim() || this.postDescription.trim();
			} else if (this.currentContentType === 'text') {
				return this.textContent.trim() || this.postTitle.trim() || this.postDescription.trim();
			}
			return false;
		}
	},
	
	onLoad() {
		// 获取系统信息
		uni.getSystemInfo({
			success: (res) => {
				this.statusBarHeight = res.statusBarHeight;
			}
		});
	},

	methods: {
		// 切换内容类型
		switchContentType(type) {
			if (this.hasContent()) {
				uni.showModal({
					title: '切换内容类型',
					content: '切换后当前内容将被清空，确定要切换吗？',
					success: (res) => {
						if (res.confirm) {
							this.clearContent();
							this.currentContentType = type;
						}
					}
				});
			} else {
				this.currentContentType = type;
			}
		},

		// 检查是否有内容
		hasContent() {
			return this.selectedImages.length > 0 ||
				   this.selectedVideo ||
				   this.textContent.trim() ||
				   this.postTitle.trim() ||
				   this.postDescription.trim();
		},

		// 清空内容
		clearContent() {
			this.selectedImages = [];
			this.selectedVideo = null;
			this.textContent = '';
			this.postTitle = '';
			this.postDescription = '';
		},

		// 返回上一页
		goBack() {
			if (this.hasContent()) {
				uni.showModal({
					title: '确认离开',
					content: '离开后内容将不会保存，确定要离开吗？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack();
						}
					}
				});
			} else {
				uni.navigateBack();
			}
		},
		
		// 选择图片
		chooseImage() {
			const remainingCount = 9 - this.selectedImages.length;
			uni.chooseImage({
				count: remainingCount,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					const newImages = res.tempFilePaths.map(url => ({
						url: url,
						isCover: this.selectedImages.length === 0 // 第一张作为封面
					}));
					this.selectedImages = [...this.selectedImages, ...newImages];
				}
			});
		},

		// 删除图片
		removeImage(index) {
			this.selectedImages.splice(index, 1);
			// 如果删除的是封面，将第一张设为封面
			if (this.selectedImages.length > 0) {
				this.selectedImages[0].isCover = true;
			}
		},

		// 编辑图片封面
		editImageCover(index) {
			uni.showToast({
				title: '封面编辑功能开发中',
				icon: 'none'
			});
		},

		// 选择视频
		chooseVideo() {
			uni.chooseVideo({
				sourceType: ['album', 'camera'],
				maxDuration: 300, // 最大5分钟
				success: (res) => {
					this.selectedVideo = {
						url: res.tempFilePath,
						cover: res.thumbTempFilePath || '',
						duration: res.duration,
						size: res.size
					};
				}
			});
		},

		// 删除视频
		removeVideo() {
			this.selectedVideo = null;
		},

		// 编辑视频封面
		editVideoCover() {
			uni.showToast({
				title: '视频封面编辑功能开发中',
				icon: 'none'
			});
		},

		// @朋友
		mentionFriends() {
			uni.showToast({
				title: '@朋友功能开发中',
				icon: 'none'
			});
		},

		// 选择位置
		chooseLocation() {
			// 跳转到城市选择页面
			uni.navigateTo({
				url: '/pages/city-select/city-select?from=publish'
			});
		},

		// 添加话题
		addTopic() {
			const topicNames = this.allTopics.map(topic => topic.name);
			uni.showActionSheet({
				itemList: topicNames,
				success: (res) => {
					const selectedTopic = this.allTopics[res.tapIndex];
					if (!this.selectedTopics.includes(selectedTopic.id)) {
						this.selectedTopics.push(selectedTopic.id);
					}
				}
			});
		},

		// 移除话题
		removeTopic(topicId) {
			const index = this.selectedTopics.indexOf(topicId);
			if (index > -1) {
				this.selectedTopics.splice(index, 1);
			}
		},

		// 获取话题名称
		getTopicName(topicId) {
			const topic = this.allTopics.find(t => t.id === topicId);
			return topic ? topic.name : '';
		},
		
		// 显示隐私设置
		showPrivacySettings() {
			this.showPrivacyModal = true;
		},

		// 隐藏隐私设置
		hidePrivacySettings() {
			this.showPrivacyModal = false;
		},

		// 选择隐私设置
		selectPrivacy(privacy) {
			this.currentPrivacy = privacy;
			this.hidePrivacySettings();
		},

		// 发布动态
		publishPost() {
			if (!this.canPublish) {
				uni.showToast({
					title: '请添加内容后再发布',
					icon: 'none'
				});
				return;
			}

			// 构建发布数据
			const publishData = {
				type: this.currentContentType,
				title: this.postTitle,
				description: this.postDescription,
				privacy: this.currentPrivacy,
				location: this.currentLocation,
				topics: this.selectedTopics,
				mentionedFriends: this.mentionedFriends
			};

			// 根据内容类型添加对应数据
			if (this.currentContentType === 'image') {
				publishData.images = this.selectedImages;
			} else if (this.currentContentType === 'video') {
				publishData.video = this.selectedVideo;
			} else if (this.currentContentType === 'text') {
				publishData.content = this.textContent;
			}

			console.log('发布数据:', publishData);

			uni.showLoading({
				title: '发布中...'
			});

			// 模拟发布
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '发布成功',
					icon: 'success'
				});

				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}, 2000);
		}
	},

	// 监听页面显示
	onShow() {
		// 从城市选择页面返回时获取选择的城市
		const pages = getCurrentPages();
		const currentPage = pages[pages.length - 1];
		if (currentPage.options && currentPage.options.selectedCity) {
			this.currentLocation = currentPage.options.selectedCity;
			// 清除参数，避免重复设置
			delete currentPage.options.selectedCity;
		}
	},

	// 监听页面返回
	onUnload() {
		// 页面卸载时的清理工作
	}
}
</script>

<style lang="scss" scoped>
.publish-container {
	background: #f8f9fa;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.status-bar {
	background: #fff;
}

.top-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16rpx 24rpx;
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	
	.header-left {
		display: flex;
		align-items: center;
		gap: 8rpx;
		
		.back-text {
			font-size: 32rpx;
			color: #333;
		}
	}
	
	.header-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}
	
	.header-right {
		.publish-text {
			font-size: 32rpx;
			color: #FFD700;
			font-weight: 600;
		}
	}
}

// 内容类型选择
.content-type-tabs {
	display: flex;
	background: #fff;
	padding: 16rpx 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
	gap: 40rpx;

	.type-tab {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8rpx;
		padding: 12rpx 20rpx;
		border-radius: 12rpx;
		transition: all 0.3s ease;

		&.active {
			background: rgba(255, 215, 0, 0.1);
		}

		.type-text {
			font-size: 24rpx;
			color: #666;
		}

		&.active .type-text {
			color: #FFD700;
			font-weight: 600;
		}
	}
}

.content-area {
	flex: 1;
	padding: 0 24rpx;
}

// 标题输入
.title-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin: 16rpx 0;
	position: relative;

	.title-input {
		width: 100%;
		font-size: 32rpx;
		color: #333;
		line-height: 1.5;
		padding-right: 80rpx;
	}

	.title-count {
		position: absolute;
		right: 24rpx;
		top: 24rpx;
		font-size: 24rpx;
		color: #999;
	}
}

// 描述输入
.desc-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	position: relative;

	.desc-input {
		width: 100%;
		min-height: 120rpx;
		font-size: 28rpx;
		color: #333;
		line-height: 1.6;
		padding-bottom: 40rpx;
	}

	.desc-count {
		position: absolute;
		right: 24rpx;
		bottom: 16rpx;
		font-size: 24rpx;
		color: #999;
	}
}

// 媒体内容区域
.media-section {
	margin-bottom: 24rpx;

	.media-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;

		.media-title {
			font-size: 32rpx;
			color: #333;
			font-weight: 600;
		}

		.media-tip {
			font-size: 24rpx;
			color: #999;
		}
	}
}

// 图片内容
.image-content {
	.image-grid {
		display: flex;
		flex-wrap: wrap;
		gap: 12rpx;

		.image-item {
			position: relative;
			width: 200rpx;
			height: 200rpx;
			border-radius: 12rpx;
			overflow: hidden;

			.preview-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.image-actions {
				position: absolute;
				top: 8rpx;
				right: 8rpx;
				display: flex;
				gap: 8rpx;

				.action-btn {
					display: flex;
					align-items: center;
					gap: 4rpx;
					padding: 6rpx 12rpx;
					background: rgba(0, 0, 0, 0.6);
					border-radius: 16rpx;

					&.delete {
						padding: 8rpx;
						width: 32rpx;
						height: 32rpx;
						justify-content: center;
					}

					.action-text {
						font-size: 20rpx;
						color: #fff;
					}
				}
			}
		}
	}
}

// 视频内容
.video-content {
	.video-preview {
		position: relative;
		border-radius: 16rpx;
		overflow: hidden;

		.preview-video {
			width: 100%;
			height: 400rpx;
		}

		.video-actions {
			position: absolute;
			top: 16rpx;
			right: 16rpx;
			display: flex;
			gap: 12rpx;

			.action-btn {
				display: flex;
				align-items: center;
				gap: 6rpx;
				padding: 8rpx 16rpx;
				background: rgba(0, 0, 0, 0.6);
				border-radius: 20rpx;

				&.delete {
					padding: 8rpx;
					width: 36rpx;
					height: 36rpx;
					justify-content: center;
				}

				.action-text {
					font-size: 24rpx;
					color: #fff;
				}
			}
		}
	}
}

// 文字内容
.text-content {
	.text-content-input {
		width: 100%;
		min-height: 300rpx;
		padding: 24rpx;
		background: #fff;
		border-radius: 16rpx;
		font-size: 32rpx;
		line-height: 1.6;
		color: #333;
		margin-bottom: 16rpx;
		box-sizing: border-box;
	}

	.text-count {
		text-align: right;
		font-size: 24rpx;
		color: #999;
	}
}

// 添加媒体按钮
.add-media-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	width: 200rpx;
	height: 200rpx;
	border: 2rpx dashed #ddd;
	border-radius: 12rpx;
	background: #fafafa;

	&.large {
		width: 100%;
		height: 300rpx;
	}

	.add-text {
		font-size: 28rpx;
		color: #999;
	}
}

// 功能选项
.options-section {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	overflow: hidden;

	.option-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx;
		border-bottom: 1rpx solid #f5f5f5;

		&:last-child {
			border-bottom: none;
		}

		.option-left {
			display: flex;
			align-items: center;
			gap: 16rpx;

			.option-text {
				font-size: 32rpx;
				color: #333;
			}
		}

		.option-right {
			display: flex;
			align-items: center;
			gap: 12rpx;

			.option-value {
				font-size: 28rpx;
				color: #666;
			}
		}
	}
}

// 已选择的话题
.selected-topics {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;

	.topics-title {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 16rpx;
		display: block;
	}

	.topics-list {
		display: flex;
		flex-wrap: wrap;
		gap: 12rpx;

		.topic-tag {
			display: flex;
			align-items: center;
			gap: 8rpx;
			padding: 8rpx 16rpx;
			background: rgba(255, 215, 0, 0.1);
			border: 1rpx solid #FFD700;
			border-radius: 20rpx;

			.topic-text {
				font-size: 26rpx;
				color: #333;
			}
		}
	}
}

// 隐私设置弹窗
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	z-index: 1000;
}

.privacy-modal {
	width: 100%;
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 32rpx 24rpx;
	animation: slideUp 0.3s ease;

	.modal-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 32rpx;

		.modal-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
		}

		.close-btn {
			width: 48rpx;
			height: 48rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 24rpx;
			background: #f5f5f5;
		}
	}

	.privacy-options {
		.privacy-option {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx 0;
			border-bottom: 1rpx solid #f5f5f5;

			&:last-child {
				border-bottom: none;
			}

			&.active {
				.option-name {
					color: #FFD700;
					font-weight: 600;
				}
			}

			.option-info {
				display: flex;
				align-items: center;
				gap: 20rpx;

				.option-content {
					.option-name {
						font-size: 32rpx;
						color: #333;
						margin-bottom: 8rpx;
						display: block;
					}

					.option-desc {
						font-size: 26rpx;
						color: #999;
						line-height: 1.4;
						display: block;
					}
				}
			}
		}
	}
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}
</style>
