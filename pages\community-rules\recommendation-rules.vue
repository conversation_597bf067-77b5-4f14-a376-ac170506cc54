<template>
	<view class="page-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="header-content">
				<view class="back-button" @click="goBack">
					<text class="back-icon">‹</text>
				</view>
				<text class="header-title">推荐规则</text>
				<view class="header-placeholder"></view>
			</view>
		</view>

		<!-- 主要内容 -->
		<view class="main-content">
			<scroll-view scroll-y="true" class="scroll-container">
				<view class="document-content">
					<!-- 文档标题 -->
					<view class="document-header">
						<text class="document-title">闲伴内容推荐规则</text>
						<text class="document-subtitle">优质内容优先，个性化精准推荐</text>
						<view class="document-info">
							<text class="info-item">版本：v1.9</text>
							<text class="info-item">发布日期：2025年6月4日</text>
							<text class="info-item">生效日期：2025年6月4日</text>
						</view>
					</view>

					<!-- 第1章 -->
					<view class="content-section">
						<text class="section-title">1. 推荐原则</text>
						<view class="section-item">
							<text class="item-number">1.1</text>
							<view class="item-content">
								<text class="item-title">内容质量优先</text>
								<text class="item-text">优先推荐原创、有价值、高质量的内容，为用户提供更好的浏览体验。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.2</text>
							<view class="item-content">
								<text class="item-title">个性化推荐</text>
								<text class="item-text">基于用户的兴趣偏好、行为习惯和社交关系，提供个性化的内容推荐。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.3</text>
							<view class="item-content">
								<text class="item-title">多样性平衡</text>
								<text class="item-text">在满足用户兴趣的同时，适当推荐不同类型的内容，避免信息茧房效应。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.4</text>
							<view class="item-content">
								<text class="item-title">时效性考虑</text>
								<text class="item-text">优先推荐新鲜、热门的内容，同时兼顾经典优质内容的长尾推荐。</text>
							</view>
						</view>
					</view>
					
					<!-- 第2章 -->
					<view class="content-section">
						<text class="section-title">2. 推荐因子</text>
						<view class="section-item">
							<text class="item-number">2.1</text>
							<view class="item-content">
								<text class="item-title">内容质量评分</text>
								<text class="item-text">包括原创性、完整性、实用性、趣味性等多个维度的综合评分。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.2</text>
							<view class="item-content">
								<text class="item-title">用户互动数据</text>
								<text class="item-text">点赞、评论、分享、收藏等用户行为数据，反映内容的受欢迎程度。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.3</text>
							<view class="item-content">
								<text class="item-title">发布者信誉</text>
								<text class="item-text">发布者的历史表现、粉丝数量、内容质量等综合信誉评估。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.4</text>
							<view class="item-content">
								<text class="item-title">用户偏好匹配</text>
								<text class="item-text">基于用户的历史行为、兴趣标签、关注关系等进行个性化匹配。</text>
							</view>
						</view>
					</view>
					
					<!-- 第3章 -->
					<view class="content-section">
						<text class="section-title">3. 推荐策略</text>
						<view class="section-item">
							<text class="item-number">3.1</text>
							<view class="item-content">
								<text class="item-title">热门推荐</text>
								<text class="item-text">推荐当前热门、高互动的优质内容，帮助用户发现社区热点。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.2</text>
							<view class="item-content">
								<text class="item-title">兴趣推荐</text>
								<text class="item-text">基于用户的兴趣标签和行为偏好，推荐相关领域的优质内容。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.3</text>
							<view class="item-content">
								<text class="item-title">关注推荐</text>
								<text class="item-text">优先推荐用户关注的创作者发布的新内容，维护社交关系。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.4</text>
							<view class="item-content">
								<text class="item-title">探索推荐</text>
								<text class="item-text">适当推荐用户未接触过的新领域内容，拓展用户的兴趣边界。</text>
							</view>
						</view>
					</view>
					
					<!-- 第4章 -->
					<view class="content-section">
						<text class="section-title">4. 内容审核</text>
						<view class="section-item">
							<text class="item-number">4.1</text>
							<view class="item-content">
								<text class="item-text">所有推荐内容均需通过平台的内容审核机制，确保符合社区规范和法律法规。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.2</text>
							<view class="item-content">
								<text class="item-text">对于违规内容，将立即停止推荐并进行相应处理，维护社区环境的健康有序。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.3</text>
							<view class="item-content">
								<text class="item-text">建立用户举报机制，鼓励用户参与社区内容治理，共同维护良好的社区氛围。</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'RecommendationRules',
	data() {
		return {
			statusBarHeight: 0
		};
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 0;
	},
	methods: {
		goBack() {
			// 尝试返回上一页，如果没有上一页则跳转到社区公约页面
			const pages = getCurrentPages();
			if (pages.length > 1) {
				uni.navigateBack({
					delta: 1
				});
			} else {
				uni.redirectTo({
					url: '/pages/community-rules/community-rules'
				});
			}
		}
	}
};
</script>

<style lang="scss">
.page-container {
	min-height: 100vh;
	background: #f8f9fa;
	display: flex;
	flex-direction: column;
}

.status-bar {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
}

.header {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
	padding: 0 32rpx 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(255, 215, 0, 0.2);

	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;

		.back-button {
			width: 64rpx;
			height: 64rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50%;
			backdrop-filter: blur(10rpx);

			.back-icon {
				font-size: 36rpx;
				color: #333;
				font-weight: bold;
				line-height: 1;
			}
		}

		.header-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			flex: 1;
			text-align: center;
		}

		.header-placeholder {
			width: 64rpx;
		}
	}
}

.main-content {
	flex: 1;
	overflow: hidden;

	.scroll-container {
		height: 100%;
	}
}

.document-content {
	padding: 32rpx;

	.document-header {
		text-align: center;
		margin-bottom: 48rpx;
		padding: 48rpx 32rpx;
		background: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

		.document-title {
			font-size: 48rpx;
			font-weight: 700;
			color: #333;
			display: block;
			margin-bottom: 16rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}

		.document-subtitle {
			font-size: 28rpx;
			color: #999;
			display: block;
			margin-bottom: 32rpx;
			font-style: italic;
		}

		.document-info {
			display: flex;
			justify-content: center;
			flex-wrap: wrap;
			gap: 24rpx;

			.info-item {
				font-size: 24rpx;
				color: #666;
				padding: 8rpx 16rpx;
				background: #f8f9fa;
				border-radius: 8rpx;
			}
		}
	}

	.content-section {
		margin-bottom: 48rpx;
		background: #fff;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

		.section-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			display: block;
			margin-bottom: 32rpx;
			padding: 24rpx 32rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
			border-radius: 12rpx;
			text-align: center;
			box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.2);
		}

		.section-item {
			display: flex;
			margin-bottom: 24rpx;
			align-items: flex-start;

			&:last-child {
				margin-bottom: 0;
			}

			.item-number {
				font-size: 28rpx;
				color: #FFD700;
				font-weight: 600;
				margin-right: 20rpx;
				flex-shrink: 0;
				margin-top: 4rpx;
				min-width: 60rpx;
			}

			.item-content {
				flex: 1;
				line-height: 1.6;

				.item-title {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
					display: block;
					margin-bottom: 12rpx;
				}

				.item-text {
					font-size: 28rpx;
					color: #666;
					line-height: 1.8;
					text-align: justify;
				}
			}
		}
	}
}

/* 全局强制样式 */
.page-container {
	min-height: 100vh !important;
	background: #f8f9fa !important;
	display: flex !important;
	flex-direction: column !important;
}

.status-bar {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%) !important;
}

.header {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%) !important;
	padding: 0 32rpx 24rpx !important;
	box-shadow: 0 2rpx 12rpx rgba(255, 215, 0, 0.2) !important;
}

.header .header-content {
	display: flex !important;
	align-items: center !important;
	justify-content: space-between !important;
	height: 88rpx !important;
}

.header .back-button {
	width: 64rpx !important;
	height: 64rpx !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	background: rgba(255, 255, 255, 0.2) !important;
	border-radius: 50% !important;
	backdrop-filter: blur(10rpx) !important;
}

.header .back-icon {
	font-size: 36rpx !important;
	color: #333 !important;
	font-weight: bold !important;
	line-height: 1 !important;
}

.header .header-title {
	font-size: 36rpx !important;
	font-weight: 600 !important;
	color: #333 !important;
	flex: 1 !important;
	text-align: center !important;
}

.header .header-placeholder {
	width: 64rpx !important;
}

.main-content {
	flex: 1 !important;
	overflow: hidden !important;
}

.scroll-container {
	height: 100% !important;
}

.document-content {
	padding: 32rpx !important;
}

.document-header {
	text-align: center !important;
	margin-bottom: 48rpx !important;
	padding: 48rpx 32rpx !important;
	background: #fff !important;
	border-radius: 16rpx !important;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08) !important;
}

.document-title {
	font-size: 48rpx !important;
	font-weight: 700 !important;
	color: #333 !important;
	display: block !important;
	margin-bottom: 16rpx !important;
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%) !important;
	-webkit-background-clip: text !important;
	-webkit-text-fill-color: transparent !important;
}

.document-subtitle {
	font-size: 28rpx !important;
	color: #999 !important;
	display: block !important;
	margin-bottom: 32rpx !important;
	font-style: italic !important;
}

.document-info {
	display: flex !important;
	justify-content: center !important;
	flex-wrap: wrap !important;
	gap: 24rpx !important;
}

.info-item {
	font-size: 24rpx !important;
	color: #666 !important;
	padding: 8rpx 16rpx !important;
	background: #f8f9fa !important;
	border-radius: 8rpx !important;
}

.content-section {
	margin-bottom: 48rpx !important;
	background: #fff !important;
	border-radius: 16rpx !important;
	padding: 32rpx !important;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05) !important;
}

.section-title {
	font-size: 36rpx !important;
	font-weight: 600 !important;
	color: #333 !important;
	display: block !important;
	margin-bottom: 32rpx !important;
	padding: 24rpx 32rpx !important;
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%) !important;
	border-radius: 12rpx !important;
	text-align: center !important;
	box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.2) !important;
}

.section-item {
	display: flex !important;
	margin-bottom: 24rpx !important;
	align-items: flex-start !important;
}

.section-item:last-child {
	margin-bottom: 0 !important;
}

.item-number {
	font-size: 28rpx !important;
	color: #FFD700 !important;
	font-weight: 600 !important;
	margin-right: 20rpx !important;
	flex-shrink: 0 !important;
	margin-top: 4rpx !important;
	min-width: 60rpx !important;
}

.item-content {
	flex: 1 !important;
	line-height: 1.6 !important;
}

.item-title {
	font-size: 30rpx !important;
	font-weight: 600 !important;
	color: #333 !important;
	display: block !important;
	margin-bottom: 12rpx !important;
}

.item-text {
	font-size: 28rpx !important;
	color: #666 !important;
	line-height: 1.8 !important;
	text-align: justify !important;
}
</style>
