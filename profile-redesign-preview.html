<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲伴 - 我的页面重构预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #F0F0F0;
            min-height: 100vh;
            padding: 20px;
        }
        
        .phone-frame {
            max-width: 375px;
            margin: 0 auto;
            background: #000;
            border-radius: 30px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        .screen {
            background: #F8F9FA;
            border-radius: 22px;
            overflow: hidden;
            min-height: 600px;
        }

        .status-bar {
            height: 44px;
            background: transparent;
        }

        .profile-header {
            background-image: url('/static/images/backgrounds/profile-bg.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
            padding-top: 40px;
            padding-bottom: 60px;
        }

        .profile-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            z-index: 1;
        }

        .ip-location {
            position: absolute;
            top: 20px;
            left: 15px;
            z-index: 10;
        }

        .ip-text {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
            background: rgba(0, 0, 0, 0.3);
            padding: 4px 8px;
            border-radius: 10px;
        }

        .more-btn-header {
            position: absolute;
            top: 20px;
            right: 15px;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            z-index: 3;
            color: #fff;
        }

        .profile-content {
            display: flex;
            align-items: flex-start;
            padding: 40px 15px 0 15px;
            position: relative;
            z-index: 2;
        }

        .avatar-section {
            margin-right: 15px;
        }

        .user-avatar-large {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .user-info-section {
            flex: 1;
            padding-top: 10px;
        }

        .user-name-large {
            font-size: 22px;
            font-weight: 700;
            color: #fff;
            margin-bottom: 12px;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
            display: block;
            width: 100%;
        }

        .user-id-container {
            display: flex;
            align-items: center;
            margin-bottom: 14px;
            gap: 4px;
        }

        .user-id-text {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }

        .copy-id-btn {
            padding: 3px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            min-width: 20px;
            min-height: 20px;
        }

        .user-bio-row {
            font-size: 14px;
            color: #333;
            line-height: 1.6;
            padding: 12px 20px;
            word-break: break-all;
            overflow-wrap: break-word;
        }

        .user-badges {
            display: flex;
            gap: 8px;
            flex-wrap: nowrap;
            align-items: center;
        }

        .badge-item {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 5px 8px;
            background: rgba(255, 255, 255, 0.25);
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 0.5px solid rgba(255, 255, 255, 0.3);
            font-size: 11px;
            flex-shrink: 0;
            cursor: pointer;
            transition: all 0.2s;
        }

        .badge-item:hover {
            background: rgba(255, 255, 255, 0.35);
            transform: scale(1.05);
        }

        .badge-text {
            color: rgba(255, 255, 255, 0.95);
            font-weight: 500;
            white-space: nowrap;
        }


        
        .stats-section-fullwidth {
            background: rgba(255, 255, 255, 0.95);
            margin-top: -30px;
            padding: 20px 0 10px;
            border-radius: 16px 16px 0 0;
            position: relative;
            z-index: 2;
            box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
        }

        .stats-row {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 20px 16px;
            border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
        }
        
        .stat-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 10px 5px;
            cursor: pointer;
            transition: background-color 0.2s;
            border-radius: 8px;
        }

        .stat-item:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: 700;
            color: #333;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            font-weight: 400;
        }

        .diamond-section {
            background: #fff;
            padding: 15px;
            border-bottom: 10px solid #F8F9FA;
        }

        .diamond-row {
            display: flex;
            justify-content: space-around;
        }

        .diamond-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
        }

        .diamond-icon {
            width: 40px;
            height: 40px;
            background: #F8F9FA;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .diamond-text {
            font-size: 12px;
            color: #666;
            font-weight: 400;
        }


        
        .tab-section {
            background: #fff;
            border-bottom: 0.5px solid #F0F0F0;
        }

        .tab-container {
            display: flex;
            padding: 0 15px;
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 15px 0;
            position: relative;
        }

        .tab-item.active .tab-text {
            color: #333;
            font-weight: 600;
        }

        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: #333;
            border-radius: 1px;
        }

        .tab-text {
            font-size: 14px;
            color: #999;
            font-weight: 400;
        }

        .content-section {
            background: #fff;
            min-height: 200px;
        }

        .content-container {
            padding: 30px 15px;
        }

        .empty-state {
            text-align: center;
            padding: 40px 0;
        }

        .empty-illustration {
            width: 100px;
            height: 100px;
            margin: 0 auto 15px auto;
            display: block;
        }

        .empty-text {
            font-size: 14px;
            color: #333;
            margin-bottom: 6px;
            font-weight: 500;
            text-align: center;
        }

        .empty-desc {
            font-size: 12px;
            color: #999;
            text-align: center;
        }


    </style>
    <script>
        function copyUserId() {
            const userId = '8264751';
            navigator.clipboard.writeText(userId).then(function() {
                alert(`已复制ID：${userId}`);
            }, function() {
                alert('复制失败，请重试');
            });
        }

        function goToVerification() {
            alert('跳转到实名认证页面');
        }

        function goToVipCenter() {
            alert('跳转到会员中心页面');
        }

        function goToLevel() {
            alert('跳转到等级中心页面');
        }

        function goToFollowing() {
            alert('跳转到关注列表页面');
        }

        function goToFollowers() {
            alert('跳转到粉丝列表页面');
        }

        function goToLikes() {
            alert('跳转到获赞列表页面');
        }

        function goToPosts() {
            alert('跳转到作品列表页面');
        }
    </script>
</head>
<body>
    <div class="phone-frame">
        <div class="screen">
            <!-- 状态栏占位 -->
            <div class="status-bar"></div>

            <!-- 个人信息背景区域 -->
            <div class="profile-header">
                <!-- IP属地显示 -->
                <div class="ip-location">
                    <span class="ip-text">🌐 IP属地：上海</span>
                </div>

                <!-- 更多功能按钮 -->
                <div class="more-btn-header">⋯</div>

                <!-- 个人信息内容 -->
                <div class="profile-content">
                    <div class="avatar-section">
                        <img class="user-avatar-large" src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="头像">
                    </div>
                    <div class="user-info-section">
                        <div class="user-name-large">闲伴用户</div>
                        <div class="user-id-container">
                            <span class="user-id-text">乐鱼ID：8264751</span>
                            <div class="copy-id-btn" onclick="copyUserId()">
                                <span style="color: #FFFFFF; font-size: 10px;">📎</span>
                            </div>
                        </div>
                        <div class="user-badges">
                            <div class="badge-item" onclick="goToVerification()">
                                <span style="color: #4CAF50;">👤</span>
                                <span class="badge-text">实名认证</span>
                            </div>
                            <div class="badge-item" onclick="goToVipCenter()">
                                <span style="color: #FFD700; font-size: 11px;">♦</span>
                                <span class="badge-text">会员</span>
                            </div>
                            <div class="badge-item" onclick="goToLevel()">
                                <span style="color: #FF9800;">🏆</span>
                                <span class="badge-text">Lv.12</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据统计区域 - 全屏显示 -->
            <div class="stats-section-fullwidth">
                <!-- 数据统计行 -->
                <div class="stats-row">
                    <div class="stat-item" onclick="goToFollowing()">
                        <div class="stat-number">128</div>
                        <div class="stat-label">关注</div>
                    </div>
                    <div class="stat-item" onclick="goToFollowers()">
                        <div class="stat-number">256</div>
                        <div class="stat-label">粉丝</div>
                    </div>
                    <div class="stat-item" onclick="goToLikes()">
                        <div class="stat-number">1024</div>
                        <div class="stat-label">获赞</div>
                    </div>
                    <div class="stat-item" onclick="goToPosts()">
                        <div class="stat-number">32</div>
                        <div class="stat-label">作品</div>
                    </div>
                </div>

                <!-- 个人简介行 -->
                <div class="user-bio-row">热爱生活，享受每一个美好时刻 ✨</div>
            </div>

            <!-- 金刚功能区 -->
            <div class="diamond-section">
                <div class="diamond-row">
                    <div class="diamond-item">
                        <div class="diamond-icon" style="color: #4CAF50;">💰</div>
                        <div class="diamond-text">钱包</div>
                    </div>
                    <div class="diamond-item">
                        <div class="diamond-icon" style="color: #2196F3;">📋</div>
                        <div class="diamond-text">订单</div>
                    </div>
                    <div class="diamond-item">
                        <div class="diamond-icon" style="color: #FF9800;">🎫</div>
                        <div class="diamond-text">优惠券</div>
                    </div>
                    <div class="diamond-item">
                        <div class="diamond-icon" style="color: #9C27B0;">🏪</div>
                        <div class="diamond-text">商城</div>
                    </div>
                </div>
            </div>



            <!-- 切换菜单功能区 -->
            <div class="tab-section">
                <div class="tab-container">
                    <div class="tab-item active">
                        <div class="tab-text">作品</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-text">喜欢</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-text">收藏</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-text">活动</div>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-section">
                <div class="content-container">
                    <div class="empty-state">
                        <img class="empty-illustration" src="/static/illustrations/empty-works.png" alt="空状态插画">
                        <div class="empty-text">还没有发布作品</div>
                        <div class="empty-desc">分享你的精彩瞬间吧</div>
                    </div>
                </div>
            </div>


        </div>
    </div>
</body>
</html>
