<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>呼吸感布局调整示意</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .title {
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 40px;
            color: #FFD700;
        }
        
        .comparison {
            display: flex;
            gap: 40px;
            justify-content: center;
            align-items: flex-start;
        }
        
        .phone-mockup {
            width: 300px;
            height: 600px;
            background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 50%, #3498DB 100%);
            border-radius: 30px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        .mockup-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #FFD700;
        }
        
        .status-bar {
            height: 30px;
            background: transparent;
        }
        
        .nav-bar {
            height: 50px;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }
        
        .back-button {
            width: 35px;
            height: 35px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            color: white;
            font-size: 18px;
        }
        
        .hello-text {
            position: absolute;
            top: 120px;
            right: 20px;
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        
        .character {
            position: absolute;
            top: 140px;
            right: 30px;
            font-size: 40px;
        }
        
        .spacing-indicator {
            position: absolute;
            left: 10px;
            width: 20px;
            border-left: 2px dashed #FFD700;
            color: #FFD700;
            font-size: 12px;
            font-weight: bold;
        }
        
        .spacing-1 {
            top: 150px;
            height: 40px;
        }
        
        .spacing-1::after {
            content: "呼吸空间";
            position: absolute;
            left: 25px;
            top: 15px;
            background: rgba(255, 215, 0, 0.2);
            padding: 2px 8px;
            border-radius: 10px;
            white-space: nowrap;
        }
        
        .spacing-2 {
            top: 240px;
            height: 50px;
        }
        
        .spacing-2::after {
            content: "舒适间距";
            position: absolute;
            left: 25px;
            top: 20px;
            background: rgba(255, 215, 0, 0.2);
            padding: 2px 8px;
            border-radius: 10px;
            white-space: nowrap;
        }
        
        .welcome-section-tight {
            position: absolute;
            top: 150px;
            left: 20px;
            right: 100px;
            text-align: left;
        }
        
        .welcome-section-balanced {
            position: absolute;
            top: 190px;
            left: 20px;
            right: 100px;
            text-align: left;
        }
        
        .welcome-title {
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 8px;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        .welcome-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .main-section {
            position: absolute;
            top: 290px;
            left: 20px;
            right: 20px;
        }
        
        .password-input {
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 0 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            color: white;
            font-size: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .analysis {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .analysis-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #FFD700;
            text-align: center;
        }
        
        .spacing-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .spacing-table th,
        .spacing-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .spacing-table th {
            background: rgba(255, 215, 0, 0.2);
            color: #FFD700;
            font-weight: bold;
        }
        
        .spacing-table td {
            color: rgba(255, 255, 255, 0.9);
        }
        
        .highlight {
            background: rgba(255, 215, 0, 0.1);
            color: #FFD700;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🌬️ 呼吸感布局调整示意</h1>
        
        <div class="comparison">
            <div>
                <div class="mockup-title">❌ 过于紧凑</div>
                <div class="phone-mockup">
                    <div class="status-bar"></div>
                    <div class="nav-bar">
                        <div class="back-button">←</div>
                    </div>
                    <div class="hello-text">HELLO!</div>
                    <div class="character">🐻</div>
                    
                    <div class="welcome-section-tight">
                        <div class="welcome-title">欢迎回来！</div>
                        <div class="welcome-subtitle">166****1214 已注册</div>
                    </div>
                    
                    <div class="main-section">
                        <div class="password-input">请输入登录密码</div>
                    </div>
                </div>
            </div>
            
            <div>
                <div class="mockup-title">✅ 呼吸感布局</div>
                <div class="phone-mockup">
                    <div class="status-bar"></div>
                    <div class="nav-bar">
                        <div class="back-button">←</div>
                    </div>
                    <div class="hello-text">HELLO!</div>
                    <div class="character">🐻</div>
                    
                    <!-- 间距指示器 -->
                    <div class="spacing-indicator spacing-1"></div>
                    <div class="spacing-indicator spacing-2"></div>
                    
                    <div class="welcome-section-balanced">
                        <div class="welcome-title">欢迎回来！</div>
                        <div class="welcome-subtitle">166****1214 已注册</div>
                    </div>
                    
                    <div class="main-section">
                        <div class="password-input">请输入登录密码</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="analysis">
            <div class="analysis-title">📏 间距分析</div>
            
            <table class="spacing-table">
                <thead>
                    <tr>
                        <th>元素</th>
                        <th>过于紧凑版本</th>
                        <th>呼吸感版本</th>
                        <th>改进效果</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Hello! 到欢迎文字</td>
                        <td>30rpx</td>
                        <td class="highlight">70rpx</td>
                        <td>增加呼吸空间，避免拥挤</td>
                    </tr>
                    <tr>
                        <td>欢迎文字到密码框</td>
                        <td>140rpx</td>
                        <td class="highlight">120rpx</td>
                        <td>保持舒适间距，视觉平衡</td>
                    </tr>
                    <tr>
                        <td>欢迎文字位置</td>
                        <td>top: 220rpx</td>
                        <td class="highlight">top: 260rpx</td>
                        <td>在Hello!和深蓝色区域间均匀分布</td>
                    </tr>
                    <tr>
                        <td>整体视觉效果</td>
                        <td>紧凑拥挤</td>
                        <td class="highlight">舒适自然</td>
                        <td>符合现代UI设计原则</td>
                    </tr>
                </tbody>
            </table>
            
            <div style="margin-top: 30px; padding: 20px; background: rgba(255, 215, 0, 0.1); border-radius: 15px; border-left: 4px solid #FFD700;">
                <h4 style="color: #FFD700; margin-top: 0;">🎯 设计原则</h4>
                <p style="margin-bottom: 0; line-height: 1.6;">
                    • <strong>呼吸感</strong>：元素间保持适当空白，避免视觉拥挤<br>
                    • <strong>平衡感</strong>：在Hello!和深蓝色区域间均匀分布<br>
                    • <strong>层次感</strong>：通过间距营造清晰的信息层级<br>
                    • <strong>舒适感</strong>：符合用户视觉习惯和阅读节奏
                </p>
            </div>
        </div>
    </div>
</body>
</html>
