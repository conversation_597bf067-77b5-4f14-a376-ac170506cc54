<template>
	<view class="vip-page">
		<!-- 顶部导航栏 -->
		<view class="top-header">
			<!-- 状态栏 -->
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

			<!-- 导航内容 -->
			<view class="nav-content">
				<view class="nav-left" @click="goBack">
					<view class="back-btn">
						<uni-icons type="left" size="20" color="#FFD700"></uni-icons>
					</view>
				</view>
				<view class="nav-center">
					<text class="nav-title">会员中心</text>
				</view>
				<view class="nav-right">
					<view class="help-btn" @click="showHelp">
						<uni-icons type="help" size="20" color="#FFD700"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 会员卡片展示区域 -->
		<view class="member-card-section">
			<!-- 当前会员等级指示器 -->
			<view class="level-indicator">
				<view class="indicator-line">
					<view class="indicator-dot" :style="{ left: getIndicatorPosition() }"></view>
					<view class="indicator-track"></view>
				</view>
				<text class="current-level-text">{{ getCurrentLevelName() }}</text>
			</view>

			<!-- 会员卡片滑动区域 -->
			<view class="member-cards-container">
				<swiper
					class="member-cards-swiper"
					:current="selectedLevel - 1"
					@change="onCardChange"
					:indicator-dots="false"
					:circular="false"
					:display-multiple-items="1.2"
					:previous-margin="40"
					:next-margin="40"
				>
					<swiper-item v-for="level in vipLevels" :key="level.id" class="card-item">
						<view class="member-card" :class="[level.theme, { active: selectedLevel === level.id, current: userInfo.vipLevel === level.id }]">
							<!-- 卡片背景装饰 -->
							<view class="card-decorations" :class="level.theme">
								<view class="decoration-pattern pattern-1"></view>
								<view class="decoration-pattern pattern-2"></view>
								<view class="decoration-pattern pattern-3"></view>
								<view class="level-watermark">{{ level.id }}</view>
							</view>

							<!-- 卡片内容 -->
							<view class="card-content">
								<!-- 卡片头部 -->
								<view class="card-header">
									<view class="level-badge" :class="level.theme">
										<uni-icons :type="level.icon" size="20" color="#fff"></uni-icons>
									</view>
									<view class="card-status" v-if="userInfo.vipLevel === level.id">
										<text class="status-label">当前</text>
									</view>
									<view class="card-status locked" v-else-if="userInfo.vipLevel < level.id">
										<text class="status-label">未解锁</text>
									</view>
								</view>

								<!-- 会员信息 -->
								<view class="member-info">
									<text class="level-name">{{ level.name }}</text>
									<text class="level-desc">{{ level.description }}</text>
									<view class="level-price">
										<text class="price-text">¥{{ level.price }}</text>
										<text class="price-unit">/月</text>
									</view>
								</view>

								<!-- 用户头像（仅当前等级显示） -->
								<view class="user-avatar-section" v-if="userInfo.vipLevel === level.id">
									<image :src="userInfo.avatar" class="user-avatar"></image>
									<view class="user-info">
										<text class="user-name">{{ userInfo.name }}</text>
										<text class="expire-date">有效期至 {{ userInfo.vipExpireDate }}</text>
									</view>
								</view>

								<!-- 等级特权预览 - 所有卡片都显示权益 -->
								<view class="privileges-preview">
									<view class="privilege-item" v-for="(privilege, index) in getPreviewPrivileges(level.id)" :key="index">
										<uni-icons :type="privilege.icon" size="14" :color="getPrivilegeColor(level.theme)"></uni-icons>
										<text class="privilege-text">{{ privilege.name }}</text>
									</view>
								</view>
							</view>
						</view>
					</swiper-item>
				</swiper>
			</view>
		</view>

		<!-- 功能管理区域 -->
		<view class="function-management">
			<view class="management-grid">
				<view class="management-item" @click="goToRightsCenter">
					<view class="item-icon rights">
						<uni-icons type="star-filled" size="24" color="#FFD700"></uni-icons>
					</view>
					<text class="item-title">权益中心</text>
					<text class="item-desc">查看专属权益</text>
				</view>
				<view class="management-item" @click="goToRenewalRules">
					<view class="item-icon renewal">
						<uni-icons type="refresh" size="24" color="#FFD700"></uni-icons>
					</view>
					<text class="item-title">续费规则</text>
					<text class="item-desc">了解续费政策</text>
				</view>
				<view class="management-item" @click="goToBillingHistory">
					<view class="item-icon billing">
						<uni-icons type="list" size="24" color="#FFD700"></uni-icons>
					</view>
					<text class="item-title">消费记录</text>
					<text class="item-desc">查看账单明细</text>
				</view>
				<view class="management-item" @click="goToMemberGuide">
					<view class="item-icon guide">
						<uni-icons type="help" size="24" color="#FFD700"></uni-icons>
					</view>
					<text class="item-title">会员说明</text>
					<text class="item-desc">使用指南帮助</text>
				</view>
			</view>
		</view>

		<!-- VIP等级权益展示 -->
		<view class="vip-levels-section">
			<view class="levels-header">
				<text class="levels-title">VIP等级权益</text>
				<text class="levels-subtitle">滑动查看不同等级专享特权</text>
			</view>

			<!-- VIP等级选择器 -->
			<view class="level-selector">
				<scroll-view class="level-tabs" scroll-x="true" :scroll-into-view="'level-' + selectedLevel">
					<view
						class="level-tab"
						v-for="level in vipLevels"
						:key="level.id"
						:id="'level-' + level.id"
						:class="{
							active: selectedLevel === level.id,
							unlocked: userInfo.vipLevel >= level.id,
							current: userInfo.vipLevel === level.id
						}"
						@click="selectLevel(level.id)"
					>
						<view class="level-icon" :class="level.theme">
							<uni-icons :type="level.icon" size="24" color="#fff"></uni-icons>
						</view>
						<text class="level-name">{{ level.name }}</text>
						<text class="level-price">¥{{ level.price }}/月</text>
						<view class="level-status" v-if="userInfo.vipLevel >= level.id">
							<text class="status-text">已解锁</text>
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 当前选中等级的权益展示 -->
			<view class="level-privileges">
				<view class="privileges-header">
					<view class="header-info">
						<text class="current-level">{{ getCurrentLevelInfo().name }}</text>
						<text class="level-desc">{{ getCurrentLevelInfo().description }}</text>
					</view>
					<view class="unlock-status" :class="{ unlocked: userInfo.vipLevel >= selectedLevel }">
						<text class="status-text" v-if="userInfo.vipLevel >= selectedLevel">您已获得以下权益</text>
						<text class="status-text" v-else>您暂未解锁该等级</text>
					</view>
				</view>

				<view class="privileges-grid">
					<view
						class="privilege-card"
						v-for="privilege in getCurrentLevelPrivileges()"
						:key="privilege.id"
						:class="{
							unlocked: userInfo.vipLevel >= selectedLevel,
							locked: userInfo.vipLevel < selectedLevel
						}"
					>
						<view class="card-header">
							<view class="privilege-icon" :class="privilege.theme">
								<uni-icons :type="privilege.icon" size="20" color="#fff"></uni-icons>
							</view>
							<view class="unlock-badge" v-if="userInfo.vipLevel >= selectedLevel">
								<uni-icons type="checkmarkempty" size="14" color="#4CAF50"></uni-icons>
							</view>
							<view class="lock-badge" v-else>
								<uni-icons type="locked" size="14" color="#999"></uni-icons>
							</view>
						</view>
						<view class="privilege-content">
							<text class="privilege-name">{{ privilege.name }}</text>
							<text class="privilege-desc">{{ privilege.description }}</text>
						</view>
					</view>
				</view>

				<!-- 升级提示 -->
				<view class="upgrade-hint" v-if="userInfo.vipLevel < selectedLevel">
					<view class="hint-content">
						<text class="hint-title">升级至{{ getCurrentLevelInfo().name }}</text>
						<text class="hint-desc">解锁更多专属权益，享受尊贵体验</text>
						<view class="upgrade-action" @click="upgradeToLevel(selectedLevel)">
							<text class="action-text">立即升级</text>
							<uni-icons type="right" size="16" color="#000"></uni-icons>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 会员套餐选择 -->
		<view class="packages-section" id="packages">
			<view class="packages-header">
				<text class="packages-title">选择会员套餐</text>
				<text class="packages-subtitle">多种套餐，满足不同需求</text>
			</view>

			<!-- 套餐选择器 -->
			<view class="package-selector">
				<view
					class="package-option"
					v-for="pkg in vipPackages"
					:key="pkg.id"
					:class="{
						selected: selectedPackage === pkg.id,
						recommended: pkg.recommended,
						popular: pkg.popular
					}"
					@click="selectPackage(pkg.id)"
				>
					<!-- 推荐标签 -->
					<view class="recommendation-tag" v-if="pkg.recommended">
						<text class="tag-text">最划算</text>
					</view>
					<view class="popular-tag" v-if="pkg.popular">
						<text class="tag-text">最受欢迎</text>
					</view>

					<!-- 套餐内容 -->
					<view class="package-content">
						<view class="package-header">
							<text class="package-name">{{ pkg.name }}</text>
							<text class="package-duration">{{ pkg.duration }}</text>
						</view>

						<view class="package-pricing">
							<view class="current-price">
								<text class="price-symbol">¥</text>
								<text class="price-amount">{{ pkg.price }}</text>
							</view>
							<view class="price-details" v-if="pkg.originalPrice">
								<text class="original-price">原价¥{{ pkg.originalPrice }}</text>
								<view class="discount-badge">
									<text class="discount-text">省{{ Math.round((1 - pkg.price / pkg.originalPrice) * 100) }}%</text>
								</view>
							</view>
							<text class="price-per-month">约¥{{ (pkg.price / pkg.months).toFixed(1) }}/月</text>
						</view>

						<view class="package-highlights">
							<view class="highlight-item" v-for="highlight in pkg.highlights" :key="highlight">
								<uni-icons type="checkmarkempty" size="14" color="#FFD700"></uni-icons>
								<text class="highlight-text">{{ highlight }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 购买操作区域 -->
		<view class="purchase-section" v-if="!userInfo.isVip">
			<!-- 选中套餐信息 -->
			<view class="selected-package-info">
				<view class="package-header">
					<text class="section-title">已选套餐</text>
				</view>
				<view class="package-details">
					<view class="package-main">
						<text class="package-name">{{ selectedPackageInfo.name }}</text>
						<text class="package-duration">{{ selectedPackageInfo.duration }}</text>
					</view>
					<view class="package-pricing">
						<text class="current-price">¥{{ selectedPackageInfo.price }}</text>
						<text class="original-price" v-if="selectedPackageInfo.originalPrice">¥{{ selectedPackageInfo.originalPrice }}</text>
					</view>
				</view>
			</view>

			<!-- 支付方式选择 -->
			<view class="payment-section">
				<view class="payment-header">
					<text class="section-title">支付方式</text>
				</view>
				<view class="payment-options">
					<view class="payment-option" :class="{ selected: selectedPayment === 'wechat' }" @click="selectPayment('wechat')">
						<view class="payment-left">
							<view class="payment-icon wechat">微</view>
							<text class="payment-name">微信支付</text>
						</view>
						<view class="payment-radio" :class="{ checked: selectedPayment === 'wechat' }">
							<view class="radio-dot" v-if="selectedPayment === 'wechat'"></view>
						</view>
					</view>
					<view class="payment-option" :class="{ selected: selectedPayment === 'alipay' }" @click="selectPayment('alipay')">
						<view class="payment-left">
							<view class="payment-icon alipay">支</view>
							<text class="payment-name">支付宝</text>
						</view>
						<view class="payment-radio" :class="{ checked: selectedPayment === 'alipay' }">
							<view class="radio-dot" v-if="selectedPayment === 'alipay'"></view>
						</view>
					</view>
				</view>
			</view>

			<!-- 协议同意 -->
			<view class="agreement-section">
				<view class="agreement-checkbox" @tap="toggleAgreement">
					<view class="checkbox" :class="{ checked: agreementChecked }">
						<uni-icons v-if="agreementChecked" type="checkmarkempty" size="16" color="#fff"></uni-icons>
					</view>
					<text class="agreement-text">我已阅读并同意</text>
					<text class="agreement-link" @tap.stop="viewTerms">《会员服务协议》</text>
					<text class="agreement-text">和</text>
					<text class="agreement-link" @tap.stop="viewPrivacy">《隐私政策》</text>
				</view>
			</view>

			<!-- 立即支付按钮 -->
			<view class="pay-button-section">
				<view class="pay-btn" :class="{ disabled: !agreementChecked }" @click="purchaseVip">
					<text class="pay-text">立即支付 ¥{{ selectedPackageInfo.price }}</text>
				</view>
			</view>
		</view>

		<!-- 会员管理区域 -->
		<view class="member-management" v-if="userInfo.isVip">
			<view class="management-header">
				<text class="management-title">会员管理</text>
			</view>
			<view class="management-actions">
				<view class="action-card" @click="renewVip">
					<view class="action-icon renew">
						<uni-icons type="refresh" size="24" color="#FFD700"></uni-icons>
					</view>
					<view class="action-info">
						<text class="action-title">续费会员</text>
						<text class="action-desc">延长会员有效期</text>
					</view>
					<view class="action-arrow">
						<uni-icons type="right" size="16" color="#666"></uni-icons>
					</view>
				</view>
				<view class="action-card" @click="upgradeVip">
					<view class="action-icon upgrade">
						<uni-icons type="arrow-up" size="24" color="#FFD700"></uni-icons>
					</view>
					<view class="action-info">
						<text class="action-title">升级会员</text>
						<text class="action-desc">解锁更高级权益</text>
					</view>
					<view class="action-arrow">
						<uni-icons type="right" size="16" color="#666"></uni-icons>
					</view>
				</view>
			</view>
		</view>


	</view>

	<!-- 会员规则说明弹窗 -->
	<view class="rules-modal" v-if="showRulesModal" @tap="closeRulesModal">
		<view class="modal-content" @tap.stop="">
			<!-- 弹窗顶部装饰 -->
			<view class="modal-header">
				<view class="decoration-circles">
					<view class="circle circle-1"></view>
					<view class="circle circle-2"></view>
					<view class="circle circle-3"></view>
					<view class="circle circle-4"></view>
					<view class="circle circle-5"></view>
				</view>
				<text class="modal-title">会员规则说明</text>
				<view class="close-btn" @tap="closeRulesModal">
					<uni-icons type="close" size="24" color="#666"></uni-icons>
				</view>
			</view>

			<!-- 弹窗内容 -->
			<scroll-view class="modal-body" scroll-y="true">
				<!-- 会员等级说明 -->
				<view class="rule-section">
					<text class="section-title">会员等级</text>
					<view class="level-list">
						<view class="level-item" v-for="level in vipLevels" :key="level.id">
							<view class="level-icon" :class="level.theme">
								<uni-icons :type="level.icon" size="20" color="#fff"></uni-icons>
							</view>
							<view class="level-info">
								<text class="level-name">{{ level.name }}</text>
								<text class="level-price">¥{{ level.price }}/月</text>
							</view>
							<text class="level-desc">{{ level.description }}</text>
						</view>
					</view>
				</view>

				<!-- 会员权益说明 -->
				<view class="rule-section">
					<text class="section-title">会员权益</text>
					<view class="privilege-categories">
						<view class="category-item">
							<text class="category-title">基础权益（黄金会员及以上）</text>
							<view class="privilege-list">
								<text class="privilege-item" v-for="privilege in levelPrivileges[1]" :key="privilege.id">
									• {{ privilege.name }}：{{ privilege.description }}
								</text>
							</view>
						</view>
						<view class="category-item">
							<text class="category-title">进阶权益（钻石会员及以上）</text>
							<view class="privilege-list">
								<text class="privilege-item" v-for="privilege in levelPrivileges[2]" :key="privilege.id">
									• {{ privilege.name }}：{{ privilege.description }}
								</text>
							</view>
						</view>
						<view class="category-item">
							<text class="category-title">高级权益（白金会员及以上）</text>
							<view class="privilege-list">
								<text class="privilege-item" v-for="privilege in levelPrivileges[3]" :key="privilege.id">
									• {{ privilege.name }}：{{ privilege.description }}
								</text>
							</view>
						</view>
						<view class="category-item">
							<text class="category-title">至尊权益（黑金会员专享）</text>
							<view class="privilege-list">
								<text class="privilege-item" v-for="privilege in levelPrivileges[4]" :key="privilege.id">
									• {{ privilege.name }}：{{ privilege.description }}
								</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 续费规则 -->
				<view class="rule-section">
					<text class="section-title">续费规则</text>
					<view class="rule-list">
						<text class="rule-item">会员到期前7天可进行续费操作</text>
						<text class="rule-item">续费后会员时间将在原有基础上累加</text>
						<text class="rule-item">支持开启自动续费，到期前自动扣费</text>
						<text class="rule-item">自动续费可随时关闭，不影响当前会员期限</text>
						<text class="rule-item">续费价格以当时平台公布价格为准</text>
					</view>
				</view>

				<!-- 升级规则 -->
				<view class="rule-section">
					<text class="section-title">升级规则</text>
					<view class="rule-list">
						<text class="rule-item">1. 低等级会员可随时升级到高等级</text>
						<text class="rule-item">2. 升级后立即享受高等级会员权益</text>
						<text class="rule-item">3. 升级费用 = 高等级价格 - 剩余时间价值</text>
						<text class="rule-item">4. 升级后会员到期时间保持不变</text>
						<text class="rule-item">5. 不支持从高等级降级到低等级</text>
					</view>
				</view>

				<!-- 使用规则 -->
				<view class="rule-section">
					<text class="section-title">使用规则</text>
					<view class="rule-list">
						<text class="rule-item">1. 会员权益仅限本人使用，不得转让</text>
						<text class="rule-item">2. 会员权益自开通后立即生效</text>
						<text class="rule-item">3. 会员到期后权益自动失效</text>
						<text class="rule-item">4. 违规使用会员权益将被终止服务</text>
						<text class="rule-item">5. 技术维护期间部分功能可能暂停</text>
					</view>
				</view>

				<!-- 退费规则 -->
				<view class="rule-section">
					<text class="section-title">退费规则</text>
					<view class="rule-list">
						<text class="rule-item">1. 会员服务一经开通，原则上不予退费</text>
						<text class="rule-item">2. 因系统故障导致无法使用可申请退费</text>
						<text class="rule-item">3. 重复购买可在7天内申请退费</text>
						<text class="rule-item">4. 退费申请需联系客服并提供相关证明</text>
						<text class="rule-item">5. 退费金额将原路返回支付账户</text>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				selectedPackage: 2,
				selectedPayment: 'wechat',
				selectedLevel: 2, // 当前选中查看的VIP等级
				agreementChecked: false, // 协议同意状态
				showRulesModal: false, // 会员规则弹窗显示状态

				userInfo: {
					name: '闲伴用户',
					avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
					isVip: false, // 改为false以显示购买区域
					vipLevel: 0, // 用户当前VIP等级
					vipExpireDate: '2025-03-15',
					totalSpent: 168
				},

				// VIP等级定义
				vipLevels: [
					{
						id: 1,
						name: '黄金会员',
						description: '入门级会员，享受基础特权',
						price: 19,
						theme: 'gold',
						icon: 'medal',
						colors: {
							primary: '#FFD700',
							secondary: '#FFA000',
							accent: '#FF8F00'
						}
					},
					{
						id: 2,
						name: '钻石会员',
						description: '进阶会员，解锁更多功能',
						price: 39,
						theme: 'diamond',
						icon: 'star-filled',
						colors: {
							primary: '#00E5FF',
							secondary: '#00BCD4',
							accent: '#0097A7'
						}
					},
					{
						id: 3,
						name: '白金会员',
						description: '高级会员，尊享专属服务',
						price: 69,
						theme: 'platinum',
						icon: 'crown',
						colors: {
							primary: '#E8EAF6',
							secondary: '#C5CAE9',
							accent: '#9FA8DA'
						}
					},
					{
						id: 4,
						name: '黑金会员',
						description: '顶级会员，至尊无上体验',
						price: 99,
						theme: 'black-gold',
						icon: 'vip-filled',
						colors: {
							primary: '#FFD700',
							secondary: '#212121',
							accent: '#424242'
						}
					}
				],
				
				// 按VIP等级分组的权益
				levelPrivileges: {
					1: [ // 黄金会员
						{
							id: 1,
							name: '专属身份标识',
							description: '个人资料显示黄金VIP标识',
							icon: 'star',
							theme: 'gold'
						},
						{
							id: 2,
							name: '优先匹配推荐',
							description: '优先推荐给其他用户',
							icon: 'fire',
							theme: 'gold'
						},
						{
							id: 3,
							name: '去除广告',
							description: '享受无广告纯净体验',
							icon: 'close',
							theme: 'gold'
						},
						{
							id: 4,
							name: '基础筛选',
							description: '使用基础筛选功能',
							icon: 'tune',
							theme: 'gold'
						}
					],
					2: [ // 钻石会员
						{
							id: 5,
							name: '无限点赞特权',
							description: '每日点赞次数无限制',
							icon: 'heart-filled',
							theme: 'diamond'
						},
						{
							id: 6,
							name: '高级筛选功能',
							description: '更多筛选条件选择',
							icon: 'tune',
							theme: 'diamond'
						},
						{
							id: 7,
							name: '专属客服通道',
							description: '优先客服支持服务',
							icon: 'help',
							theme: 'diamond'
						},
						{
							id: 8,
							name: '消息已读显示',
							description: '查看消息已读状态',
							icon: 'checkmarkempty',
							theme: 'diamond'
						}
					],
					3: [ // 白金会员
						{
							id: 9,
							name: '数据统计分析',
							description: '详细的个人数据报告',
							icon: 'bar-chart',
							theme: 'platinum'
						},
						{
							id: 10,
							name: '专属活动邀请',
							description: '优先参与线下活动',
							icon: 'calendar',
							theme: 'platinum'
						},
						{
							id: 11,
							name: '隐身浏览模式',
							description: '匿名浏览他人资料',
							icon: 'eye-slash',
							theme: 'platinum'
						},
						{
							id: 12,
							name: '超级曝光',
							description: '获得更多展示机会',
							icon: 'trending-up',
							theme: 'platinum'
						}
					],
					4: [ // 黑金会员
						{
							id: 13,
							name: '专属管家服务',
							description: '一对一专属管家服务',
							icon: 'person-filled',
							theme: 'black-gold'
						},
						{
							id: 14,
							name: '定制化推荐',
							description: 'AI智能个性化推荐',
							icon: 'gear',
							theme: 'black-gold'
						},
						{
							id: 15,
							name: '线下活动优先',
							description: '所有活动优先参与权',
							icon: 'location',
							theme: 'black-gold'
						},
						{
							id: 16,
							name: '至尊身份标识',
							description: '独一无二的黑金标识',
							icon: 'vip',
							theme: 'black-gold'
						}
					]
				},

				vipPackages: [
					{
						id: 1,
						name: '月度会员',
						duration: '1个月',
						price: 19,
						originalPrice: 29,
						months: 1,
						popular: false,
						recommended: false,
						highlights: ['专属标识', '优先匹配', '高级筛选', '去除广告']
					},
					{
						id: 2,
						name: '季度会员',
						duration: '3个月',
						price: 49,
						originalPrice: 87,
						months: 3,
						popular: true,
						recommended: true,
						highlights: ['月度全部特权', '无限点赞', '专属客服', '数据分析']
					},
					{
						id: 3,
						name: '年度会员',
						duration: '12个月',
						price: 168,
						originalPrice: 348,
						months: 12,
						popular: false,
						recommended: false,
						highlights: ['季度全部特权', '专属活动', '隐身模式', '优先审核']
					}
				]
			}
		},
		
		computed: {
			selectedPackageInfo() {
				return this.vipPackages.find(p => p.id === this.selectedPackage) || this.vipPackages[0];
			},

			remainingDays() {
				if (!this.userInfo.isVip) return 0;
				const expireDate = new Date(this.userInfo.vipExpireDate);
				const today = new Date();
				const diffTime = expireDate - today;
				return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
			}
		},
		
		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},
		
		methods: {
			goBack() {
				// 直接使用switchTab跳转到"我的"页面，因为它是tabBar页面
				uni.switchTab({
					url: '/pages/profile/profile'
				});
			},

			showHelp() {
				this.showRulesModal = true;
			},

			closeRulesModal() {
				this.showRulesModal = false;
			},

			scrollToPackages() {
				// 滚动到套餐选择区域
				uni.pageScrollTo({
					selector: '#packages',
					duration: 300
				});
			},

			selectPackage(id) {
				this.selectedPackage = id;
			},

			selectPayment(method) {
				this.selectedPayment = method;
			},

			// 会员卡片相关方法
			onCardChange(e) {
				this.selectedLevel = e.detail.current + 1;
			},

			getCurrentLevelName() {
				const level = this.vipLevels.find(l => l.id === this.selectedLevel);
				return level ? level.name : '黄金会员';
			},

			getIndicatorPosition() {
				// 计算指示器位置，基于当前选中的等级
				const totalLevels = this.vipLevels.length;
				const position = ((this.selectedLevel - 1) / (totalLevels - 1)) * 100;
				return position + '%';
			},

			getPreviewPrivileges(levelId) {
				const privileges = this.levelPrivileges[levelId] || [];
				return privileges.slice(0, 4); // 显示前4个特权作为预览
			},

			getPrivilegeColor(theme) {
				const colorMap = {
					'gold': '#FFD700',
					'diamond': '#00E5FF',
					'platinum': '#E8EAF6',
					'black-gold': '#FFD700'
				};
				return colorMap[theme] || '#FFD700';
			},

			getCurrentLevelInfo() {
				return this.vipLevels.find(level => level.id === this.selectedLevel) || this.vipLevels[0];
			},

			getCurrentLevelPrivileges() {
				return this.levelPrivileges[this.selectedLevel] || [];
			},

			upgradeToLevel(levelId) {
				const levelInfo = this.vipLevels.find(level => level.id === levelId);
				if (levelInfo) {
					uni.showModal({
						title: '升级会员',
						content: `确认升级至${levelInfo.name}(¥${levelInfo.price}/月)？`,
						success: (res) => {
							if (res.confirm) {
								this.scrollToPackages();
							}
						}
					});
				}
			},

			purchaseVip() {
				if (!this.agreementChecked) {
					uni.showToast({
						title: '请先同意服务协议',
						icon: 'none'
					});
					return;
				}

				const packageInfo = this.selectedPackageInfo;
				uni.showModal({
					title: '确认开通',
					content: `确认开通${packageInfo.name}(¥${packageInfo.price})？`,
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '支付功能开发中',
								icon: 'none'
							});
						}
					}
				});
			},

			toggleAgreement() {
				this.agreementChecked = !this.agreementChecked;
			},

			renewVip() {
				this.scrollToPackages();
			},

			upgradeVip() {
				this.scrollToPackages();
			},

			// 功能管理方法
			goToRightsCenter() {
				uni.showToast({
					title: '权益中心开发中',
					icon: 'none'
				});
			},

			goToRenewalRules() {
				uni.showModal({
					title: '续费规则',
					content: '1. 会员到期前7天可续费\n2. 续费后时间累加\n3. 支持自动续费设置',
					showCancel: false
				});
			},

			goToBillingHistory() {
				uni.showToast({
					title: '消费记录开发中',
					icon: 'none'
				});
			},

			goToMemberGuide() {
				uni.showModal({
					title: '会员说明',
					content: '会员权益详细说明和使用指南',
					showCancel: false
				});
			},

			viewTerms() {
				uni.navigateTo({
					url: '/pages/profile-features/vip/member-agreement'
				});
			},

			viewPrivacy() {
				uni.showToast({
					title: '隐私政策开发中',
					icon: 'none'
				});
			},

			getPrivilegeIconColor(privilege) {
				if (this.userInfo.isVip && privilege.level <= this.userInfo.vipLevel) {
					return '#FFD700';
				}
				return '#666';
			}
		}
	}
</script>

<style lang="scss" scoped>
	.vip-page {
		background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 100%);
		min-height: 100vh;
		color: #fff;
	}

	// 顶部导航栏
	.top-header {
		position: relative;
		background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);

		.status-bar {
			background: transparent;
		}

		.nav-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;

			.nav-left, .nav-right {
				.back-btn, .help-btn {
					width: 44rpx;
					height: 44rpx;
					border-radius: 22rpx;
					background: rgba(255, 215, 0, 0.1);
					border: 1rpx solid rgba(255, 215, 0, 0.3);
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.3s ease;

					&:active {
						background: rgba(255, 215, 0, 0.2);
						transform: scale(0.95);
					}
				}
			}

			.nav-center {
				flex: 1;
				text-align: center;

				.nav-title {
					font-size: 36rpx;
					font-weight: 700;
					color: #FFD700;
					text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
				}
			}
		}
	}

	// 会员卡片展示区域
	.member-card-section {
		padding: 24rpx 0;

		// 等级指示器
		.level-indicator {
			padding: 0 24rpx 24rpx;
			text-align: center;

			.indicator-line {
				position: relative;
				height: 4rpx;
				margin-bottom: 16rpx;

				.indicator-track {
					width: 100%;
					height: 100%;
					background: rgba(255, 255, 255, 0.1);
					border-radius: 2rpx;
				}

				.indicator-dot {
					position: absolute;
					top: -6rpx;
					width: 16rpx;
					height: 16rpx;
					background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
					border-radius: 50%;
					box-shadow: 0 0 16rpx rgba(255, 215, 0, 0.6);
					transition: all 0.3s ease;
					transform: translateX(-50%);
				}
			}

			.current-level-text {
				font-size: 28rpx;
				color: #FFD700;
				font-weight: 700;
				text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
			}
		}

		// 会员卡片容器
		.member-cards-container {
			.member-cards-swiper {
				height: 400rpx;

				.card-item {
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 0 8rpx;
				}

				.member-card {
					width: 100%;
					height: 360rpx;
					border-radius: 24rpx;
					position: relative;
					overflow: hidden;
					transition: all 0.3s ease;
					transform: scale(0.9);
					opacity: 0.7;

					&.active {
						transform: scale(1);
						opacity: 1;
					}

					&.current {
						box-shadow: 0 16rpx 48rpx rgba(255, 215, 0, 0.4);
					}

					// 不同主题的卡片样式
					&.gold {
						background: linear-gradient(135deg, #FFD700 0%, #FFA000 50%, #FF8F00 100%);
					}

					&.diamond {
						background: linear-gradient(135deg, #00E5FF 0%, #00BCD4 50%, #0097A7 100%);
					}

					&.platinum {
						background: linear-gradient(135deg, #E8EAF6 0%, #C5CAE9 50%, #9FA8DA 100%);
					}

					&.black-gold {
						background: linear-gradient(135deg, #212121 0%, #424242 30%, #FFD700 70%, #FFA000 100%);
					}

					// 卡片装饰
					.card-decorations {
						position: absolute;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;
						pointer-events: none;
						overflow: hidden;

						.decoration-pattern {
							position: absolute;
							border-radius: 50%;
							opacity: 0.1;

							&.pattern-1 {
								width: 200rpx;
								height: 200rpx;
								top: -100rpx;
								right: -50rpx;
								background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
								animation: float 8s ease-in-out infinite;
							}

							&.pattern-2 {
								width: 120rpx;
								height: 120rpx;
								bottom: -60rpx;
								left: -30rpx;
								background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
								animation: float 10s ease-in-out infinite reverse;
							}

							&.pattern-3 {
								width: 80rpx;
								height: 80rpx;
								top: 50%;
								left: 20%;
								background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
								animation: float 6s ease-in-out infinite;
							}
						}

						.level-watermark {
							position: absolute;
							bottom: 20rpx;
							right: 20rpx;
							font-size: 120rpx;
							font-weight: 900;
							color: rgba(255, 255, 255, 0.05);
							line-height: 1;
							pointer-events: none;
						}

						// 不同主题的特殊装饰
						&.gold {
							&::before {
								content: '';
								position: absolute;
								top: 0;
								left: 0;
								right: 0;
								bottom: 0;
								background:
									radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
									radial-gradient(circle at 80% 80%, rgba(255, 215, 0, 0.3) 0%, transparent 50%);
							}
						}

						&.diamond {
							&::before {
								content: '';
								position: absolute;
								top: 0;
								left: 0;
								right: 0;
								bottom: 0;
								background:
									radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
									radial-gradient(circle at 70% 70%, rgba(0, 229, 255, 0.2) 0%, transparent 50%);
							}
						}

						&.platinum {
							&::before {
								content: '';
								position: absolute;
								top: 0;
								left: 0;
								right: 0;
								bottom: 0;
								background:
									radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.4) 0%, transparent 50%),
									radial-gradient(circle at 75% 75%, rgba(159, 168, 218, 0.3) 0%, transparent 50%);
							}
						}

						&.black-gold {
							&::before {
								content: '';
								position: absolute;
								top: 0;
								left: 0;
								right: 0;
								bottom: 0;
								background:
									radial-gradient(circle at 20% 20%, rgba(255, 215, 0, 0.3) 0%, transparent 50%),
									radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
							}
						}
					}

			// 卡片内容
			.card-content {
				position: relative;
				z-index: 2;
				padding: 32rpx;
				height: 100%;
				display: flex;
				flex-direction: column;

				// 卡片头部
				.card-header {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 24rpx;

					.level-badge {
						width: 56rpx;
						height: 56rpx;
						border-radius: 28rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						background: rgba(255, 255, 255, 0.2);
						backdrop-filter: blur(10rpx);

						&.gold {
							background: rgba(255, 215, 0, 0.3);
						}

						&.diamond {
							background: rgba(0, 229, 255, 0.3);
						}

						&.platinum {
							background: rgba(232, 234, 246, 0.3);
						}

						&.black-gold {
							background: rgba(255, 215, 0, 0.3);
						}
					}

					.card-status {
						padding: 8rpx 16rpx;
						border-radius: 16rpx;
						font-size: 22rpx;
						font-weight: 700;
						border: 2rpx solid;
						box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);

						&:not(.locked) {
							background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
							color: #fff;
							border-color: #4CAF50;
							text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
						}

						&.locked {
							background: linear-gradient(135deg, #FF5722 0%, #F44336 100%);
							color: #fff;
							border-color: #FF5722;
							text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
						}

						.status-label {
							font-size: 22rpx;
							font-weight: 700;
							letter-spacing: 1rpx;
						}
					}
				}

				// 会员信息
				.member-info {
					flex: 1;
					margin-bottom: 24rpx;

					.level-name {
						font-size: 36rpx;
						font-weight: 700;
						color: white;
						margin-bottom: 8rpx;
						text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
					}

					.level-desc {
						font-size: 24rpx;
						color: rgba(255, 255, 255, 0.9);
						margin-bottom: 16rpx;
						line-height: 1.4;
					}

					.level-price {
						display: flex;
						align-items: baseline;
						gap: 4rpx;

						.price-text {
							font-size: 40rpx;
							font-weight: 700;
							color: white;
							text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
						}

						.price-unit {
							font-size: 24rpx;
							color: rgba(255, 255, 255, 0.8);
						}
					}
				}

				// 用户头像区域（当前等级）
				.user-avatar-section {
					display: flex;
					align-items: center;
					gap: 16rpx;
					background: rgba(255, 255, 255, 0.1);
					border-radius: 16rpx;
					padding: 16rpx;
					backdrop-filter: blur(10rpx);

					.user-avatar {
						width: 64rpx;
						height: 64rpx;
						border-radius: 50%;
						border: 3rpx solid rgba(255, 255, 255, 0.5);
					}

					.user-info {
						flex: 1;

						.user-name {
							font-size: 24rpx;
							color: white;
							font-weight: 600;
							margin-bottom: 4rpx;
						}

						.expire-date {
							font-size: 20rpx;
							color: rgba(255, 255, 255, 0.8);
						}
					}
				}

				// 权益预览
				.privileges-preview {
					display: flex;
					flex-direction: column;
					gap: 12rpx;
					background: rgba(255, 255, 255, 0.08);
					border-radius: 12rpx;
					padding: 16rpx;
					backdrop-filter: blur(10rpx);
					border: 1rpx solid rgba(255, 255, 255, 0.1);

					.privilege-item {
						display: flex;
						align-items: center;
						gap: 12rpx;
						font-size: 22rpx;
						color: rgba(255, 255, 255, 0.95);
						padding: 8rpx 0;
						border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);

						&:last-child {
							border-bottom: none;
						}

						uni-icons {
							flex-shrink: 0;
							width: 24rpx;
							height: 24rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							background: rgba(255, 255, 255, 0.15);
							border-radius: 50%;
							padding: 4rpx;
						}

						.privilege-text {
							flex: 1;
							line-height: 1.4;
							font-weight: 500;
							text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
						}
					}
				}
			}
		}
	}
		}
	}

	// 功能管理区域
	.function-management {
		padding: 24rpx;

		.management-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 16rpx;

			.management-item {
				background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
				border: 1rpx solid rgba(255, 215, 0, 0.2);
				border-radius: 16rpx;
				padding: 24rpx;
				text-align: center;
				transition: all 0.3s ease;

				&:active {
					border-color: rgba(255, 215, 0, 0.5);
					transform: translateY(-4rpx);
					box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.2);
				}

				.item-icon {
					width: 64rpx;
					height: 64rpx;
					border-radius: 32rpx;
					background: rgba(255, 215, 0, 0.1);
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 0 auto 16rpx;

					&.rights {
						background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 160, 0, 0.2) 100%);
					}

					&.renewal {
						background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(102, 187, 106, 0.2) 100%);
					}

					&.billing {
						background: linear-gradient(135deg, rgba(156, 39, 176, 0.2) 0%, rgba(187, 107, 217, 0.2) 100%);
					}

					&.guide {
						background: linear-gradient(135deg, rgba(33, 150, 243, 0.2) 0%, rgba(100, 181, 246, 0.2) 100%);
					}
				}

				.item-title {
					font-size: 26rpx;
					color: #fff;
					font-weight: 600;
					display: block;
					margin-bottom: 8rpx;
				}

				.item-desc {
					font-size: 22rpx;
					color: rgba(255, 255, 255, 0.7);
					line-height: 1.3;
				}
			}
		}
	}

	// VIP等级权益展示
	.vip-levels-section {
		padding: 24rpx;

		.levels-header {
			text-align: center;
			margin-bottom: 32rpx;

			.levels-title {
				font-size: 36rpx;
				font-weight: 700;
				color: #FFD700;
				display: block;
				margin-bottom: 12rpx;
				text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
			}

			.levels-subtitle {
				font-size: 26rpx;
				color: rgba(255, 255, 255, 0.7);
			}
		}

		// VIP等级选择器
		.level-selector {
			margin-bottom: 32rpx;

			.level-tabs {
				white-space: nowrap;
				padding: 0 0 16rpx;

				.level-tab {
					display: inline-block;
					width: 200rpx;
					margin-right: 16rpx;
					background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
					border: 2rpx solid rgba(255, 215, 0, 0.2);
					border-radius: 16rpx;
					padding: 24rpx 16rpx;
					text-align: center;
					transition: all 0.3s ease;
					position: relative;

					&.active {
						border-color: #FFD700;
						box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.3);
						transform: translateY(-4rpx);
					}

					&.unlocked {
						.level-icon {
							box-shadow: 0 0 16rpx rgba(255, 215, 0, 0.4);
						}
					}

					&.current {
						&::after {
							content: '当前';
							position: absolute;
							top: -12rpx;
							right: -12rpx;
							background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
							color: #fff;
							font-size: 20rpx;
							font-weight: 700;
							padding: 8rpx 12rpx;
							border-radius: 12rpx;
							border: 2rpx solid #fff;
							box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.4);
							z-index: 10;
							letter-spacing: 1rpx;
						}
					}

					.level-icon {
						width: 56rpx;
						height: 56rpx;
						border-radius: 28rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin: 0 auto 12rpx;
						transition: all 0.3s ease;

						&.gold {
							background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
						}

						&.diamond {
							background: linear-gradient(135deg, #00BCD4 0%, #26C6DA 100%);
						}

						&.platinum {
							background: linear-gradient(135deg, #9E9E9E 0%, #BDBDBD 100%);
						}

						&.black-gold {
							background: linear-gradient(135deg, #212121 0%, #FFD700 100%);
						}
					}

					.level-name {
						font-size: 24rpx;
						color: #fff;
						font-weight: 600;
						display: block;
						margin-bottom: 8rpx;
					}

					.level-price {
						font-size: 20rpx;
						color: #FFD700;
						font-weight: 500;
						display: block;
						margin-bottom: 8rpx;
					}

					.level-status {
						.status-text {
							font-size: 18rpx;
							color: #4CAF50;
							font-weight: 500;
						}
					}
				}
			}
		}

		// 等级权益展示
		.level-privileges {
			.privileges-header {
				background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
				border: 1rpx solid rgba(255, 215, 0, 0.3);
				border-radius: 16rpx;
				padding: 24rpx;
				margin-bottom: 24rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.header-info {
					.current-level {
						font-size: 32rpx;
						color: #FFD700;
						font-weight: 700;
						display: block;
						margin-bottom: 8rpx;
						text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
					}

					.level-desc {
						font-size: 24rpx;
						color: rgba(255, 255, 255, 0.8);
						line-height: 1.4;
					}
				}

				.unlock-status {
					text-align: right;

					&.unlocked .status-text {
						color: #4CAF50;
					}

					.status-text {
						font-size: 24rpx;
						color: rgba(255, 255, 255, 0.6);
						font-weight: 600;
					}
				}
			}

			.privileges-grid {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				gap: 16rpx;
				margin-bottom: 24rpx;

				.privilege-card {
					background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
					border: 1rpx solid rgba(255, 215, 0, 0.2);
					border-radius: 16rpx;
					padding: 20rpx;
					transition: all 0.3s ease;

					&.unlocked {
						border-color: rgba(255, 215, 0, 0.5);
						box-shadow: 0 4rpx 16rpx rgba(255, 215, 0, 0.2);
					}

					&.locked {
						opacity: 0.6;
						filter: grayscale(0.5);
					}

					.card-header {
						display: flex;
						align-items: center;
						justify-content: space-between;
						margin-bottom: 16rpx;

						.privilege-icon {
							width: 40rpx;
							height: 40rpx;
							border-radius: 20rpx;
							display: flex;
							align-items: center;
							justify-content: center;

							&.gold {
								background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
							}

							&.diamond {
								background: linear-gradient(135deg, #00BCD4 0%, #26C6DA 100%);
							}

							&.platinum {
								background: linear-gradient(135deg, #9E9E9E 0%, #BDBDBD 100%);
							}

							&.black-gold {
								background: linear-gradient(135deg, #212121 0%, #FFD700 100%);
							}
						}

						.unlock-badge, .lock-badge {
							width: 24rpx;
							height: 24rpx;
							border-radius: 12rpx;
							display: flex;
							align-items: center;
							justify-content: center;
						}

						.unlock-badge {
							background: rgba(76, 175, 80, 0.2);
						}

						.lock-badge {
							background: rgba(158, 158, 158, 0.2);
						}
					}

					.privilege-content {
						.privilege-name {
							font-size: 26rpx;
							color: #fff;
							font-weight: 600;
							display: block;
							margin-bottom: 8rpx;
						}

						.privilege-desc {
							font-size: 22rpx;
							color: rgba(255, 255, 255, 0.7);
							line-height: 1.4;
						}
					}
				}
			}

			.upgrade-hint {
				background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
				border-radius: 16rpx;
				padding: 24rpx;
				text-align: center;

				.hint-content {
					.hint-title {
						font-size: 28rpx;
						color: #000;
						font-weight: 700;
						display: block;
						margin-bottom: 8rpx;
					}

					.hint-desc {
						font-size: 22rpx;
						color: rgba(0, 0, 0, 0.7);
						display: block;
						margin-bottom: 16rpx;
						line-height: 1.4;
					}

					.upgrade-action {
						display: inline-flex;
						align-items: center;
						gap: 8rpx;
						background: rgba(0, 0, 0, 0.1);
						border-radius: 20rpx;
						padding: 12rpx 24rpx;
						transition: all 0.3s ease;

						&:active {
							background: rgba(0, 0, 0, 0.2);
							transform: scale(0.98);
						}

						.action-text {
							font-size: 24rpx;
							color: #000;
							font-weight: 600;
						}
					}
				}
			}
		}
	}

	// 会员套餐选择
	.packages-section {
		padding: 24rpx;

		.packages-header {
			text-align: center;
			margin-bottom: 32rpx;

			.packages-title {
				font-size: 36rpx;
				font-weight: 700;
				color: #FFD700;
				display: block;
				margin-bottom: 12rpx;
				text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
			}

			.packages-subtitle {
				font-size: 26rpx;
				color: rgba(255, 255, 255, 0.7);
			}
		}

		.package-selector {
			display: flex;
			flex-direction: column;
			gap: 20rpx;

			.package-option {
				position: relative;
				background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
				border: 2rpx solid rgba(255, 215, 0, 0.2);
				border-radius: 20rpx;
				padding: 32rpx;
				transition: all 0.3s ease;

				&.selected {
					border-color: #FFD700;
					box-shadow: 0 8rpx 32rpx rgba(255, 215, 0, 0.3);
					transform: translateY(-4rpx);
				}

				&.recommended {
					border-color: rgba(255, 215, 0, 0.6);

					.recommendation-tag {
						position: absolute;
						top: -12rpx;
						left: 32rpx;
						background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
						border-radius: 12rpx;
						padding: 6rpx 16rpx;

						.tag-text {
							font-size: 20rpx;
							color: #000;
							font-weight: 700;
						}
					}
				}

				&.popular {
					.popular-tag {
						position: absolute;
						top: -12rpx;
						right: 32rpx;
						background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
						border-radius: 12rpx;
						padding: 6rpx 16rpx;

						.tag-text {
							font-size: 20rpx;
							color: #fff;
							font-weight: 700;
						}
					}
				}

				.package-header {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 16rpx;

					.package-name {
						font-size: 30rpx;
						font-weight: 700;
						color: #333;
					}

					.package-badge {
						background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
						border-radius: 12rpx;
						padding: 4rpx 12rpx;

						.badge-text {
							font-size: 20rpx;
							color: #333;
							font-weight: 600;
						}
					}
				}

				.package-price {
					display: flex;
					align-items: baseline;
					margin-bottom: 8rpx;

					.price-symbol {
						font-size: 24rpx;
						color: #FFD700;
						font-weight: 600;
					}

					.price-number {
						font-size: 48rpx;
						color: #FFD700;
						font-weight: 700;
						margin: 0 4rpx;
					}

					.price-unit {
						font-size: 24rpx;
						color: #999;
					}
				}

				.package-original {
					display: flex;
					align-items: center;
					gap: 12rpx;
					margin-bottom: 16rpx;

					.original-price {
						font-size: 22rpx;
						color: #999;
						text-decoration: line-through;
					}

					.discount {
						font-size: 22rpx;
						color: #FF6B6B;
						font-weight: 600;
					}
				}

				.package-features {
					.feature-item {
						display: flex;
						align-items: center;
						gap: 12rpx;
						margin-bottom: 8rpx;

						.feature-text {
							font-size: 24rpx;
							color: #666;
						}
					}
				}
			}
		}
	}

	// 购买操作区域
	.purchase-section {
		padding: 24rpx;
		margin-bottom: 120rpx;

		// 选中套餐信息
		.selected-package-info {
			background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
			border: 1rpx solid rgba(255, 215, 0, 0.3);
			border-radius: 16rpx;
			padding: 24rpx;
			margin-bottom: 24rpx;

			.package-header {
				margin-bottom: 16rpx;

				.section-title {
					font-size: 28rpx;
					color: #FFD700;
					font-weight: 600;
				}
			}

			.package-details {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.package-main {
					.package-name {
						font-size: 32rpx;
						color: #fff;
						font-weight: 700;
						display: block;
						margin-bottom: 8rpx;
					}

					.package-duration {
						font-size: 24rpx;
						color: rgba(255, 255, 255, 0.7);
					}
				}

				.package-pricing {
					text-align: right;

					.current-price {
						font-size: 40rpx;
						color: #FFD700;
						font-weight: 700;
						display: block;
						margin-bottom: 4rpx;
					}

					.original-price {
						font-size: 22rpx;
						color: #999;
						text-decoration: line-through;
					}
				}
			}
		}

		// 支付方式选择
		.payment-section {
			background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
			border: 1rpx solid rgba(255, 215, 0, 0.3);
			border-radius: 16rpx;
			padding: 24rpx;
			margin-bottom: 24rpx;

			.payment-header {
				margin-bottom: 20rpx;

				.section-title {
					font-size: 28rpx;
					color: #FFD700;
					font-weight: 600;
				}
			}

			.payment-options {
				.payment-option {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 20rpx;
					background: rgba(255, 255, 255, 0.05);
					border: 2rpx solid rgba(255, 255, 255, 0.1);
					border-radius: 12rpx;
					margin-bottom: 16rpx;
					transition: all 0.3s ease;

					&:last-child {
						margin-bottom: 0;
					}

					&.selected {
						border-color: #FFD700;
						background: rgba(255, 215, 0, 0.1);
					}

					.payment-left {
						display: flex;
						align-items: center;
						gap: 16rpx;

						.payment-icon {
							width: 40rpx;
							height: 40rpx;
							border-radius: 8rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							font-size: 24rpx;
							font-weight: 700;

							&.wechat {
								background: #07C160;
								color: #fff;
							}

							&.alipay {
								background: #1677FF;
								color: #fff;
							}
						}

						.payment-name {
							font-size: 28rpx;
							color: #fff;
							font-weight: 500;
						}
					}

					.payment-radio {
						width: 32rpx;
						height: 32rpx;
						border: 2rpx solid rgba(255, 255, 255, 0.3);
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						transition: all 0.3s ease;

						&.checked {
							border-color: #FFD700;
							background: rgba(255, 215, 0, 0.2);
						}

						.radio-dot {
							width: 16rpx;
							height: 16rpx;
							background: #FFD700;
							border-radius: 50%;
						}
					}
				}
			}
		}

		// 协议同意
		.agreement-section {
			padding: 20rpx 0;

			.agreement-checkbox {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 12rpx;
				flex-wrap: wrap;

				.checkbox {
					width: 32rpx;
					height: 32rpx;
					border: 2rpx solid rgba(255, 215, 0, 0.5);
					border-radius: 6rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.3s ease;
					flex-shrink: 0;

					&.checked {
						background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
						border-color: #FFD700;
					}
				}

				.agreement-text {
					font-size: 24rpx;
					color: rgba(255, 255, 255, 0.8);
				}

				.agreement-link {
					font-size: 24rpx;
					color: #FFD700;
					text-decoration: underline;
					font-weight: 500;
				}
			}
		}

		// 立即支付按钮
		.pay-button-section {
			.pay-btn {
				width: 100%;
				background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
				border-radius: 16rpx;
				padding: 24rpx;
				text-align: center;
				transition: all 0.3s ease;
				box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.3);

				&:active:not(.disabled) {
					transform: scale(0.98);
					box-shadow: 0 4rpx 16rpx rgba(255, 215, 0, 0.4);
				}

				&.disabled {
					background: linear-gradient(135deg, #666 0%, #999 100%);
					opacity: 0.6;
					box-shadow: none;

					.pay-text {
						color: #ccc;
					}
				}

				.pay-text {
					font-size: 36rpx;
					color: #000;
					font-weight: 700;
					letter-spacing: 2rpx;
				}
			}
		}
	}

	// 会员管理区域
	.member-management {
		padding: 24rpx;
		margin-bottom: 120rpx;

		.management-header {
			margin-bottom: 24rpx;

			.management-title {
				font-size: 36rpx;
				color: #FFD700;
				font-weight: 700;
				text-align: center;
				text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
			}
		}

		.management-actions {
			.action-card {
				background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
				border: 1rpx solid rgba(255, 215, 0, 0.3);
				border-radius: 16rpx;
				padding: 24rpx;
				margin-bottom: 16rpx;
				display: flex;
				align-items: center;
				transition: all 0.3s ease;

				&:last-child {
					margin-bottom: 0;
				}

				&:active {
					transform: scale(0.98);
					background: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 100%);
				}

				.action-icon {
					width: 64rpx;
					height: 64rpx;
					border-radius: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20rpx;

					&.renew {
						background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
					}

					&.upgrade {
						background: linear-gradient(135deg, #FF9800 0%, #FFB74D 100%);
					}
				}

				.action-info {
					flex: 1;

					.action-title {
						font-size: 30rpx;
						color: #fff;
						font-weight: 600;
						display: block;
						margin-bottom: 8rpx;
					}

					.action-desc {
						font-size: 24rpx;
						color: rgba(255, 255, 255, 0.7);
					}
				}

				.action-arrow {
					width: 32rpx;
					height: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
	}

	// VIP管理
	.manage-section {
		padding: 0 24rpx 120rpx;

		.manage-container {
			background: #fff;
			border-radius: 20rpx;
			overflow: hidden;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

			.manage-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 24rpx;
				border-bottom: 1rpx solid #f8f9fa;
				transition: all 0.3s ease;

				&:last-child {
					border-bottom: none;
				}

				&:active {
					background: #f8f9fa;
				}

				.item-left {
					display: flex;
					align-items: center;
					gap: 16rpx;

					.item-text {
						font-size: 28rpx;
						color: #333;
						font-weight: 500;
					}
				}
			}
		}
	}



	// 会员规则弹窗
	.rules-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		padding: 40rpx;

		.modal-content {
			background: #fff;
			border-radius: 24rpx;
			width: 100%;
			max-width: 640rpx;
			max-height: 80vh;
			overflow: hidden;
			position: relative;

			.modal-header {
				position: relative;
				background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
				padding: 32rpx 24rpx 24rpx;
				overflow: hidden;

				.decoration-circles {
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					overflow: hidden;

					.circle {
						position: absolute;
						border-radius: 50%;
						opacity: 0.3;

						&.circle-1 {
							width: 120rpx;
							height: 120rpx;
							background: #FF6B6B;
							top: -60rpx;
							left: -60rpx;
						}

						&.circle-2 {
							width: 80rpx;
							height: 80rpx;
							background: #4ECDC4;
							top: 20rpx;
							right: 40rpx;
						}

						&.circle-3 {
							width: 60rpx;
							height: 60rpx;
							background: #45B7D1;
							bottom: -30rpx;
							left: 100rpx;
						}

						&.circle-4 {
							width: 100rpx;
							height: 100rpx;
							background: #96CEB4;
							top: -50rpx;
							right: -50rpx;
						}

						&.circle-5 {
							width: 40rpx;
							height: 40rpx;
							background: #FFEAA7;
							bottom: 10rpx;
							right: 20rpx;
						}
					}
				}

				.modal-title {
					font-size: 36rpx;
					font-weight: 700;
					color: #000;
					text-align: center;
					position: relative;
					z-index: 2;
				}

				.close-btn {
					position: absolute;
					top: 24rpx;
					right: 24rpx;
					width: 48rpx;
					height: 48rpx;
					background: rgba(0, 0, 0, 0.1);
					border-radius: 24rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					z-index: 3;
					transition: all 0.3s ease;

					&:active {
						background: rgba(0, 0, 0, 0.2);
						transform: scale(0.95);
					}
				}
			}

			.modal-body {
				height: 60vh;
				padding: 24rpx;

				.rule-section {
					margin-bottom: 32rpx;

					&:last-child {
						margin-bottom: 0;
					}

					.section-title {
						font-size: 32rpx;
						font-weight: 700;
						color: #333;
						margin-bottom: 16rpx;
						padding-left: 16rpx;
						border-left: 6rpx solid #FFD700;
						display: block;
					}

					.level-list {
						.level-item {
							display: flex;
							align-items: center;
							padding: 16rpx;
							background: #f8f9fa;
							border-radius: 12rpx;
							margin-bottom: 12rpx;

							&:last-child {
								margin-bottom: 0;
							}

							.level-icon {
								width: 48rpx;
								height: 48rpx;
								border-radius: 24rpx;
								display: flex;
								align-items: center;
								justify-content: center;
								margin-right: 16rpx;

								&.gold {
									background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
								}

								&.diamond {
									background: linear-gradient(135deg, #00BCD4 0%, #26C6DA 100%);
								}

								&.platinum {
									background: linear-gradient(135deg, #9E9E9E 0%, #BDBDBD 100%);
								}

								&.black-gold {
									background: linear-gradient(135deg, #212121 0%, #FFD700 100%);
								}
							}

							.level-info {
								flex: 1;
								margin-right: 16rpx;

								.level-name {
									font-size: 28rpx;
									font-weight: 600;
									color: #333;
									display: block;
									margin-bottom: 4rpx;
								}

								.level-price {
									font-size: 24rpx;
									color: #FFD700;
									font-weight: 500;
								}
							}

							.level-desc {
								font-size: 22rpx;
								color: #666;
								flex: 2;
							}
						}
					}

					.privilege-categories {
						.category-item {
							margin-bottom: 24rpx;

							&:last-child {
								margin-bottom: 0;
							}

							.category-title {
								font-size: 28rpx;
								font-weight: 600;
								color: #FFD700;
								margin-bottom: 12rpx;
								display: block;
							}

							.privilege-list {
								background: #f8f9fa;
								border-radius: 12rpx;
								padding: 16rpx;

								.privilege-item {
									font-size: 24rpx;
									color: #555;
									line-height: 1.6;
									margin-bottom: 8rpx;
									display: block;

									&:last-child {
										margin-bottom: 0;
									}
								}
							}
						}
					}

					.rule-list {
						.rule-item {
							font-size: 26rpx;
							color: #555;
							line-height: 1.6;
							margin-bottom: 12rpx;
							padding-left: 24rpx;
							position: relative;
							display: block;

							&:before {
								content: '•';
								position: absolute;
								left: 0;
								color: #FFD700;
								font-weight: 700;
							}

							&:last-child {
								margin-bottom: 0;
							}
						}
					}
				}
			}
		}
	}

// 动画定义
@keyframes float {
	0%, 100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-15px);
	}
}

@keyframes sparkle {
	0%, 100% {
		opacity: 0;
		transform: scale(0);
	}
	50% {
		opacity: 1;
		transform: scale(1);
	}
}
</style>
