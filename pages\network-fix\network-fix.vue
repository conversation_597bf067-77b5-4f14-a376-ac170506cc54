<template>
	<view class="network-fix-page">
		<!-- 导航栏 -->
		<view class="nav-bar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="nav-left" @click="goBack">
				<image src="/static/icons/fanhui.png" class="nav-icon" mode="aspectFit"></image>
			</view>
			<text class="nav-title">网络修复工具</text>
		</view>

		<!-- 主要内容 -->
		<view class="fix-content">
			<!-- 状态显示 -->
			<view class="status-section">
				<view class="status-card" :class="{ success: networkStatus.isConnected, error: !networkStatus.isConnected }">
					<view class="status-icon">
						<text class="icon-text">{{ networkStatus.isConnected ? '✓' : '✗' }}</text>
					</view>
					<view class="status-info">
						<text class="status-title">网络状态</text>
						<text class="status-desc">{{ networkStatus.isConnected ? '已连接' : '未连接' }}</text>
						<text class="status-detail">{{ networkStatus.networkType || '未知' }}</text>
					</view>
				</view>
			</view>

			<!-- 诊断结果 -->
			<view class="diagnosis-section" v-if="diagnosisResult">
				<view class="section-title">
					<text class="title-text">诊断结果</text>
					<text class="title-time">{{ formatTime(diagnosisResult.timestamp) }}</text>
				</view>
				
				<view class="diagnosis-list">
					<view class="diagnosis-item" v-for="(test, key) in diagnosisResult.tests" :key="key">
						<view class="item-icon" :class="{ success: test.success, error: !test.success }">
							<text class="icon-text">{{ test.success ? '✓' : '✗' }}</text>
						</view>
						<view class="item-info">
							<text class="item-title">{{ getTestName(key) }}</text>
							<text class="item-desc">{{ test.message || (test.success ? '正常' : '异常') }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="action-section">
				<view class="action-btn primary" @click="runDiagnosis" :class="{ loading: isRunning }">
					<text class="btn-text">{{ isRunning ? '诊断中...' : '开始诊断' }}</text>
				</view>
				
				<view class="action-btn secondary" @click="autoFix" :class="{ loading: isFixing }">
					<text class="btn-text">{{ isFixing ? '修复中...' : '自动修复' }}</text>
				</view>
				
				<view class="action-btn outline" @click="clearCache">
					<text class="btn-text">清理缓存</text>
				</view>
				
				<view class="action-btn outline" @click="runCompleteTest" :class="{ loading: isTesting }">
					<text class="btn-text">{{ isTesting ? '测试中...' : '完整测试' }}</text>
				</view>

				<view class="action-btn outline" @click="showDeployGuide">
					<text class="btn-text">部署指南</text>
				</view>
			</view>

			<!-- 详细信息 -->
			<view class="detail-section" v-if="diagnosisResult">
				<view class="section-title">
					<text class="title-text">详细信息</text>
				</view>
				
				<view class="detail-content">
					<text class="detail-text">{{ JSON.stringify(diagnosisResult, null, 2) }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		comprehensiveNetworkDiagnosis,
		autoFixNetworkIssues,
		checkNetworkStatus
	} from '@/utils/networkTest.js'
	import {
		runCompleteTest as runFinalTest,
		showTestResults
	} from '@/utils/finalTest.js'

	export default {
		data() {
			return {
				statusBarHeight: 0,
				networkStatus: {
					isConnected: false,
					networkType: 'unknown'
				},
				diagnosisResult: null,
				isRunning: false,
				isFixing: false,
				isTesting: false
			}
		},
		
		onLoad() {
			this.getSystemInfo()
			this.checkNetworkStatus()
		},
		
		methods: {
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync()
				this.statusBarHeight = systemInfo.statusBarHeight || 0
			},
			
			async checkNetworkStatus() {
				try {
					const status = await checkNetworkStatus()
					this.networkStatus = status
				} catch (error) {
					console.error('检查网络状态失败:', error)
				}
			},
			
			async runDiagnosis() {
				if (this.isRunning) return
				
				this.isRunning = true
				try {
					uni.showLoading({
						title: '诊断中...'
					})
					
					const result = await comprehensiveNetworkDiagnosis()
					this.diagnosisResult = result
					
					uni.hideLoading()
					
					// 更新网络状态
					await this.checkNetworkStatus()
					
					// 显示结果提示
					const { analysis } = result
					let title = '诊断完成'
					let icon = 'success'
					
					if (analysis.overall === 'critical') {
						title = '发现严重问题'
						icon = 'error'
					} else if (analysis.overall === 'warning') {
						title = '发现一些问题'
						icon = 'none'
					}
					
					uni.showToast({
						title: title,
						icon: icon,
						duration: 2000
					})
					
				} catch (error) {
					uni.hideLoading()
					console.error('诊断失败:', error)
					uni.showToast({
						title: '诊断失败',
						icon: 'error'
					})
				} finally {
					this.isRunning = false
				}
			},
			
			async autoFix() {
				if (this.isFixing) return
				
				this.isFixing = true
				try {
					uni.showLoading({
						title: '修复中...'
					})
					
					const result = await autoFixNetworkIssues()
					
					uni.hideLoading()
					
					if (result.success) {
						uni.showModal({
							title: '修复完成',
							content: `已完成以下修复：\n${result.fixes.join('\n')}\n\n${result.message}`,
							showCancel: true,
							cancelText: '稍后重启',
							confirmText: '立即重启',
							success: (res) => {
								if (res.confirm) {
									uni.reLaunch({
										url: '/pages/auth/auth'
									})
								}
							}
						})
					} else {
						uni.showToast({
							title: result.message,
							icon: 'none',
							duration: 3000
						})
					}
					
				} catch (error) {
					uni.hideLoading()
					console.error('自动修复失败:', error)
					uni.showToast({
						title: '修复失败',
						icon: 'error'
					})
				} finally {
					this.isFixing = false
				}
			},
			
			clearCache() {
				uni.showModal({
					title: '清理缓存',
					content: '确定要清理所有本地缓存吗？这将清除登录状态和用户数据。',
					success: (res) => {
						if (res.confirm) {
							try {
								uni.clearStorageSync()
								uni.showToast({
									title: '缓存已清理',
									icon: 'success'
								})
							} catch (error) {
								uni.showToast({
									title: '清理失败',
									icon: 'error'
								})
							}
						}
					}
				})
			},
			
			async runCompleteTest() {
				if (this.isTesting) return

				this.isTesting = true
				try {
					uni.showLoading({
						title: '执行完整测试...'
					})

					const results = await runFinalTest()

					uni.hideLoading()
					showTestResults(results)

					// 如果测试通过，更新诊断结果
					if (results.overall) {
						await this.runDiagnosis()
					}

				} catch (error) {
					uni.hideLoading()
					console.error('完整测试失败:', error)
					uni.showToast({
						title: '测试失败',
						icon: 'error'
					})
				} finally {
					this.isTesting = false
				}
			},

			showDeployGuide() {
				uni.showModal({
					title: '部署指南',
					content: '1. 在HBuilderX中右键uniCloud-aliyun/cloudfunctions\n2. 选择"上传所有云函数"\n3. 右键uniCloud-aliyun/database\n4. 选择"初始化云数据库"\n5. 等待部署完成后重新测试',
					showCancel: false,
					confirmText: '知道了'
				})
			},
			
			getTestName(key) {
				const names = {
					networkStatus: '网络连接',
					basicConnection: '基础连接',
					uniCloudConnection: 'uniCloud服务',
					databaseConnection: '数据库连接',
					cloudFunctionDeployment: '云函数部署'
				}
				return names[key] || key
			},
			
			formatTime(timestamp) {
				if (!timestamp) return ''
				const date = new Date(timestamp)
				return date.toLocaleTimeString()
			},
			
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss" scoped>
.network-fix-page {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.nav-bar {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 32rpx;
	position: relative;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-icon {
	width: 40rpx;
	height: 40rpx;
}

.nav-title {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	font-size: 36rpx;
	font-weight: 600;
	color: white;
}

.fix-content {
	padding: 32rpx;
}

.status-section {
	margin-bottom: 40rpx;
}

.status-card {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 24rpx;
	padding: 32rpx;
	display: flex;
	align-items: center;
	backdrop-filter: blur(10rpx);
	border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.status-card.success {
	border-color: rgba(76, 175, 80, 0.5);
	background: rgba(76, 175, 80, 0.1);
}

.status-card.error {
	border-color: rgba(244, 67, 54, 0.5);
	background: rgba(244, 67, 54, 0.1);
}

.status-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.icon-text {
	font-size: 32rpx;
	font-weight: bold;
	color: white;
}

.status-info {
	flex: 1;
}

.status-title {
	font-size: 32rpx;
	font-weight: 600;
	color: white;
	display: block;
	margin-bottom: 8rpx;
}

.status-desc {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
	margin-bottom: 4rpx;
}

.status-detail {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.6);
	display: block;
}

.section-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: white;
}

.title-time {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.6);
}

.diagnosis-list {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 24rpx;
	padding: 24rpx;
	backdrop-filter: blur(10rpx);
}

.diagnosis-item {
	display: flex;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.diagnosis-item:last-child {
	border-bottom: none;
}

.item-icon {
	width: 48rpx;
	height: 48rpx;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	background: rgba(255, 255, 255, 0.2);
}

.item-icon.success {
	background: rgba(76, 175, 80, 0.3);
}

.item-icon.error {
	background: rgba(244, 67, 54, 0.3);
}

.item-icon .icon-text {
	font-size: 24rpx;
}

.item-info {
	flex: 1;
}

.item-title {
	font-size: 28rpx;
	color: white;
	display: block;
	margin-bottom: 4rpx;
}

.item-desc {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.7);
	display: block;
}

.action-section {
	margin: 40rpx 0;
}

.action-btn {
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 24rpx;
	transition: all 0.3s ease;
}

.action-btn.primary {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);
}

.action-btn.secondary {
	background: linear-gradient(135deg, #FF9800, #F57C00);
	box-shadow: 0 8rpx 24rpx rgba(255, 152, 0, 0.3);
}

.action-btn.outline {
	background: rgba(255, 255, 255, 0.1);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	backdrop-filter: blur(10rpx);
}

.action-btn:active {
	transform: scale(0.95);
}

.action-btn.loading {
	opacity: 0.7;
}

.btn-text {
	font-size: 32rpx;
	font-weight: 600;
	color: white;
}

.detail-section {
	margin-top: 40rpx;
}

.detail-content {
	background: rgba(0, 0, 0, 0.3);
	border-radius: 16rpx;
	padding: 24rpx;
	backdrop-filter: blur(10rpx);
}

.detail-text {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	font-family: monospace;
	line-height: 1.5;
}
</style>
