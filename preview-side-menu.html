<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲伴 - 右侧半页弹窗预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .demo-container {
            padding: 20px;
            text-align: center;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(255, 193, 7, 0.3);
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
        }
        
        .side-menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.4);
            z-index: 1000;
            display: flex;
            justify-content: flex-end;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .side-menu-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        
        .side-menu-panel {
            width: 70%;
            height: 100%;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            transform: translateX(100%);
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            overflow-y: auto;
            box-shadow: -4px 0 16px rgba(0, 0, 0, 0.15);
        }
        
        .side-menu-overlay.show .side-menu-panel {
            transform: translateX(0);
        }
        
        .drag-indicator {
            width: 30px;
            height: 3px;
            background: #e0e0e0;
            border-radius: 2px;
            margin: 8px auto 0;
        }
        
        .side-menu-header {
            position: relative;
            padding: 20px 16px 16px;
            background:
                radial-gradient(ellipse 200px 150px at 30% 20%, rgba(255, 248, 220, 0.7) 0%, transparent 60%),
                radial-gradient(ellipse 150px 200px at 70% 80%, rgba(245, 222, 179, 0.5) 0%, transparent 60%),
                linear-gradient(135deg, #FFF8DC 0%, #F5DEB3 50%, #DEB887 100%);
            overflow: hidden;
        }
        
        .header-content {
            position: relative;
            z-index: 2;
        }
        
        .menu-title {
            font-size: 20px;
            font-weight: 700;
            color: #fff;
            display: block;
            margin-bottom: 4px;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        }
        
        .menu-subtitle {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
        
        .header-decoration {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 60px;
        }
        
        .decoration-dot {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
        }
        
        .dot-1 {
            width: 20px;
            height: 20px;
            top: 15px;
            right: 10px;
            animation: float 4s ease-in-out infinite;
        }
        
        .dot-2 {
            width: 12px;
            height: 12px;
            top: 40px;
            right: 25px;
            animation: float 6s ease-in-out infinite reverse;
        }
        
        .dot-3 {
            width: 8px;
            height: 8px;
            top: 60px;
            right: 15px;
            animation: float 5s ease-in-out infinite;
        }
        
        .side-menu-content {
            padding: 12px 0;
        }
        
        .menu-section {
            margin-bottom: 16px;
        }
        
        .section-title {
            font-size: 12px;
            color: #999;
            font-weight: 600;
            padding: 0 16px 8px;
            display: block;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }
        
        .menu-item:active {
            background: rgba(255, 215, 0, 0.1);
            transform: translateX(4px);
        }
        
        .item-icon {
            width: 28px;
            height: 28px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            color: white;
            font-size: 14px;
        }
        
        .agreement { background: linear-gradient(135deg, #4ECDC4 0%, #6FE7DD 100%); }
        .rules { background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%); }
        .creator { background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%); }
        .guild { background: linear-gradient(135deg, #9C27B0 0%, #BB6BD9 100%); }
        .service { background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%); }
        .companion { background: linear-gradient(135deg, #FF9800 0%, #FFB74D 100%); }
        .about { background: linear-gradient(135deg, #795548 0%, #8D6E63 100%); }
        
        .item-info {
            flex: 1;
            min-width: 0;
        }
        
        .item-title {
            font-size: 15px;
            color: #333;
            font-weight: 600;
            display: block;
            margin-bottom: 2px;
        }
        
        .item-desc {
            font-size: 12px;
            color: #999;
            line-height: 1.3;
        }
        
        .item-arrow {
            margin-left: 8px;
            transition: all 0.3s ease;
            color: #ccc;
        }
        
        .menu-item:active .item-arrow {
            transform: translateX(2px);
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-5px);
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h2>🎉 右侧半页弹窗效果预览</h2>
        <p style="margin: 10px 0; color: #666;">点击下方按钮体验类似抖音的右侧弹窗效果</p>
        <button class="demo-button" onclick="showSideMenu()">打开更多功能</button>
    </div>
    
    <div class="side-menu-overlay" id="sideMenuOverlay" onclick="hideSideMenu()">
        <div class="side-menu-panel" onclick="event.stopPropagation()">
            <div class="drag-indicator"></div>
            
            <div class="side-menu-header">
                <div class="header-content">
                    <span class="menu-title">更多功能</span>
                    <span class="menu-subtitle">发现更多精彩</span>
                </div>
                <div class="header-decoration">
                    <div class="decoration-dot dot-1"></div>
                    <div class="decoration-dot dot-2"></div>
                    <div class="decoration-dot dot-3"></div>
                </div>
            </div>
            
            <div class="side-menu-content">
                <div class="menu-section">
                    <span class="section-title">创作与社交</span>
                    <div class="menu-item">
                        <div class="item-icon creator">⭐</div>
                        <div class="item-info">
                            <span class="item-title">创作者中心</span>
                            <span class="item-desc">管理创作内容与收益</span>
                        </div>
                        <div class="item-arrow">›</div>
                    </div>
                    <div class="menu-item">
                        <div class="item-icon guild">🏠</div>
                        <div class="item-info">
                            <span class="item-title">我的公会</span>
                            <span class="item-desc">加入兴趣社群</span>
                        </div>
                        <div class="item-arrow">›</div>
                    </div>
                    <div class="menu-item">
                        <div class="item-icon companion">👤</div>
                        <div class="item-info">
                            <span class="item-title">成为玩伴</span>
                            <span class="item-desc">开启陪伴之旅</span>
                        </div>
                        <div class="item-arrow">›</div>
                    </div>
                </div>

                <div class="menu-section">
                    <span class="section-title">帮助与支持</span>
                    <div class="menu-item">
                        <div class="item-icon service">❓</div>
                        <div class="item-info">
                            <span class="item-title">客服中心</span>
                            <span class="item-desc">获取帮助与支持</span>
                        </div>
                        <div class="item-arrow">›</div>
                    </div>
                    <div class="menu-item">
                        <div class="item-icon about">ℹ️</div>
                        <div class="item-info">
                            <span class="item-title">关于闲伴</span>
                            <span class="item-desc">了解我们的故事</span>
                        </div>
                        <div class="item-arrow">›</div>
                    </div>
                </div>

                <div class="menu-section">
                    <span class="section-title">条款与规则</span>
                    <div class="menu-item">
                        <div class="item-icon rules">📋</div>
                        <div class="item-info">
                            <span class="item-title">规则中心</span>
                            <span class="item-desc">社区规范与指南</span>
                        </div>
                        <div class="item-arrow">›</div>
                    </div>
                    <div class="menu-item">
                        <div class="item-icon agreement">📄</div>
                        <div class="item-info">
                            <span class="item-title">用户协议</span>
                            <span class="item-desc">查看使用条款</span>
                        </div>
                        <div class="item-arrow">›</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showSideMenu() {
            document.getElementById('sideMenuOverlay').classList.add('show');
        }
        
        function hideSideMenu() {
            document.getElementById('sideMenuOverlay').classList.remove('show');
        }
    </script>
</body>
</html>
