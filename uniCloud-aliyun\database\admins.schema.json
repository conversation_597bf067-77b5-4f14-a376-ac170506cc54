{"bsonType": "object", "description": "管理员表", "required": ["employee_id", "name", "role"], "properties": {"_id": {"description": "管理员ID"}, "employee_id": {"bsonType": "string", "description": "员工ID", "unique": true}, "name": {"bsonType": "string", "description": "姓名"}, "department": {"bsonType": "string", "description": "部门"}, "role": {"bsonType": "string", "description": "角色", "enum": ["super_admin", "admin", "customer_service"]}, "permissions": {"bsonType": "object", "description": "权限配置"}, "status": {"bsonType": "int", "description": "状态", "default": 1}, "created_at": {"bsonType": "timestamp", "description": "创建时间"}}}