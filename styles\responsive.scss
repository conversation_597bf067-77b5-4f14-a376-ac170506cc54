/**
 * 响应式样式工具类
 * 解决不同屏幕尺寸的适配问题
 */

// 屏幕尺寸断点
$breakpoint-small: 320px;   // 小屏设备
$breakpoint-medium: 375px;  // 中等屏幕 (iPhone 6/7/8)
$breakpoint-large: 414px;   // 大屏设备 (iPhone 6/7/8 Plus)
$breakpoint-xl: 480px;      // 超大屏设备

// 响应式容器
@mixin responsive-container($padding: 24rpx) {
	padding-left: $padding;
	padding-right: $padding;
	max-width: 100%;
	box-sizing: border-box;
	
	// 小屏适配
	@media (max-width: $breakpoint-small) {
		padding-left: calc(#{$padding} * 0.8);
		padding-right: calc(#{$padding} * 0.8);
	}
	
	// 大屏适配
	@media (min-width: $breakpoint-large) {
		padding-left: calc(#{$padding} * 1.2);
		padding-right: calc(#{$padding} * 1.2);
	}
}

// 响应式字体大小
@mixin responsive-font($base-size: 28rpx) {
	font-size: $base-size;
	
	// 小屏适配
	@media (max-width: $breakpoint-small) {
		font-size: calc(#{$base-size} * 0.9);
	}
	
	// 大屏适配
	@media (min-width: $breakpoint-large) {
		font-size: calc(#{$base-size} * 1.1);
	}
}

// 响应式间距
@mixin responsive-spacing($property: margin, $size: 16rpx) {
	#{$property}: $size;
	
	// 小屏适配
	@media (max-width: $breakpoint-small) {
		#{$property}: calc(#{$size} * 0.8);
	}
	
	// 大屏适配
	@media (min-width: $breakpoint-large) {
		#{$property}: calc(#{$size} * 1.2);
	}
}

// 响应式卡片
@mixin responsive-card($padding: 24rpx, $margin: 16rpx, $radius: 16rpx) {
	background: #fff;
	border-radius: $radius;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
	
	@include responsive-spacing(margin, $margin);
	@include responsive-spacing(padding, $padding);
	
	// 小屏适配
	@media (max-width: $breakpoint-small) {
		border-radius: calc(#{$radius} * 0.8);
	}
}

// 响应式按钮
@mixin responsive-button($padding: 16rpx 32rpx, $font-size: 28rpx, $radius: 24rpx) {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: $radius;
	transition: all 0.3s ease;
	
	@include responsive-spacing(padding, $padding);
	@include responsive-font($font-size);
	
	&:active {
		transform: scale(0.98);
	}
	
	// 小屏适配
	@media (max-width: $breakpoint-small) {
		border-radius: calc(#{$radius} * 0.8);
	}
}

// 响应式网格
@mixin responsive-grid($columns: 2, $gap: 16rpx) {
	display: flex;
	flex-wrap: wrap;
	gap: $gap;
	
	> * {
		width: calc((100% - #{$gap} * (#{$columns} - 1)) / #{$columns});
	}
	
	// 小屏适配 - 减少列数
	@media (max-width: $breakpoint-small) {
		gap: calc(#{$gap} * 0.8);
		
		@if $columns > 2 {
			> * {
				width: calc((100% - #{$gap} * 0.8) / 2);
			}
		}
	}
	
	// 大屏适配 - 增加列数
	@media (min-width: $breakpoint-xl) {
		@if $columns < 4 {
			> * {
				width: calc((100% - #{$gap} * 2) / 3);
			}
		}
	}
}

// 响应式图片
@mixin responsive-image($width: 100%, $height: auto, $radius: 8rpx) {
	width: $width;
	height: $height;
	border-radius: $radius;
	object-fit: cover;
	
	// 小屏适配
	@media (max-width: $breakpoint-small) {
		border-radius: calc(#{$radius} * 0.8);
	}
}

// 安全区域适配
@mixin safe-area-padding($position: bottom, $fallback: 0) {
	@if $position == top {
		padding-top: $fallback;
		padding-top: constant(safe-area-inset-top);
		padding-top: env(safe-area-inset-top);
	} @else if $position == bottom {
		padding-bottom: $fallback;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	} @else if $position == left {
		padding-left: $fallback;
		padding-left: constant(safe-area-inset-left);
		padding-left: env(safe-area-inset-left);
	} @else if $position == right {
		padding-right: $fallback;
		padding-right: constant(safe-area-inset-right);
		padding-right: env(safe-area-inset-right);
	}
}

// 状态栏高度适配
@mixin status-bar-height($fallback: 20px) {
	height: $fallback;
	height: constant(status-bar-height);
	height: env(status-bar-height);
}

// 导航栏高度适配
@mixin nav-bar-height($fallback: 44px) {
	height: $fallback;
	
	// Android 适配
	@media (platform: android) {
		height: 48px;
	}
}

// 工具类
.responsive-container {
	@include responsive-container();
}

.responsive-card {
	@include responsive-card();
}

.responsive-grid-2 {
	@include responsive-grid(2);
}

.responsive-grid-3 {
	@include responsive-grid(3);
}

.responsive-grid-4 {
	@include responsive-grid(4);
}

// 字体大小工具类
.text-xs {
	@include responsive-font(20rpx);
}

.text-sm {
	@include responsive-font(24rpx);
}

.text-base {
	@include responsive-font(28rpx);
}

.text-lg {
	@include responsive-font(32rpx);
}

.text-xl {
	@include responsive-font(36rpx);
}

.text-2xl {
	@include responsive-font(48rpx);
}

// 间距工具类
.m-xs {
	@include responsive-spacing(margin, 8rpx);
}

.m-sm {
	@include responsive-spacing(margin, 12rpx);
}

.m-base {
	@include responsive-spacing(margin, 16rpx);
}

.m-lg {
	@include responsive-spacing(margin, 24rpx);
}

.m-xl {
	@include responsive-spacing(margin, 32rpx);
}

.p-xs {
	@include responsive-spacing(padding, 8rpx);
}

.p-sm {
	@include responsive-spacing(padding, 12rpx);
}

.p-base {
	@include responsive-spacing(padding, 16rpx);
}

.p-lg {
	@include responsive-spacing(padding, 24rpx);
}

.p-xl {
	@include responsive-spacing(padding, 32rpx);
}

// 安全区域工具类
.safe-area-top {
	@include safe-area-padding(top, 20px);
}

.safe-area-bottom {
	@include safe-area-padding(bottom, 0);
}

.safe-area-left {
	@include safe-area-padding(left, 0);
}

.safe-area-right {
	@include safe-area-padding(right, 0);
}

// 页面容器
.page-container {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background: #f5f5f5;
	
	@include safe-area-padding(bottom, 0);
}

// 头部容器
.header-container {
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	
	.nav-bar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;
		
		@include responsive-container();
		@include nav-bar-height();
		
		.nav-left, .nav-right {
			flex: 1;
			display: flex;
			align-items: center;
		}
		
		.nav-right {
			justify-content: flex-end;
		}
		
		.nav-title {
			flex: 2;
			text-align: center;
			
			@include responsive-font(32rpx);
			color: #333;
			font-weight: 600;
		}
	}
}

// 内容容器
.content-container {
	flex: 1;
	
	@include responsive-container();
}

// 底部容器
.footer-container {
	@include safe-area-padding(bottom, 0);
}
