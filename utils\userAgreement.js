/**
 * 用户协议状态管理工具
 */

// 存储键名常量
const STORAGE_KEYS = {
	HAS_AGREED_TO_TERMS: 'hasAgreedToTerms',
	HAS_AGREED_TO_PERSONALIZED: 'hasAgreedToPersonalized',
	AGREEMENT_TIME: 'agreementTime',
	PERSONALIZED_AGREEMENT_TIME: 'personalizedAgreementTime',
	USER_TOKEN: 'userToken',
	USER_INFO: 'userInfo'
};

/**
 * 检查用户是否已同意协议
 * @returns {boolean} 是否已同意用户协议
 */
export function hasAgreedToTerms() {
	try {
		return !!uni.getStorageSync(STORAGE_KEYS.HAS_AGREED_TO_TERMS);
	} catch (error) {
		console.error('检查用户协议状态失败:', error);
		return false;
	}
}

/**
 * 检查用户是否已同意个性化服务
 * @returns {boolean} 是否已同意个性化服务
 */
export function hasAgreedToPersonalized() {
	try {
		return !!uni.getStorageSync(STORAGE_KEYS.HAS_AGREED_TO_PERSONALIZED);
	} catch (error) {
		console.error('检查个性化服务状态失败:', error);
		return false;
	}
}

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function isUserLoggedIn() {
	try {
		const userToken = uni.getStorageSync(STORAGE_KEYS.USER_TOKEN);
		const userInfo = uni.getStorageSync(STORAGE_KEYS.USER_INFO);
		return !!(userToken || userInfo);
	} catch (error) {
		console.error('检查用户登录状态失败:', error);
		return false;
	}
}

/**
 * 检查是否需要显示用户协议
 * @returns {boolean} 是否需要显示用户协议
 */
export function shouldShowUserAgreement() {
	// 如果用户已经同意过协议且已登录，则不需要显示
	if (hasAgreedToTerms() && hasAgreedToPersonalized() && isUserLoggedIn()) {
		return false;
	}
	return true;
}

/**
 * 保存用户同意协议的状态
 */
export function saveAgreementStatus() {
	try {
		uni.setStorageSync(STORAGE_KEYS.HAS_AGREED_TO_TERMS, true);
		uni.setStorageSync(STORAGE_KEYS.AGREEMENT_TIME, new Date().getTime());
		return true;
	} catch (error) {
		console.error('保存用户协议状态失败:', error);
		return false;
	}
}

/**
 * 保存用户同意个性化服务的状态
 */
export function savePersonalizedStatus() {
	try {
		uni.setStorageSync(STORAGE_KEYS.HAS_AGREED_TO_PERSONALIZED, true);
		uni.setStorageSync(STORAGE_KEYS.PERSONALIZED_AGREEMENT_TIME, new Date().getTime());
		return true;
	} catch (error) {
		console.error('保存个性化服务状态失败:', error);
		return false;
	}
}

/**
 * 清除用户协议状态（用于账号退出时调用）
 */
export function clearAgreementStatus() {
	try {
		uni.removeStorageSync(STORAGE_KEYS.HAS_AGREED_TO_TERMS);
		uni.removeStorageSync(STORAGE_KEYS.HAS_AGREED_TO_PERSONALIZED);
		uni.removeStorageSync(STORAGE_KEYS.AGREEMENT_TIME);
		uni.removeStorageSync(STORAGE_KEYS.PERSONALIZED_AGREEMENT_TIME);
		return true;
	} catch (error) {
		console.error('清除协议状态失败:', error);
		return false;
	}
}

/**
 * 清除所有用户相关数据（完整退出登录）
 */
export function clearAllUserData() {
	try {
		// 清除协议状态
		clearAgreementStatus();
		// 清除用户登录信息
		uni.removeStorageSync(STORAGE_KEYS.USER_TOKEN);
		uni.removeStorageSync(STORAGE_KEYS.USER_INFO);
		// 可以在这里添加其他需要清除的用户数据
		return true;
	} catch (error) {
		console.error('清除用户数据失败:', error);
		return false;
	}
}

/**
 * 获取协议同意时间
 * @returns {number|null} 同意时间戳
 */
export function getAgreementTime() {
	try {
		return uni.getStorageSync(STORAGE_KEYS.AGREEMENT_TIME) || null;
	} catch (error) {
		console.error('获取协议时间失败:', error);
		return null;
	}
}

/**
 * 获取个性化服务同意时间
 * @returns {number|null} 同意时间戳
 */
export function getPersonalizedAgreementTime() {
	try {
		return uni.getStorageSync(STORAGE_KEYS.PERSONALIZED_AGREEMENT_TIME) || null;
	} catch (error) {
		console.error('获取个性化服务时间失败:', error);
		return null;
	}
}

export default {
	hasAgreedToTerms,
	hasAgreedToPersonalized,
	isUserLoggedIn,
	shouldShowUserAgreement,
	saveAgreementStatus,
	savePersonalizedStatus,
	clearAgreementStatus,
	clearAllUserData,
	getAgreementTime,
	getPersonalizedAgreementTime
};
