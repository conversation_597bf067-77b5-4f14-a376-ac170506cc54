<template>
	<view class="level-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="nav-left" @click="goBack">
				<uni-icons type="left" size="20" color="#fff"></uni-icons>
			</view>
			<view class="nav-title">等级中心</view>
			<view class="nav-right"></view>
		</view>

		<!-- 当前等级卡片 -->
		<view class="current-level-card">
			<view class="level-icon">
				<uni-icons type="medal-filled" size="60" color="#FFD700"></uni-icons>
			</view>
			<view class="level-info">
				<text class="level-title">Lv.{{ currentLevel }}</text>
				<text class="level-name">{{ levelName }}</text>
				<view class="progress-section">
					<view class="progress-bar">
						<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
					</view>
					<text class="progress-text">{{ currentExp }}/{{ nextLevelExp }} 经验值</text>
				</view>
			</view>
		</view>

		<!-- 等级特权 -->
		<view class="privileges-section">
			<view class="section-title">
				<uni-icons type="star-filled" size="18" color="#FFD700"></uni-icons>
				<text>当前特权</text>
			</view>
			<view class="privileges-grid">
				<view class="privilege-item" v-for="privilege in currentPrivileges" :key="privilege.id">
					<uni-icons :type="privilege.icon" size="24" :color="privilege.color"></uni-icons>
					<text class="privilege-text">{{ privilege.name }}</text>
				</view>
			</view>
		</view>

		<!-- 等级列表 -->
		<view class="levels-section">
			<view class="section-title">
				<uni-icons type="list" size="18" color="#2196F3"></uni-icons>
				<text>等级体系</text>
			</view>
			<view class="levels-list">
				<view 
					class="level-item" 
					v-for="level in levelList" 
					:key="level.level"
					:class="{ 'current': level.level === currentLevel, 'unlocked': level.level <= currentLevel }"
				>
					<view class="level-badge">
						<uni-icons type="medal-filled" size="20" :color="level.level <= currentLevel ? '#FFD700' : '#ccc'"></uni-icons>
						<text class="level-number">Lv.{{ level.level }}</text>
					</view>
					<view class="level-details">
						<text class="level-name">{{ level.name }}</text>
						<text class="level-requirement">需要 {{ level.requiredExp }} 经验值</text>
						<view class="level-privileges">
							<text class="privilege-tag" v-for="privilege in level.privileges" :key="privilege">
								{{ privilege }}
							</text>
						</view>
					</view>
					<view class="level-status">
						<uni-icons v-if="level.level < currentLevel" type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
						<uni-icons v-else-if="level.level === currentLevel" type="star-filled" size="16" color="#FFD700"></uni-icons>
						<uni-icons v-else type="locked" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 获取经验值方式 -->
		<view class="exp-ways-section">
			<view class="section-title">
				<uni-icons type="plus-filled" size="18" color="#4CAF50"></uni-icons>
				<text>获取经验值</text>
			</view>
			<view class="exp-ways-list">
				<view class="exp-way-item" v-for="way in expWays" :key="way.id">
					<view class="way-icon">
						<uni-icons :type="way.icon" size="20" :color="way.color"></uni-icons>
					</view>
					<view class="way-content">
						<text class="way-name">{{ way.name }}</text>
						<text class="way-exp">+{{ way.exp }} 经验值</text>
					</view>
					<view class="way-frequency">
						<text class="frequency-text">{{ way.frequency }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0,
			currentLevel: 12,
			currentExp: 2850,
			nextLevelExp: 3000,
			levelList: [
				{
					level: 1,
					name: '新手',
					requiredExp: 0,
					privileges: ['基础功能']
				},
				{
					level: 5,
					name: '活跃用户',
					requiredExp: 500,
					privileges: ['发布动态', '评论互动']
				},
				{
					level: 10,
					name: '资深玩家',
					requiredExp: 1500,
					privileges: ['创建活动', '专属标识']
				},
				{
					level: 15,
					name: '社区达人',
					requiredExp: 3000,
					privileges: ['优先推荐', '专属客服']
				},
				{
					level: 20,
					name: '超级用户',
					requiredExp: 5000,
					privileges: ['内测资格', '专属礼品']
				}
			],
			expWays: [
				{
					id: 1,
					name: '每日签到',
					exp: 10,
					icon: 'calendar',
					color: '#4CAF50',
					frequency: '每日1次'
				},
				{
					id: 2,
					name: '发布动态',
					exp: 20,
					icon: 'compose',
					color: '#2196F3',
					frequency: '每日3次'
				},
				{
					id: 3,
					name: '参与活动',
					exp: 50,
					icon: 'heart-filled',
					color: '#FF5722',
					frequency: '不限'
				},
				{
					id: 4,
					name: '邀请好友',
					exp: 100,
					icon: 'person-add',
					color: '#9C27B0',
					frequency: '不限'
				}
			]
		}
	},
	
	computed: {
		levelName() {
			const level = this.levelList.find(l => l.level <= this.currentLevel);
			return level ? level.name : '新手';
		},
		
		progressPercent() {
			const prevLevel = this.levelList.filter(l => l.level < this.currentLevel).pop();
			const prevExp = prevLevel ? prevLevel.requiredExp : 0;
			const currentLevelExp = this.nextLevelExp - prevExp;
			const currentProgress = this.currentExp - prevExp;
			return Math.min((currentProgress / currentLevelExp) * 100, 100);
		},
		
		currentPrivileges() {
			const level = this.levelList.filter(l => l.level <= this.currentLevel).pop();
			if (!level) return [];
			
			return [
				{ id: 1, name: '发布动态', icon: 'compose', color: '#2196F3' },
				{ id: 2, name: '创建活动', icon: 'plus-filled', color: '#4CAF50' },
				{ id: 3, name: '专属标识', icon: 'star-filled', color: '#FFD700' },
				{ id: 4, name: '优先推荐', icon: 'fire', color: '#FF5722' }
			];
		}
	},
	
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
	},
	
	methods: {
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style lang="scss" scoped>
.level-page {
	min-height: 100vh;
	background: #f5f5f5;
}

.status-bar {
	background: transparent;
}

/* 导航栏 */
.nav-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 40rpx;
	background: #fff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

/* 当前等级卡片 */
.current-level-card {
	margin: 40rpx;
	padding: 60rpx 40rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 32rpx;
	display: flex;
	align-items: center;
	gap: 40rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.level-icon {
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #FFD700, #FFA000);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 16rpx rgba(255, 215, 0, 0.3);
}

.level-info {
	flex: 1;
}

.level-title {
	font-size: 48rpx;
	font-weight: 700;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.level-name {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 24rpx;
}

.progress-section {
	width: 100%;
}

.progress-bar {
	width: 100%;
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 12rpx;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #4CAF50, #8BC34A);
	border-radius: 6rpx;
	transition: width 0.3s;
}

.progress-text {
	font-size: 24rpx;
	color: #666;
}

/* 通用区块样式 */
.privileges-section, .levels-section, .exp-ways-section {
	margin: 0 40rpx 40rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 32rpx;
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

/* 特权网格 */
.privileges-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 24rpx;
}

.privilege-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s;
}

.privilege-item:active {
	border-color: #4CAF50;
	background: rgba(76, 175, 80, 0.05);
}

.privilege-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}

/* 等级列表 */
.levels-list {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

.level-item {
	display: flex;
	align-items: center;
	padding: 32rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s;
}

.level-item.current {
	background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 160, 0, 0.1));
	border-color: #FFD700;
}

.level-item.unlocked {
	background: rgba(76, 175, 80, 0.05);
}

.level-badge {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-right: 24rpx;
}

.level-number {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.level-details {
	flex: 1;
}

.level-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.level-requirement {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 16rpx;
}

.level-privileges {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
}

.privilege-tag {
	padding: 6rpx 12rpx;
	background: rgba(33, 150, 243, 0.1);
	color: #2196F3;
	font-size: 20rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(33, 150, 243, 0.2);
}

.level-status {
	margin-left: 24rpx;
}

/* 经验值获取方式 */
.exp-ways-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.exp-way-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	transition: all 0.3s;
}

.exp-way-item:active {
	background: rgba(76, 175, 80, 0.05);
	transform: scale(0.98);
}

.way-icon {
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.way-content {
	flex: 1;
}

.way-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.way-exp {
	font-size: 24rpx;
	color: #4CAF50;
	font-weight: 600;
}

.way-frequency {
	margin-left: 24rpx;
}

.frequency-text {
	font-size: 22rpx;
	color: #999;
	padding: 8rpx 16rpx;
	background: rgba(0, 0, 0, 0.05);
	border-radius: 12rpx;
}
</style>
