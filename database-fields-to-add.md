# 需要在uniCloud控制台添加的数据库字段

## uni-id-users 表需要添加的字段

请在uniCloud控制台的数据库管理中，找到 `uni-id-users` 表，添加以下字段：

### 1. birthday (生日字段)
```json
{
  "bsonType": "string",
  "description": "生日",
  "maxLength": 20
}
```

### 2. email (邮箱字段) - 如果不存在
```json
{
  "bsonType": "string", 
  "description": "邮箱地址",
  "maxLength": 100
}
```

### 3. email_confirmed (邮箱验证状态) - 如果不存在
```json
{
  "bsonType": "int",
  "description": "邮箱是否已验证，0未验证 1已验证", 
  "enum": [0, 1]
}
```

## 操作步骤

1. 登录 uniCloud 控制台
2. 进入数据库管理
3. 找到 `uni-id-users` 表
4. 点击"表结构"
5. 点击"添加字段"
6. 按照上述配置添加每个字段

## 验证字段是否存在

可以通过以下方式验证字段是否已存在：
- 在数据库管理界面查看表结构
- 或者在云函数中查询一条用户记录，查看返回的字段

## 注意事项

- 如果字段已存在，请跳过该字段的添加
- 添加字段后，新注册的用户会自动包含这些字段
- 现有用户的这些字段值可能为空，需要用户重新完善资料
