<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<script>
			var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
				CSS.supports('top: constant(a)'))
			document.write(
				'<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
				(coverSupport ? ', viewport-fit=cover' : '') + '" />')
		</script>
		<title>趣玩星球</title>
		<meta name="description" content="趣玩星球 - 发现身边有趣的人和事">
		<meta name="keywords" content="社交,交友,附近的人,趣玩星球">

		<!-- FontAwesome 图标库 -->
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

		<!-- 高德地图API -->
		<script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=e318a539d606974c5f13654e905cf555"></script>

		<!--preload-links-->
		<!--app-context-->
	</head>
	<body>
		<div id="app"><!--app-html--></div>
		<script type="module" src="/main"></script>
	</body>
</html>