<template>
	<view class="coming-soon-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<uni-icons type="left" size="24" color="#fff"></uni-icons>
					<text class="back-text">返回</text>
				</view>
				<text class="nav-title">{{ pageTitle }}</text>
				<view class="nav-right"></view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content-area">
			<view class="coming-soon-container">
				<!-- 图标 -->
				<view class="icon-container">
					<uni-icons :type="pageIcon" size="120" color="#667eea"></uni-icons>
				</view>

				<!-- 标题 -->
				<text class="main-title">{{ pageTitle }}</text>
				
				<!-- 描述 -->
				<text class="description">{{ pageDescription }}</text>

				<!-- 功能预览 -->
				<view class="features-preview" v-if="features.length > 0">
					<text class="preview-title">即将上线的功能</text>
					<view class="feature-list">
						<view class="feature-item" v-for="feature in features" :key="feature.id">
							<view class="feature-icon">
								<uni-icons :type="feature.icon" size="20" color="#667eea"></uni-icons>
							</view>
							<text class="feature-text">{{ feature.name }}</text>
						</view>
					</view>
				</view>

				<!-- 进度指示 -->
				<view class="progress-section">
					<text class="progress-title">开发进度</text>
					<view class="progress-bar">
						<view class="progress-fill" :style="{ width: progress + '%' }"></view>
					</view>
					<text class="progress-text">{{ progress }}% 完成</text>
				</view>

				<!-- 操作按钮 -->
				<view class="action-buttons">
					<view class="action-btn primary" @tap="notifyMe">
						<uni-icons type="bell" size="20" color="#fff"></uni-icons>
						<text class="btn-text">上线提醒</text>
					</view>
					<view class="action-btn secondary" @tap="feedback">
						<uni-icons type="chatbubble" size="20" color="#667eea"></uni-icons>
						<text class="btn-text">意见反馈</text>
					</view>
				</view>

				<!-- 预计上线时间 -->
				<view class="timeline">
					<text class="timeline-text">预计上线时间：{{ expectedDate }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				pageTitle: '功能开发中',
				pageIcon: 'gear',
				pageDescription: '我们正在努力开发这个功能，敬请期待！',
				progress: 65,
				expectedDate: '2025年1月',
				features: []
			}
		},

		onLoad(options) {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});

			// 根据传入的参数设置页面信息
			this.setupPageInfo(options.type || 'default');
		},

		methods: {
			goBack() {
				uni.navigateBack();
			},

			setupPageInfo(type) {
				const pageConfigs = {
					'my-guild': {
						title: '我的公会',
						icon: 'star',
						description: '加入公会，与志同道合的伙伴一起成长！',
						progress: 70,
						expectedDate: '2025年1月',
						features: [
							{ id: 1, name: '公会创建与管理', icon: 'plus' },
							{ id: 2, name: '公会活动组织', icon: 'calendar' },
							{ id: 3, name: '成员等级系统', icon: 'medal' },
							{ id: 4, name: '公会任务系统', icon: 'checkmarkempty' }
						]
					},
					'become-companion': {
						title: '成为玩伴',
						icon: 'person-add',
						description: '成为认证玩伴，开启你的陪伴之旅！',
						progress: 80,
						expectedDate: '2024年12月',
						features: [
							{ id: 1, name: '玩伴认证申请', icon: 'checkmarkempty' },
							{ id: 2, name: '技能标签设置', icon: 'pricetag' },
							{ id: 3, name: '服务定价管理', icon: 'card' },
							{ id: 4, name: '订单接单系统', icon: 'list' }
						]
					},
					'customer-service': {
						title: '客服中心',
						icon: 'chatbubble-filled',
						description: '7x24小时在线客服，随时为您解答疑问！',
						progress: 90,
						expectedDate: '2024年12月',
						features: [
							{ id: 1, name: '在线客服聊天', icon: 'chatbubble' },
							{ id: 2, name: '智能问答机器人', icon: 'help' },
							{ id: 3, name: '工单系统', icon: 'compose' },
							{ id: 4, name: '服务评价', icon: 'star' }
						]
					},
					'rules-center': {
						title: '规则中心',
						icon: 'list',
						description: '了解社区规则，共建和谐环境！',
						progress: 85,
						expectedDate: '2024年12月',
						features: [
							{ id: 1, name: '社区行为规范', icon: 'checkmarkempty' },
							{ id: 2, name: '违规处理流程', icon: 'alert' },
							{ id: 3, name: '申诉系统', icon: 'redo' },
							{ id: 4, name: '规则更新通知', icon: 'bell' }
						]
					}
				};

				const config = pageConfigs[type] || pageConfigs['default'] || {
					title: '功能开发中',
					icon: 'gear',
					description: '我们正在努力开发这个功能，敬请期待！',
					progress: 65,
					expectedDate: '2025年1月',
					features: []
				};

				this.pageTitle = config.title;
				this.pageIcon = config.icon;
				this.pageDescription = config.description;
				this.progress = config.progress;
				this.expectedDate = config.expectedDate;
				this.features = config.features || [];
			},

			notifyMe() {
				uni.showModal({
					title: '上线提醒',
					content: '功能上线后我们会第一时间通知您！',
					confirmText: '确定',
					showCancel: false,
					success: () => {
						uni.showToast({
							title: '提醒设置成功',
							icon: 'success'
						});
					}
				});
			},

			feedback() {
				uni.navigateTo({
					url: '/pages/profile-features/help/help'
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.coming-soon-page {
		background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.status-bar {
		background: transparent;
	}

	.header {
		background: transparent;

		.nav-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;
			height: 88rpx;

			.nav-left {
				display: flex;
				align-items: center;
				gap: 8rpx;
				flex: 1;

				.back-text {
					font-size: 28rpx;
					color: #fff;
					font-weight: 500;
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: #fff;
				font-weight: 600;
				text-align: center;
				flex: 2;
			}

			.nav-right {
				flex: 1;
			}
		}
	}

	.content-area {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 40rpx 24rpx;

		.coming-soon-container {
			background: rgba(255, 255, 255, 0.95);
			border-radius: 32rpx;
			padding: 60rpx 40rpx;
			text-align: center;
			max-width: 600rpx;
			width: 100%;
			box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);

			.icon-container {
				margin-bottom: 32rpx;
				opacity: 0.8;
			}

			.main-title {
				font-size: 48rpx;
				color: #333;
				font-weight: 700;
				display: block;
				margin-bottom: 16rpx;
			}

			.description {
				font-size: 28rpx;
				color: #666;
				line-height: 1.6;
				display: block;
				margin-bottom: 40rpx;
			}

			.features-preview {
				margin-bottom: 40rpx;

				.preview-title {
					font-size: 32rpx;
					color: #333;
					font-weight: 600;
					display: block;
					margin-bottom: 24rpx;
				}

				.feature-list {
					display: flex;
					flex-direction: column;
					gap: 16rpx;

					.feature-item {
						display: flex;
						align-items: center;
						justify-content: center;
						gap: 12rpx;
						padding: 16rpx 24rpx;
						background: #f8f9fa;
						border-radius: 24rpx;

						.feature-icon {
							width: 32rpx;
							height: 32rpx;
							display: flex;
							align-items: center;
							justify-content: center;
						}

						.feature-text {
							font-size: 26rpx;
							color: #333;
							font-weight: 500;
						}
					}
				}
			}

			.progress-section {
				margin-bottom: 40rpx;

				.progress-title {
					font-size: 28rpx;
					color: #333;
					font-weight: 600;
					display: block;
					margin-bottom: 16rpx;
				}

				.progress-bar {
					width: 100%;
					height: 12rpx;
					background: #e9ecef;
					border-radius: 6rpx;
					overflow: hidden;
					margin-bottom: 12rpx;

					.progress-fill {
						height: 100%;
						background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
						border-radius: 6rpx;
						transition: width 0.3s ease;
					}
				}

				.progress-text {
					font-size: 24rpx;
					color: #999;
				}
			}

			.action-buttons {
				display: flex;
				gap: 16rpx;
				margin-bottom: 32rpx;

				.action-btn {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 8rpx;
					padding: 20rpx;
					border-radius: 24rpx;
					transition: all 0.3s ease;

					&.primary {
						background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
						color: #fff;
						box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);

						.btn-text {
							color: #fff;
						}
					}

					&.secondary {
						background: #fff;
						border: 2rpx solid #667eea;

						.btn-text {
							color: #667eea;
						}
					}

					&:active {
						transform: scale(0.98);
					}

					.btn-text {
						font-size: 26rpx;
						font-weight: 600;
					}
				}
			}

			.timeline {
				.timeline-text {
					font-size: 24rpx;
					color: #999;
					font-style: italic;
				}
			}
		}
	}
</style>
