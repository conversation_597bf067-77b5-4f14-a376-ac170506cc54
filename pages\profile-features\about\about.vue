<template>
	<view class="about-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<uni-icons type="left" size="24" color="#fff"></uni-icons>
					<text class="back-text">返回</text>
				</view>
				<text class="nav-title">关于闲伴</text>
				<view class="nav-right"></view>
			</view>
			
			<!-- 头部装饰 -->
			<view class="header-decoration">
				<view class="decoration-circle circle-1"></view>
				<view class="decoration-circle circle-2"></view>
				<view class="decoration-wave"></view>
			</view>
		</view>

		<!-- 应用信息 -->
		<view class="app-info">
			<view class="app-logo">
				<image class="logo-image" src="/static/app-logo-new.png" mode="aspectFit"></image>
			</view>
			<text class="app-name">趣嗒同行</text>
			<text class="app-slogan">发现有趣的生活，遇见志同道合的人</text>
			<text class="app-version">版本 v1.0.0</text>
		</view>

		<!-- 关于内容 -->
		<scroll-view class="about-content" scroll-y="true">
			<!-- 应用介绍 -->
			<view class="about-section">
				<text class="section-title">应用介绍</text>
				<view class="intro-card">
					<text class="intro-text">
						趣嗒同行是一款专注于生活社交的移动应用，致力于帮助用户发现身边有趣的活动，结识志同道合的朋友。
						无论是户外运动、美食探店、文化艺术，还是技能学习，都能在这里找到属于你的圈子。
					</text>
					<text class="intro-text">
						我们相信，生活不应该孤单，每个人都值得拥有精彩的社交体验。让我们一起，用趣嗒同行连接美好生活！
					</text>
				</view>
			</view>

			<!-- 核心功能 -->
			<view class="about-section">
				<text class="section-title">核心功能</text>
				<view class="feature-list">
					<view class="feature-item" v-for="feature in features" :key="feature.id">
						<view class="feature-icon" :class="feature.iconClass">
							<uni-icons :type="feature.icon" size="24" color="#fff"></uni-icons>
						</view>
						<view class="feature-info">
							<text class="feature-title">{{ feature.title }}</text>
							<text class="feature-desc">{{ feature.description }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 团队信息 -->
			<view class="about-section">
				<text class="section-title">团队信息</text>
				<view class="team-card">
					<view class="team-item">
						<text class="team-label">开发团队</text>
						<text class="team-value">闲伴科技团队</text>
					</view>
					<view class="team-item">
						<text class="team-label">公司地址</text>
						<text class="team-value">上海市崇明区北沿公路2111号3幢</text>
					</view>
					<view class="team-item">
						<text class="team-label">联系邮箱</text>
						<text class="team-value"><EMAIL></text>
					</view>
					<view class="team-item">
						<text class="team-label">官方网站</text>
						<text class="team-value">www.xianban.com</text>
					</view>
				</view>
			</view>

			<!-- 版本信息 -->
			<view class="about-section">
				<text class="section-title">版本信息</text>
				<view class="version-card">
					<view class="version-item">
						<text class="version-label">当前版本</text>
						<text class="version-value">v1.0.0</text>
					</view>
					<view class="version-item">
						<text class="version-label">发布时间</text>
						<text class="version-value">2024年12月</text>
					</view>
					<view class="version-item">
						<text class="version-label">更新内容</text>
						<text class="version-value">全新发布，开启美好社交生活</text>
					</view>
				</view>
			</view>

			<!-- 法律信息 -->
			<view class="about-section">
				<text class="section-title">法律信息</text>
				<view class="legal-list">
					<view class="legal-item" @tap="goToUserAgreement">
						<text class="legal-title">用户协议</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
					<view class="legal-item" @tap="goToPrivacyPolicy">
						<text class="legal-title">隐私政策</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
					<view class="legal-item" @tap="goToRulesCenter">
						<text class="legal-title">社区规范</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 联系我们 -->
			<view class="about-section">
				<text class="section-title">联系我们</text>
				<view class="contact-actions">
					<view class="action-btn primary" @tap="contactUs">
						<uni-icons type="chatbubble-filled" size="20" color="#fff"></uni-icons>
						<text class="btn-text">联系客服</text>
					</view>
					<view class="action-btn secondary" @tap="rateApp">
						<uni-icons type="star" size="20" color="#667eea"></uni-icons>
						<text class="btn-text">评价应用</text>
					</view>
				</view>
			</view>

			<!-- 版权信息 -->
			<view class="copyright">
				<text class="copyright-text">© 2024 闲伴科技. 保留所有权利.</text>
				<text class="copyright-text">让生活更有趣，让社交更简单</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				
				// 核心功能
				features: [
					{
						id: 1,
						title: '活动发现',
						description: '发现身边有趣的活动和聚会',
						icon: 'calendar',
						iconClass: 'feature-calendar'
					},
					{
						id: 2,
						title: '社交互动',
						description: '结识志同道合的朋友',
						icon: 'person-add',
						iconClass: 'feature-social'
					},
					{
						id: 3,
						title: '内容分享',
						description: '分享生活中的美好时刻',
						icon: 'image',
						iconClass: 'feature-content'
					},
					{
						id: 4,
						title: '兴趣圈子',
						description: '加入感兴趣的主题圈子',
						icon: 'heart',
						iconClass: 'feature-interest'
					}
				]
			}
		},

		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},

		methods: {
			goBack() {
				uni.switchTab({
					url: '/pages/profile/profile'
				});
			},

			goToUserAgreement() {
				uni.navigateTo({
					url: '/pages/user-agreement/user-agreement'
				});
			},

			goToPrivacyPolicy() {
				uni.navigateTo({
					url: '/pages/legal/privacy-policy/privacy-policy'
				});
			},

			goToRulesCenter() {
				uni.navigateTo({
					url: '/pages/rule-center/rule-center'
				});
			},

			contactUs() {
				uni.showActionSheet({
					itemList: ['在线客服', '电话客服', '邮箱反馈'],
					success: (res) => {
						const actions = ['在线客服', '电话客服', '邮箱反馈'];
						uni.showToast({
							title: `${actions[res.tapIndex]}功能开发中`,
							icon: 'none'
						});
					}
				});
			},

			rateApp() {
				uni.showModal({
					title: '评价应用',
					content: '感谢您的使用！请前往应用商店为我们评分，您的支持是我们前进的动力。',
					confirmText: '去评价',
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '应用商店跳转功能开发中',
								icon: 'none'
							});
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.about-page {
		background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.status-bar {
		background: transparent;
	}

	.header {
		position: relative;
		background: transparent;
		overflow: hidden;

		.nav-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;
			height: 88rpx;
			position: relative;
			z-index: 2;

			.nav-left {
				display: flex;
				align-items: center;
				gap: 8rpx;
				flex: 1;

				.back-text {
					font-size: 28rpx;
					color: #fff;
					font-weight: 500;
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: #fff;
				font-weight: 600;
				text-align: center;
				flex: 2;
			}

			.nav-right {
				flex: 1;
			}
		}

		.header-decoration {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			overflow: hidden;

			.decoration-circle {
				position: absolute;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.1);

				&.circle-1 {
					width: 150rpx;
					height: 150rpx;
					top: -50rpx;
					right: -30rpx;
				}

				&.circle-2 {
					width: 100rpx;
					height: 100rpx;
					top: 40rpx;
					left: -20rpx;
				}
			}

			.decoration-wave {
				position: absolute;
				bottom: -20rpx;
				left: 0;
				right: 0;
				height: 40rpx;
				background: rgba(255, 255, 255, 0.1);
				border-radius: 50% 50% 0 0;
			}
		}
	}

	// 应用信息
	.app-info {
		text-align: center;
		padding: 40rpx 24rpx;
		background: transparent;

		.app-logo {
			margin-bottom: 24rpx;

			.logo-image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 24rpx;
				background: rgba(255, 255, 255, 0.1);
			}
		}

		.app-name {
			font-size: 48rpx;
			color: #fff;
			font-weight: 700;
			display: block;
			margin-bottom: 12rpx;
		}

		.app-slogan {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			display: block;
			margin-bottom: 16rpx;
			line-height: 1.4;
		}

		.app-version {
			font-size: 24rpx;
			color: rgba(255, 255, 255, 0.6);
			background: rgba(255, 255, 255, 0.1);
			padding: 8rpx 16rpx;
			border-radius: 16rpx;
			display: inline-block;
		}
	}

	// 关于内容区域
	.about-content {
		flex: 1;
		background: #f5f5f5;
		border-radius: 32rpx 32rpx 0 0;
		padding: 32rpx 24rpx 120rpx;

		.about-section {
			margin-bottom: 32rpx;

			.section-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 700;
				margin-bottom: 16rpx;
				padding-left: 8rpx;
				display: block;
			}

			// 应用介绍
			.intro-card {
				background: #fff;
				border-radius: 16rpx;
				padding: 24rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.intro-text {
					font-size: 28rpx;
					color: #666;
					line-height: 1.6;
					display: block;
					margin-bottom: 16rpx;

					&:last-child {
						margin-bottom: 0;
					}
				}
			}

			// 核心功能
			.feature-list {
				background: #fff;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.feature-item {
					display: flex;
					align-items: center;
					padding: 24rpx;
					border-bottom: 1rpx solid #f5f5f5;

					&:last-child {
						border-bottom: none;
					}

					.feature-icon {
						width: 56rpx;
						height: 56rpx;
						border-radius: 28rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 16rpx;

						&.feature-calendar {
							background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
						}

						&.feature-social {
							background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
						}

						&.feature-content {
							background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
						}

						&.feature-interest {
							background: linear-gradient(135deg, #ffa726 0%, #ffcc02 100%);
						}
					}

					.feature-info {
						flex: 1;

						.feature-title {
							font-size: 30rpx;
							color: #333;
							font-weight: 600;
							display: block;
							margin-bottom: 8rpx;
						}

						.feature-desc {
							font-size: 24rpx;
							color: #999;
							line-height: 1.4;
						}
					}
				}
			}

			// 团队信息
			.team-card {
				background: #fff;
				border-radius: 16rpx;
				padding: 24rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.team-item {
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					margin-bottom: 16rpx;

					&:last-child {
						margin-bottom: 0;
					}

					.team-label {
						font-size: 26rpx;
						color: #999;
						min-width: 120rpx;
					}

					.team-value {
						font-size: 26rpx;
						color: #333;
						flex: 1;
						text-align: right;
						line-height: 1.4;
					}
				}
			}

			// 版本信息
			.version-card {
				background: #fff;
				border-radius: 16rpx;
				padding: 24rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.version-item {
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					margin-bottom: 16rpx;

					&:last-child {
						margin-bottom: 0;
					}

					.version-label {
						font-size: 26rpx;
						color: #999;
						min-width: 120rpx;
					}

					.version-value {
						font-size: 26rpx;
						color: #333;
						flex: 1;
						text-align: right;
						line-height: 1.4;
					}
				}
			}

			// 法律信息
			.legal-list {
				background: #fff;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.legal-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 24rpx;
					border-bottom: 1rpx solid #f5f5f5;
					transition: all 0.3s ease;

					&:last-child {
						border-bottom: none;
					}

					&:active {
						background: #f8f9fa;
					}

					.legal-title {
						font-size: 28rpx;
						color: #333;
						font-weight: 500;
					}
				}
			}

			// 联系我们
			.contact-actions {
				display: flex;
				gap: 16rpx;

				.action-btn {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 8rpx;
					padding: 20rpx;
					border-radius: 24rpx;
					transition: all 0.3s ease;

					&.primary {
						background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
						color: #fff;
						box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);

						.btn-text {
							color: #fff;
						}
					}

					&.secondary {
						background: #fff;
						border: 2rpx solid #667eea;
						box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

						.btn-text {
							color: #667eea;
						}
					}

					&:active {
						transform: scale(0.98);
					}

					.btn-text {
						font-size: 28rpx;
						font-weight: 600;
					}
				}
			}
		}

		// 版权信息
		.copyright {
			text-align: center;
			padding: 32rpx 0;
			margin-top: 32rpx;

			.copyright-text {
				font-size: 22rpx;
				color: #ccc;
				display: block;
				margin-bottom: 8rpx;
				line-height: 1.4;

				&:last-child {
					margin-bottom: 0;
				}
			}
		}
	}
</style>
