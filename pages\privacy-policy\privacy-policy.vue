<template>
	<view class="privacy-container" @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<view class="header">
			<view class="back-button" @tap="goBack">
				<image src="/static/fanhuilogo.png" class="back-icon back-icon-dark" mode="aspectFit"></image>
			</view>
			<text class="title">趣嗒同行隐私政策</text>
		</view>

		<!-- 手势返回指示器 -->
		<view class="gesture-indicator" v-if="showGestureIndicator" :style="{ opacity: gestureOpacity }">
			<image src="/static/fanhuilogo.png" class="gesture-icon" mode="aspectFit"></image>
			<text class="gesture-text">松手返回</text>
		</view>

		<scroll-view class="content" scroll-y>
			<view class="policy-content">
				<!-- 版本信息区域 -->
				<view class="version-section">
					<text class="version-info">版本号：v3.0.0</text>
					<text class="update-time">更新时间：2025年7月1日</text>
					<text class="effective-time">生效时间：2025年7月1日</text>
				</view>

				<view class="related-links">
					<view class="link-item" @tap="goToUserAgreement">
						<text class="link-icon">📋</text>
						<text class="link-text">《趣嗒同行用户协议》完整版</text>
						<text class="link-arrow">›</text>
					</view>
				</view>

				<!-- 引言 -->
				<text class="section-title">引言</text>
				<view class="section-divider"></view>

				<text class="paragraph">趣嗒同行（以下简称"我们"、"本平台"或"趣嗒同行"）是由上海趣嗒网络科技有限公司（注册地址：上海市崇明区北沿公路2111号3幢）开发运营的移动应用程序，专注于组局约伴、城市玩伴和游戏玩伴服务。</text>

				<text class="paragraph">我们深知个人信息对您的重要性，严格遵守《中华人民共和国网络安全法》《中华人民共和国数据安全法》《中华人民共和国个人信息保护法》《中华人民共和国民法典》《信息安全技术 个人信息安全规范》等相关法律法规和国家标准。</text>

				<text class="paragraph">本隐私政策将详细说明我们如何收集、使用、存储、共享、转让、公开披露和保护您的个人信息，以及您享有的相关权利。我们承诺严格按照本政策和相关法律法规处理您的个人信息，并采用业界领先的安全技术和管理措施保护您的信息安全。</text>

				<text class="paragraph">请您在使用我们的服务前仔细阅读并充分理解本政策的全部内容，特别是以粗体标识的条款。如果您不同意本政策的任何内容，请您立即停止使用我们的服务。您使用我们的服务即表示您已阅读、理解并同意本政策的全部内容。</text>

				<view class="section">
					<text class="section-title">一、我们收集的个人信息类型及用途</text>
					<text class="section-content">
						我们严格遵循合法、正当、必要和诚信原则收集您的个人信息。我们仅会为实现产品功能、提升服务质量、保障账户安全等合理目的收集您的个人信息。以下是我们可能收集的个人信息的完整清单：
					</text>

					<text class="sub-title">1.1 账户注册与身份信息</text>
					<view class="item-list">
						<text class="list-item">手机号码：用于账户注册、登录验证、安全验证、找回密码和重要通知推送</text>
						<text class="list-item">密码：用于账户安全保护（采用不可逆加密存储，我们无法获取您的明文密码）</text>
						<text class="list-item">用户名/昵称：用于个人身份标识和社交展示</text>
						<text class="list-item">头像照片：用于个人形象展示和身份识别</text>
						<text class="list-item">性别：用于个性化推荐和匹配服务</text>
						<text class="list-item">年龄/出生日期：用于年龄验证、青少年保护和个性化服务</text>
						<text class="list-item">实名认证信息：姓名、身份证号码（仅在用户主动申请实名认证时收集）</text>
						<text class="list-item">第三方账户信息：当您选择微信等第三方登录时，我们会获取您授权的基本信息</text>
					</view>

					<text class="sub-title">1.2 个人资料与偏好信息</text>
					<view class="item-list">
						<text class="list-item">个人简介：用于个人展示和社交匹配</text>
						<text class="list-item">兴趣爱好：包括露营、钓鱼、BBQ、徒步、夜跑、聚会等活动偏好</text>
						<text class="list-item">职业信息：用于身份展示和匹配推荐</text>
						<text class="list-item">教育背景：用于身份展示和社交匹配</text>
						<text class="list-item">个性标签：用于个性化展示和精准匹配</text>
						<text class="list-item">VIP会员等级：黄金、钻石、白金、黑金会员状态</text>
					</view>

					<text class="sub-title">1.3 位置信息</text>
					<view class="item-list">
						<text class="list-item">精确位置信息：GPS坐标，用于附近的人推荐、活动匹配和位置服务</text>
						<text class="list-item">大概位置信息：基于IP地址的省市信息，用于内容推荐和地域服务</text>
						<text class="list-item">常用地址：用于个性化推荐和便捷服务</text>
						<text class="list-item">IP属地信息：显示用户发布内容时的IP归属地（仅显示省份或直辖市）</text>
					</view>

					<text class="sub-title">1.4 设备与技术信息</text>
					<view class="item-list">
						<text class="list-item">设备标识符：设备ID、IMEI、MAC地址、Android ID、IDFA等</text>
						<text class="list-item">设备信息：设备型号、操作系统版本、屏幕分辨率、设备制造商</text>
						<text class="list-item">网络信息：IP地址、网络类型（WiFi/4G/5G）、运营商信息</text>
						<text class="list-item">应用信息：应用版本号、安装时间、使用时长、崩溃日志</text>
						<text class="list-item">浏览器信息：User-Agent、浏览器类型和版本</text>
					</view>

					<text class="sub-title">1.5 行为与使用信息</text>
					<view class="item-list">
						<text class="list-item">操作日志：点击、浏览、搜索、分享等操作记录</text>
						<text class="list-item">使用偏好：关注的用户、喜欢的内容类型、活动参与记录</text>
						<text class="list-item">互动记录：点赞、评论、收藏、举报等社交行为</text>
						<text class="list-item">在线时长：应用使用时间、活跃时段等统计信息</text>
						<text class="list-item">搜索记录：搜索关键词、搜索结果点击等</text>
					</view>

					<text class="sub-title">1.6 内容信息</text>
					<view class="item-list">
						<text class="list-item">发布内容：文字、图片、视频等用户生成内容</text>
						<text class="list-item">聊天记录：私信内容（仅用于争议处理和安全监控）</text>
						<text class="list-item">评论内容：对他人内容的评论和回复</text>
						<text class="list-item">举报投诉：举报内容和投诉信息</text>
					</view>

					<text class="sub-title">1.7 财务与交易信息</text>
					<view class="item-list">
						<text class="list-item">支付信息：订单号、交易金额、支付方式（不包含银行卡密码）</text>
						<text class="list-item">VIP购买记录：会员购买历史、到期时间</text>
						<text class="list-item">虚拟货币：平台内虚拟货币余额和使用记录</text>
					</view>
					<view class="item-list">
						<text class="list-item">昵称、头像：用于个人展示和社交互动</text>
						<text class="list-item">性别、年龄：用于搭子匹配和内容推荐</text>
						<text class="list-item">兴趣爱好、个人简介：用于精准匹配和个性化推荐</text>
						<text class="list-item">实名认证信息：身份证号码（仅用于实名认证，加密存储）</text>
					</view>

					<text class="sub-title">1.3 位置信息</text>
					<view class="item-list">
						<text class="list-item">精确位置：用于附近搭子推荐、活动匹配（需您主动授权）</text>
						<text class="list-item">常用地址：用于优化推荐算法和服务体验</text>
					</view>

					<text class="sub-title">1.4 设备信息</text>
					<view class="item-list">
						<text class="list-item">设备标识符（IMEI、Android ID、IDFA等）：用于设备识别和安全防护</text>
						<text class="list-item">设备型号、操作系统版本：用于适配和优化应用性能</text>
						<text class="list-item">网络信息：IP地址、网络类型，用于服务提供和安全保护</text>
						<text class="list-item">应用列表：仅在您明确同意的情况下获取，用于安全风险评估</text>
					</view>

					<text class="sub-title">1.5 行为数据</text>
					<view class="item-list">
						<text class="list-item">浏览记录：您在应用内的浏览行为，用于个性化推荐</text>
						<text class="list-item">互动数据：点赞、评论、分享等行为，用于社交功能实现</text>
						<text class="list-item">搜索记录：用于优化搜索结果和推荐算法</text>
					</view>
				</view>

				<view class="section">
					<text class="section-title">二、信息收集方式</text>

					<text class="sub-title">2.1 您主动提供的信息</text>
					<view class="item-list">
						<text class="list-item">注册账户时填写的信息</text>
						<text class="list-item">完善个人资料时提供的信息</text>
						<text class="list-item">发布内容、评论、私信等互动信息</text>
						<text class="list-item">客服咨询时提供的信息</text>
						<text class="list-item">参与活动时提交的信息</text>
					</view>

					<text class="sub-title">2.2 我们自动收集的信息</text>
					<view class="item-list">
						<text class="list-item">设备信息：通过技术手段自动收集</text>
						<text class="list-item">日志信息：应用使用记录、错误日志等</text>
						<text class="list-item">位置信息：在您授权的情况下通过GPS等技术获取</text>
						<text class="list-item">Cookie和类似技术：用于改善用户体验和服务质量</text>
					</view>

					<text class="sub-title">2.3 第三方提供的信息</text>
					<view class="item-list">
						<text class="list-item">第三方登录平台提供的公开信息</text>
						<text class="list-item">合作伙伴在法律允许范围内提供的信息</text>
						<text class="list-item">公开渠道获得的信息（如公开的社交媒体信息）</text>
					</view>
				</view>

				<view class="section">
					<text class="section-title">三、我们如何使用您的个人信息</text>
					<text class="section-content">我们会基于以下目的使用您的个人信息：</text>

					<text class="sub-title">3.1 提供基础服务</text>
					<view class="item-list">
						<text class="list-item">账户注册、登录验证和身份认证</text>
						<text class="list-item">搭子匹配和推荐算法优化</text>
						<text class="list-item">活动组局和社交功能实现</text>
						<text class="list-item">消息推送和通知服务</text>
					</view>

					<text class="sub-title">3.2 改善用户体验</text>
					<view class="item-list">
						<text class="list-item">个性化内容推荐</text>
						<text class="list-item">产品功能优化和新功能开发</text>
						<text class="list-item">用户行为分析和产品改进</text>
						<text class="list-item">技术支持和客户服务</text>
					</view>

					<text class="sub-title">3.3 保障安全</text>
					<view class="item-list">
						<text class="list-item">账户安全保护和风险防控</text>
						<text class="list-item">防范欺诈、恶意行为和违法活动</text>
						<text class="list-item">数据备份和灾难恢复</text>
						<text class="list-item">法律法规要求的其他用途</text>
					</view>

					<text class="sub-title">3.4 其他用途</text>
					<view class="item-list">
						<text class="list-item">征得您明确同意的其他用途</text>
						<text class="list-item">法律法规规定的其他合法用途</text>
					</view>
				</view>

				<view class="section">
					<text class="section-title">四、我们如何共享、转让、公开披露您的个人信息</text>
					<text class="section-content">
						我们承诺严格保护您的个人信息，不会随意共享、转让或公开披露您的个人信息。

						4.1 信息共享
						我们不会与第三方共享您的个人信息，除非：
						• 获得您的明确同意或授权
						• 为实现服务功能必须的共享（如支付服务、地图服务等）
						• 法律法规要求或政府部门强制要求
						• 为维护用户、平台或公众合法权益的必要情况
						• 学术研究或统计目的（去标识化处理后）

						4.2 信息转让
						我们不会转让您的个人信息给任何第三方，除非：
						• 获得您的明确同意
						• 公司合并、收购或破产清算时，需提前通知您
						• 法律法规规定的其他情况

						4.3 公开披露
						我们不会公开披露您的个人信息，除非：
						• 获得您的明确同意
						• 基于法律要求或政府强制要求
						• 为维护平台安全和其他用户权益的紧急情况

						4.4 合作伙伴
						我们可能与以下类型的合作伙伴共享必要信息：
						• 技术服务提供商：云存储、数据分析等
						• 支付服务提供商：处理支付交易
						• 推送服务提供商：消息推送服务
						• 地图服务提供商：位置相关服务
						所有合作伙伴均签署严格的数据保护协议。
					</text>
				</view>

				<view class="section">
					<text class="section-title">五、我们如何保护您的个人信息</text>
					<text class="section-content">
						我们高度重视个人信息安全，采用业界领先的安全技术和管理措施保护您的个人信息。

						5.1 技术安全措施
						• 数据加密：采用AES-256等高强度加密算法
						• 传输安全：使用TLS/SSL加密传输协议
						• 访问控制：实施严格的权限管理和身份认证
						• 数据脱敏：对敏感信息进行脱敏处理
						• 安全审计：定期进行安全漏洞扫描和渗透测试

						5.2 管理安全措施
						• 建立完善的数据安全管理制度
						• 对员工进行定期安全培训和考核
						• 实施最小权限原则，严格控制数据访问
						• 建立数据分类分级保护机制
						• 定期备份数据，确保数据可恢复性

						5.3 物理安全措施
						• 服务器托管在具备高等级安全认证的数据中心
						• 实施7×24小时安全监控
						• 配备完善的防火、防水、防震设施
						• 严格的机房出入管理制度

						5.4 安全事件应急处理
						• 建立完善的安全事件应急响应机制
						• 发生安全事件时，立即启动应急预案
						• 及时通知受影响用户，说明事件情况和应对措施
						• 配合监管部门进行调查处理
						• 持续改进安全防护措施

						5.5 数据存储和保留
						• 您的个人信息存储在中国境内的安全服务器
						• 采用分布式存储和多重备份机制
						• 根据法律要求和业务需要确定数据保留期限
						• 超过保留期限的数据将被安全删除或匿名化处理
					</text>
				</view>

				<view class="section">
					<text class="section-title">五、您的权利</text>
					<text class="section-content">
						按照中国相关的法律、法规、标准，以及其他国家、地区的通行做法，我们保障您对自己的个人信息行使以下权利：

						1. 访问您的个人信息
						您有权访问您的个人信息，法律法规规定的例外情况除外。

						2. 更正您的个人信息
						当您发现我们处理的关于您的个人信息有错误时，您有权要求我们做出更正。

						3. 删除您的个人信息
						在以下情形中，您可以向我们提出删除个人信息的请求：
						- 如果我们处理个人信息的行为违反法律法规
						- 如果我们收集、使用您的个人信息，却未征得您的同意
						- 如果我们处理个人信息的行为违反了与您的约定

						4. 改变您授权同意的范围
						每个业务功能需要一些基本的个人信息才能得以完成。对于额外收集的个人信息的收集和使用，您可以随时给予或收回您的授权同意。

						5. 注销账户
						您随时可注销此前注册的账户，您可以通过以下方式自行操作：
						进入"我的"-"设置"-"账户与安全"-"注销账户"
					</text>
				</view>

				<view class="section">
					<text class="section-title">六、我们如何处理儿童的个人信息</text>
					<text class="section-content">
						我们的产品、网站和服务主要面向成人。如果没有父母或监护人的同意，儿童不得创建自己的用户账户。

						对于经父母同意而收集儿童个人信息的情况，我们只会在受到法律允许、父母或监护人明确同意或者保护儿童所必要的情况下使用或公开披露此信息。

						尽管当地法律和习俗对儿童的定义不同，但我们将不满14周岁的任何人均视为儿童。

						如果我们发现自己在未事先获得可证实的父母同意的情况下收集了儿童的个人信息，则会设法尽快删除相关数据。
					</text>
				</view>

				<view class="section">
					<text class="section-title">七、您的个人信息如何在全球范围转移</text>
					<text class="section-content">
						原则上，我们在中华人民共和国境内收集和产生的个人信息，将存储在中华人民共和国境内。

						由于我们通过遍布全球的资源和服务器提供产品或服务，这意味着，在获得您的授权同意后，您的个人信息可能会被转移到您使用产品或服务所在国家/地区的境外管辖区，或者受到来自这些管辖区的访问。

						此类管辖区可能设有不同的数据保护法，甚至未设立相关法律。在此类情况下，我们会确保您的个人信息得到在中华人民共和国境内足够同等的保护。
					</text>
				</view>

				<view class="section">
					<text class="section-title">八、本政策如何更新</text>
					<text class="section-content">
						我们可能适时会对本隐私权政策进行调整或变更，本隐私权政策的任何更新将以标注更新时间的方式公布在我们的网站上，除法律法规或监管规定另有强制性规定外，经调整或变更的内容一经通知或公布后的7日后生效。如果调整或变更的内容将导致您在本隐私权政策项下权利的实质减损，我们将在生效前通过在主页面显著位置提示、向您发送电子邮件或以其他方式通知您，在该种情况下，若您继续使用我们的服务，即表示同意受经修订的隐私权政策的约束。
					</text>
				</view>

				<view class="section">
					<text class="section-title">九、如何联系我们</text>
					<text class="section-content">
						如果您对本隐私权政策有任何疑问、意见或建议，通过以下方式与我们联系：

						邮箱：<EMAIL>
						地址：上海市崇明区北沿公路2111号3幢
						邮编：202150

						我们将在收到您的反馈后尽快回复您的请求。

						一般情况下，我们将在十五天内回复。如果您对我们的回复不满意，特别是我们的个人信息处理行为损害了您的合法权益，您还可以向网信、电信、公安及工商等监管部门进行投诉或举报。
					</text>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import gestureBackMixin from '@/mixins/gesture-back.js'

export default {
	mixins: [gestureBackMixin],

	methods: {
		// 智能返回逻辑
		goBack() {
			// 检查是否有上一页，如果没有则根据来源页面跳转
			const pages = getCurrentPages()
			if (pages.length > 1) {
				uni.navigateBack()
			} else {
				// 如果没有上一页，默认返回到我的页面
				uni.switchTab({
					url: '/pages/profile/profile'
				})
			}
		},

		goToUserAgreement() {
			uni.navigateTo({
				url: '/pages/user-agreement/user-agreement'
			});
		},


	}
}
</script>

<style scoped>
@import '@/styles/global.scss';

.privacy-container {
	width: 100vw;
	min-height: 100vh;
	background: #ffffff;
}

.status-bar {
	background: #ffffff;
}

.header {
	position: relative;
	height: 120rpx;
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
}

/* 返回按钮定位 */
.header .back-button {
	position: absolute;
	left: 32rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
}

.header .back-button:active {
	background: rgba(255, 255, 255, 0.3);
}

.back-icon {
	width: 32rpx;
	height: 32rpx;
}

.title {
	color: #ffffff;
	font-size: 36rpx;
	font-weight: 600;
}

.content {
	height: calc(100vh - 44px - var(--status-bar-height));
	padding: 0;
	background: #fff;
}

.policy-content {
	padding: 20px;
	max-width: 100%;
}

/* 版本信息区域 */
.version-section {
	margin-bottom: 30px;
	padding: 16px;
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 100%);
	border-radius: 12px;
}

.version-info, .update-time, .effective-time {
	display: block;
	font-size: 14px;
	color: #fff;
	margin-bottom: 6px;
	font-weight: 500;
}

.version-info {
	font-weight: 600;
	font-size: 15px;
}

.related-links {
	margin: 24px 0;
}

.link-item {
	display: flex;
	align-items: center;
	padding: 16px 20px;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 12px;
	border: 1px solid #dee2e6;
	transition: all 0.3s ease;
	cursor: pointer;
}

.link-item:active {
	transform: scale(0.98);
	background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.link-icon {
	font-size: 20px;
	margin-right: 12px;
}

.link-text {
	flex: 1;
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.link-arrow {
	font-size: 18px;
	color: #667eea;
	font-weight: bold;
}

/* 章节标题 */
.section-title {
	display: block;
	font-size: 18px;
	font-weight: 700;
	color: #333;
	margin: 30px 0 8px 0;
	text-align: left;
}

/* 分割线 */
.section-divider {
	width: 60px;
	height: 3px;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
	margin-bottom: 20px;
	border-radius: 2px;
}

/* 子标题 */
.sub-title {
	display: block;
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin: 24px 0 12px 0;
	text-align: left;
}

/* 段落文本 */
.paragraph {
	display: block;
	font-size: 15px;
	line-height: 1.8;
	color: #555;
	margin-bottom: 16px;
	text-align: justify;
	text-indent: 0;
}

/* 列表项 */
.list-item {
	display: block;
	font-size: 15px;
	line-height: 1.7;
	color: #555;
	margin-bottom: 10px;
	text-align: left;
	padding-left: 0;
}
</style>
