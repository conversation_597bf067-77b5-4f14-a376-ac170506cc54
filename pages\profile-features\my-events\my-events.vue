<template>
	<view class="my-events-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<uni-icons type="left" size="24" color="#fff"></uni-icons>
					<text class="back-text">返回</text>
				</view>
				<text class="nav-title">我的活动</text>
				<view class="nav-right">
					<view class="calendar-btn" @tap="showCalendar">
						<uni-icons type="calendar" size="20" color="#fff"></uni-icons>
					</view>
				</view>
			</view>
			
			<!-- 头部装饰 -->
			<view class="header-decoration">
				<view class="decoration-wave"></view>
				<view class="decoration-dots">
					<view class="dot" v-for="n in 6" :key="n"></view>
				</view>
			</view>
		</view>

		<!-- 活动统计 -->
		<view class="events-stats">
			<view class="stats-container">
				<view class="stat-item">
					<text class="stat-number">{{ totalEvents }}</text>
					<text class="stat-label">全部活动</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-number">{{ upcomingEvents }}</text>
					<text class="stat-label">即将开始</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-number">{{ completedEvents }}</text>
					<text class="stat-label">已完成</text>
				</view>
			</view>
		</view>

		<!-- 活动筛选 -->
		<view class="event-tabs">
			<scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false">
				<view class="tab-item" v-for="(tab, index) in eventTabs" :key="index" :class="{ active: currentTab === index }" @tap="switchTab(index)">
					<view class="tab-content">
						<uni-icons v-if="tab.icon" :type="tab.icon" size="16" :color="currentTab === index ? '#4ecdc4' : 'rgba(255,255,255,0.7)'"></uni-icons>
						<text class="tab-text">{{ tab.name }}</text>
					</view>
					<view class="tab-count" v-if="tab.count > 0">{{ tab.count }}</view>
				</view>
			</scroll-view>
		</view>

		<!-- 活动列表 -->
		<scroll-view class="events-content" scroll-y="true" @scrolltolower="loadMore">
			<!-- 空状态 -->
			<view class="empty-state" v-if="currentEvents.length === 0">
				<view class="empty-icon">
					<uni-icons type="calendar" size="80" color="#ddd"></uni-icons>
				</view>
				<text class="empty-title">暂无活动</text>
				<text class="empty-desc">{{ getEmptyDesc() }}</text>
				<view class="empty-action" @tap="exploreEvents">
					<text class="action-text">去发现</text>
				</view>
			</view>

			<!-- 活动列表 -->
			<view class="event-list" v-else>
				<view class="event-item" v-for="event in currentEvents" :key="event.id" @tap="viewEventDetail(event)">
					<view class="event-image-container">
						<image class="event-image" :src="event.image" mode="aspectFill"></image>
						<view class="event-status" :class="event.statusClass">
							<text class="status-text">{{ event.statusText }}</text>
						</view>
					</view>

					<view class="event-content">
						<view class="event-header">
							<text class="event-title">{{ event.title }}</text>
							<view class="event-category" :class="event.categoryClass">
								<text class="category-text">{{ event.category }}</text>
							</view>
						</view>

						<view class="event-info">
							<view class="info-item">
								<uni-icons type="location" size="14" color="#999"></uni-icons>
								<text class="info-text">{{ event.location }}</text>
							</view>
							<view class="info-item">
								<uni-icons type="calendar" size="14" color="#999"></uni-icons>
								<text class="info-text">{{ event.date }}</text>
							</view>
							<view class="info-item">
								<uni-icons type="person" size="14" color="#999"></uni-icons>
								<text class="info-text">{{ event.participants }}/{{ event.maxParticipants }}人</text>
							</view>
						</view>

						<view class="event-description">
							<text class="desc-text">{{ event.description }}</text>
						</view>

						<view class="event-actions">
							<view class="action-btn secondary" v-if="event.canCancel" @tap.stop="cancelEvent(event)">
								<text class="btn-text">取消报名</text>
							</view>
							<view class="action-btn secondary" v-if="event.canShare" @tap.stop="shareEvent(event)">
								<text class="btn-text">分享</text>
							</view>
							<view class="action-btn primary" v-if="event.canJoin" @tap.stop="joinEvent(event)">
								<text class="btn-text">立即参加</text>
							</view>
							<view class="action-btn primary" v-if="event.canReview" @tap.stop="reviewEvent(event)">
								<text class="btn-text">评价活动</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore">
				<text class="load-text">加载更多活动...</text>
			</view>
		</scroll-view>

		<!-- 创建活动按钮 -->
		<view class="create-event-btn" @tap="createEvent">
			<view class="btn-content">
				<uni-icons type="plus" size="24" color="#fff"></uni-icons>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				currentTab: 0,
				hasMore: true,
				
				// 活动筛选标签
				eventTabs: [
					{ name: '全部', count: 0, icon: 'list' },
					{ name: '即将开始', count: 3, icon: 'calendar' },
					{ name: '进行中', count: 1, icon: 'play' },
					{ name: '已完成', count: 5, icon: 'checkmarkempty' },
					{ name: '已取消', count: 1, icon: 'close' }
				],

				// 所有活动数据
				allEvents: [
					{
						id: '001',
						title: '周末户外徒步活动',
						description: '一起去山里呼吸新鲜空气，享受大自然的美好',
						image: 'https://s1.imagehub.cc/images/2025/05/26/hiking.png',
						location: '香山公园',
						date: '12-25 09:00',
						participants: 8,
						maxParticipants: 15,
						category: '户外运动',
						categoryClass: 'outdoor',
						status: 'upcoming',
						statusText: '即将开始',
						statusClass: 'upcoming',
						canCancel: true,
						canShare: true
					},
					{
						id: '002',
						title: '城市夜跑团',
						description: '夜晚的城市别有一番风味，一起跑步健身吧',
						image: 'https://s1.imagehub.cc/images/2025/05/26/night-run.png',
						location: '奥林匹克公园',
						date: '12-22 19:00',
						participants: 12,
						maxParticipants: 20,
						category: '运动健身',
						categoryClass: 'fitness',
						status: 'ongoing',
						statusText: '进行中',
						statusClass: 'ongoing',
						canShare: true
					},
					{
						id: '003',
						title: '周末BBQ聚会',
						description: '美食、音乐、朋友，完美的周末时光',
						image: 'https://s1.imagehub.cc/images/2025/05/26/bbq.png',
						location: '朝阳公园',
						date: '12-20 16:00',
						participants: 15,
						maxParticipants: 15,
						category: '聚会娱乐',
						categoryClass: 'party',
						status: 'completed',
						statusText: '已完成',
						statusClass: 'completed',
						canReview: true,
						canShare: true
					}
				]
			}
		},

		computed: {
			totalEvents() {
				return this.allEvents.length;
			},

			upcomingEvents() {
				return this.allEvents.filter(event => event.status === 'upcoming').length;
			},

			completedEvents() {
				return this.allEvents.filter(event => event.status === 'completed').length;
			},

			currentEvents() {
				if (this.currentTab === 0) {
					return this.allEvents;
				}
				const statusMap = {
					1: 'upcoming',
					2: 'ongoing',
					3: 'completed',
					4: 'cancelled'
				};
				return this.allEvents.filter(event => event.status === statusMap[this.currentTab]);
			}
		},

		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},

		methods: {
			goBack() {
				uni.switchTab({
					url: '/pages/profile/profile'
				});
			},

			showCalendar() {
				uni.showToast({
					title: '日历功能开发中',
					icon: 'none'
				});
			},

			switchTab(index) {
				this.currentTab = index;
			},

			getEmptyDesc() {
				const descs = [
					'还没有参加任何活动哦',
					'暂无即将开始的活动',
					'暂无进行中的活动',
					'暂无已完成的活动',
					'暂无已取消的活动'
				];
				return descs[this.currentTab];
			},

			exploreEvents() {
				uni.switchTab({
					url: '/pages/discover/discover'
				});
			},

			viewEventDetail(event) {
				uni.navigateTo({
					url: `/pages/events/event-detail?id=${event.id}`
				});
			},

			createEvent() {
				uni.navigateTo({
					url: '/pages/events/create-event'
				});
			},

			// 活动操作
			joinEvent(event) {
				uni.showModal({
					title: '参加活动',
					content: `确定要参加"${event.title}"吗？`,
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '报名成功',
								icon: 'success'
							});
						}
					}
				});
			},

			cancelEvent(event) {
				uni.showModal({
					title: '取消报名',
					content: `确定要取消报名"${event.title}"吗？`,
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '已取消报名',
								icon: 'success'
							});
						}
					}
				});
			},

			shareEvent(event) {
				uni.showActionSheet({
					itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
					success: (res) => {
						uni.showToast({
							title: '分享功能开发中',
							icon: 'none'
						});
					}
				});
			},

			reviewEvent(event) {
				uni.navigateTo({
					url: `/pages/events/event-review?id=${event.id}`
				});
			},

			loadMore() {
				if (!this.hasMore) return;
				
				setTimeout(() => {
					this.hasMore = false;
				}, 1000);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.my-events-page {
		background: linear-gradient(180deg, #4ECDC4 0%, #44A08D 100%);
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.status-bar {
		background: transparent;
	}

	.header {
		position: relative;
		background: transparent;
		overflow: hidden;

		.nav-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;
			height: 88rpx;
			position: relative;
			z-index: 2;

			.nav-left {
				display: flex;
				align-items: center;
				gap: 8rpx;
				flex: 1;

				.back-text {
					font-size: 28rpx;
					color: #fff;
					font-weight: 500;
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: #fff;
				font-weight: 600;
				text-align: center;
				flex: 2;
			}

			.nav-right {
				flex: 1;
				display: flex;
				justify-content: flex-end;

				.calendar-btn {
					width: 48rpx;
					height: 48rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: rgba(255, 255, 255, 0.2);
					border-radius: 24rpx;
				}
			}
		}

		.header-decoration {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			overflow: hidden;

			.decoration-wave {
				position: absolute;
				bottom: -20rpx;
				left: 0;
				right: 0;
				height: 40rpx;
				background: rgba(255, 255, 255, 0.1);
				border-radius: 50% 50% 0 0;
			}

			.decoration-dots {
				position: absolute;
				top: 20rpx;
				right: 80rpx;
				display: flex;
				gap: 8rpx;

				.dot {
					width: 8rpx;
					height: 8rpx;
					background: rgba(255, 255, 255, 0.3);
					border-radius: 50%;

					&:nth-child(2n) {
						background: rgba(255, 255, 255, 0.5);
					}
				}
			}
		}
	}

	// 活动统计
	.events-stats {
		background: rgba(255, 255, 255, 0.15);
		margin: 24rpx;
		border-radius: 20rpx;
		backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 255, 255, 0.2);

		.stats-container {
			display: flex;
			align-items: center;
			padding: 32rpx 24rpx;

			.stat-item {
				flex: 1;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
				gap: 8rpx;
				padding: 8rpx 12rpx;
				border-radius: 12rpx;
				transition: all 0.3s ease;

				&:active {
					background: rgba(255, 255, 255, 0.1);
					transform: scale(0.98);
				}

				.stat-number {
					font-size: 48rpx;
					color: #fff;
					font-weight: 700;
				}

				.stat-label {
					font-size: 24rpx;
					color: rgba(255, 255, 255, 0.8);
					font-weight: 500;
				}
			}

			.stat-divider {
				width: 1rpx;
				height: 60rpx;
				background: rgba(255, 255, 255, 0.3);
			}
		}
	}

	// 活动筛选
	.event-tabs {
		background: rgba(255, 255, 255, 0.1);
		margin: 0 24rpx 24rpx;
		border-radius: 16rpx;
		padding: 8rpx;

		.tabs-scroll {
			white-space: nowrap;

			.tab-item {
				display: inline-block;
				position: relative;
				padding: 16rpx 24rpx;
				margin-right: 8rpx;
				border-radius: 12rpx;
				transition: all 0.3s ease;

				&.active {
					background: rgba(255, 255, 255, 0.9);

					.tab-text {
						color: #4ECDC4;
						font-weight: 600;
					}

					.tab-count {
						background: #4ECDC4;
						color: #fff;
					}
				}

				.tab-content {
					display: flex;
					align-items: center;
					gap: 6rpx;
				}

				.tab-text {
					font-size: 26rpx;
					color: rgba(255, 255, 255, 0.8);
					transition: all 0.3s ease;
				}

				.tab-count {
					position: absolute;
					top: 4rpx;
					right: 4rpx;
					background: rgba(255, 255, 255, 0.3);
					color: #fff;
					font-size: 18rpx;
					padding: 2rpx 6rpx;
					border-radius: 8rpx;
					min-width: 20rpx;
					text-align: center;
					line-height: 1;
				}
			}
		}
	}

	// 活动内容区域
	.events-content {
		flex: 1;
		padding: 0 24rpx 120rpx;

		// 空状态
		.empty-state {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 120rpx 40rpx;
			text-align: center;

			.empty-icon {
				margin-bottom: 32rpx;
				opacity: 0.5;
			}

			.empty-title {
				font-size: 32rpx;
				color: #fff;
				font-weight: 600;
				margin-bottom: 16rpx;
			}

			.empty-desc {
				font-size: 26rpx;
				color: rgba(255, 255, 255, 0.7);
				margin-bottom: 48rpx;
				line-height: 1.5;
			}

			.empty-action {
				background: rgba(255, 255, 255, 0.2);
				padding: 20rpx 48rpx;
				border-radius: 32rpx;
				border: 1rpx solid rgba(255, 255, 255, 0.3);

				.action-text {
					font-size: 28rpx;
					color: #fff;
					font-weight: 600;
				}
			}
		}

		// 活动列表
		.event-list {
			.event-item {
				background: #fff;
				border-radius: 16rpx;
				margin-bottom: 24rpx;
				overflow: hidden;
				box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
				}

				.event-image-container {
					position: relative;
					height: 320rpx;

					.event-image {
						width: 100%;
						height: 100%;
						background: #f5f5f5;
					}

					.event-status {
						position: absolute;
						top: 16rpx;
						right: 16rpx;
						padding: 8rpx 16rpx;
						border-radius: 16rpx;
						font-size: 22rpx;
						font-weight: 500;

						&.upcoming {
							background: rgba(76, 205, 196, 0.9);
							color: #fff;
						}

						&.ongoing {
							background: rgba(255, 193, 7, 0.9);
							color: #fff;
						}

						&.completed {
							background: rgba(40, 167, 69, 0.9);
							color: #fff;
						}

						&.cancelled {
							background: rgba(220, 53, 69, 0.9);
							color: #fff;
						}
					}
				}

				.event-content {
					padding: 24rpx;

					.event-header {
						display: flex;
						justify-content: space-between;
						align-items: flex-start;
						margin-bottom: 16rpx;

						.event-title {
							font-size: 32rpx;
							color: #333;
							font-weight: 600;
							line-height: 1.4;
							flex: 1;
							margin-right: 16rpx;
						}

						.event-category {
							padding: 6rpx 12rpx;
							border-radius: 12rpx;
							font-size: 20rpx;
							font-weight: 500;
							flex-shrink: 0;

							&.outdoor {
								background: rgba(76, 205, 196, 0.1);
								color: #4ECDC4;
							}

							&.fitness {
								background: rgba(255, 107, 107, 0.1);
								color: #FF6B6B;
							}

							&.party {
								background: rgba(255, 193, 7, 0.1);
								color: #FFC107;
							}

							.category-text {
								font-size: 20rpx;
							}
						}
					}

					.event-info {
						margin-bottom: 16rpx;

						.info-item {
							display: flex;
							align-items: center;
							gap: 8rpx;
							margin-bottom: 8rpx;

							&:last-child {
								margin-bottom: 0;
							}

							.info-text {
								font-size: 24rpx;
								color: #666;
							}
						}
					}

					.event-description {
						margin-bottom: 20rpx;

						.desc-text {
							font-size: 26rpx;
							color: #666;
							line-height: 1.5;
						}
					}

					.event-actions {
						display: flex;
						justify-content: flex-end;
						align-items: center;
						gap: 16rpx;

						.action-btn {
							padding: 16rpx 32rpx;
							border-radius: 24rpx;
							font-size: 26rpx;
							font-weight: 500;
							transition: all 0.3s ease;

							&.primary {
								background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
								color: #fff;
								box-shadow: 0 4rpx 16rpx rgba(76, 205, 196, 0.3);

								&:active {
									transform: scale(0.95);
								}
							}

							&.secondary {
								background: #f8f9fa;
								color: #666;
								border: 1rpx solid #e9ecef;

								&:active {
									background: #e9ecef;
								}
							}

							.btn-text {
								font-size: 26rpx;
							}
						}
					}
				}
			}
		}

		// 加载更多
		.load-more {
			text-align: center;
			padding: 32rpx;

			.load-text {
				font-size: 26rpx;
				color: rgba(255, 255, 255, 0.7);
			}
		}
	}

	// 创建活动按钮
	.create-event-btn {
		position: fixed;
		bottom: 120rpx;
		right: 24rpx;
		z-index: 100;

		.btn-content {
			width: 112rpx;
			height: 112rpx;
			background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
			border-radius: 56rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.4);

			&:active {
				transform: scale(0.9);
			}
		}
	}
</style>
