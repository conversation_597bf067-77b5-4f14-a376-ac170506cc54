<template>
	<view class="test-page">
		<view class="test-section">
			<text class="title">头像文件测试</text>
			
			<view class="avatar-test">
				<text class="label">PNG默认头像:</text>
				<image 
					class="test-avatar" 
					src="/static/default-avatar.png" 
					mode="aspectFill"
					@load="onPngLoad"
					@error="onPngError"
				></image>
				<text class="status">{{ pngStatus }}</text>
			</view>
			
			<view class="avatar-test">
				<text class="label">JPG默认头像:</text>
				<image 
					class="test-avatar" 
					src="/static/default-avatar.jpg" 
					mode="aspectFill"
					@load="onJpgLoad"
					@error="onJpgError"
				></image>
				<text class="status">{{ jpgStatus }}</text>
			</view>
			
			<view class="avatar-test">
				<text class="label">Logo测试:</text>
				<image 
					class="test-avatar" 
					src="/static/logo.png" 
					mode="aspectFill"
					@load="onLogoLoad"
					@error="onLogoError"
				></image>
				<text class="status">{{ logoStatus }}</text>
			</view>
			
			<button @click="goBack">返回</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			pngStatus: '加载中...',
			jpgStatus: '加载中...',
			logoStatus: '加载中...'
		}
	},
	methods: {
		onPngLoad() {
			this.pngStatus = '✅ PNG加载成功'
			console.log('PNG默认头像加载成功')
		},
		onPngError(e) {
			this.pngStatus = '❌ PNG加载失败'
			console.log('PNG默认头像加载失败:', e)
		},
		onJpgLoad() {
			this.jpgStatus = '✅ JPG加载成功'
			console.log('JPG默认头像加载成功')
		},
		onJpgError(e) {
			this.jpgStatus = '❌ JPG加载失败'
			console.log('JPG默认头像加载失败:', e)
		},
		onLogoLoad() {
			this.logoStatus = '✅ Logo加载成功'
			console.log('Logo加载成功')
		},
		onLogoError(e) {
			this.logoStatus = '❌ Logo加载失败'
			console.log('Logo加载失败:', e)
		},
		goBack() {
			uni.navigateBack()
		}
	}
}
</script>

<style scoped>
.test-page {
	padding: 40rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.test-section {
	background: white;
	padding: 40rpx;
	border-radius: 20rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 40rpx;
	text-align: center;
}

.avatar-test {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #f9f9f9;
	border-radius: 10rpx;
}

.label {
	width: 200rpx;
	font-size: 28rpx;
}

.test-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin: 0 20rpx;
	border: 2rpx solid #ddd;
}

.status {
	font-size: 24rpx;
	flex: 1;
}

button {
	margin-top: 40rpx;
	background: #007aff;
	color: white;
	border: none;
	padding: 20rpx;
	border-radius: 10rpx;
}
</style>
