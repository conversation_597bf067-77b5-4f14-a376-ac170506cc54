/**
 * 调试辅助工具
 * 用于查看详细的诊断结果
 */

// 获取并显示最后一次诊断结果
export function showLastDiagnosisResult() {
	try {
		const lastResult = uni.getStorageSync('lastNetworkDiagnosis')
		if (lastResult) {
			console.log('=== 最后一次网络诊断结果 ===')
			console.log('时间:', lastResult.timestamp)
			console.log('整体状态:', lastResult.analysis?.overall)
			
			if (lastResult.tests) {
				console.log('\n=== 详细测试结果 ===')
				Object.keys(lastResult.tests).forEach(testName => {
					const test = lastResult.tests[testName]
					console.log(`\n${testName}:`)
					console.log('  成功:', test.success)
					console.log('  消息:', test.message)
					if (test.error) {
						console.log('  错误:', test.error)
					}
					if (test.results) {
						console.log('  详细结果:', test.results)
					}
				})
			}
			
			if (lastResult.analysis) {
				console.log('\n=== 分析结果 ===')
				console.log('整体状态:', lastResult.analysis.overall)
				console.log('问题列表:', lastResult.analysis.issues)
				console.log('建议:', lastResult.analysis.recommendations)
			}
			
			// 显示给用户
			showDiagnosisDetails(lastResult)
		} else {
			console.log('没有找到诊断结果')
			uni.showToast({
				title: '没有诊断记录',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('获取诊断结果失败:', error)
		uni.showToast({
			title: '获取诊断结果失败',
			icon: 'error'
		})
	}
}

// 显示诊断详情
function showDiagnosisDetails(result) {
	let content = `时间: ${new Date(result.timestamp).toLocaleString()}\n`
	content += `整体状态: ${getStatusText(result.analysis?.overall)}\n\n`
	
	if (result.tests) {
		content += '测试结果:\n'
		Object.keys(result.tests).forEach(testName => {
			const test = result.tests[testName]
			const status = test.success ? '✓' : '✗'
			const name = getTestDisplayName(testName)
			content += `${status} ${name}: ${test.message || (test.success ? '正常' : '异常')}\n`
		})
	}
	
	if (result.analysis?.issues?.length > 0) {
		content += '\n问题:\n'
		result.analysis.issues.forEach(issue => {
			content += `• ${issue}\n`
		})
	}
	
	if (result.analysis?.recommendations?.length > 0) {
		content += '\n建议:\n'
		result.analysis.recommendations.forEach(rec => {
			content += `• ${rec}\n`
		})
	}
	
	uni.showModal({
		title: '网络诊断详情',
		content: content,
		showCancel: false,
		confirmText: '知道了'
	})
}

// 获取状态文本
function getStatusText(status) {
	switch (status) {
		case 'good': return '正常'
		case 'warning': return '有警告'
		case 'critical': return '严重问题'
		default: return '未知'
	}
}

// 获取测试显示名称
function getTestDisplayName(testName) {
	const names = {
		networkStatus: '网络状态',
		basicConnection: '基础连接',
		uniCloudConnection: 'uniCloud服务',
		databaseConnection: '数据库连接',
		cloudFunctionDeployment: '云函数部署'
	}
	return names[testName] || testName
}

// 测试单个云函数
export async function testSingleCloudFunction(functionName) {
	console.log(`测试云函数: ${functionName}`)
	
	try {
		const result = await uniCloud.callFunction({
			name: functionName,
			data: { test: 'debug-test', timestamp: Date.now() }
		})
		
		console.log(`${functionName} 测试成功:`, result)
		
		uni.showModal({
			title: `${functionName} 测试结果`,
			content: `状态: 成功\n响应: ${JSON.stringify(result.result, null, 2)}`,
			showCancel: false,
			confirmText: '知道了'
		})
		
		return { success: true, result: result.result }
	} catch (error) {
		console.error(`${functionName} 测试失败:`, error)
		
		let errorMsg = '未知错误'
		if (error.errCode) {
			switch (error.errCode) {
				case 'FUNCTION_NOT_FOUND':
					errorMsg = '云函数未找到，请检查是否已部署'
					break
				case 'FUNCTION_EXECUTION_FAIL':
					errorMsg = '云函数执行失败，请检查代码'
					break
				case 'NETWORK_ERROR':
					errorMsg = '网络连接失败'
					break
				default:
					errorMsg = `错误代码: ${error.errCode}`
			}
		} else if (error.message) {
			errorMsg = error.message
		}
		
		uni.showModal({
			title: `${functionName} 测试结果`,
			content: `状态: 失败\n错误: ${errorMsg}`,
			showCancel: false,
			confirmText: '知道了'
		})
		
		return { success: false, error: errorMsg }
	}
}

// 测试所有核心云函数
export async function testAllCloudFunctions() {
	const functions = ['simple-test', 'auth', 'test-db']
	const results = {}
	
	uni.showLoading({
		title: '测试云函数中...'
	})
	
	for (const funcName of functions) {
		console.log(`正在测试: ${funcName}`)
		results[funcName] = await testSingleCloudFunction(funcName)
		
		// 短暂延迟，避免请求过快
		await new Promise(resolve => setTimeout(resolve, 500))
	}
	
	uni.hideLoading()
	
	// 显示汇总结果
	const successCount = Object.values(results).filter(r => r.success).length
	const totalCount = functions.length
	
	let content = `测试完成: ${successCount}/${totalCount} 个云函数正常\n\n`
	
	functions.forEach(funcName => {
		const result = results[funcName]
		const status = result.success ? '✓' : '✗'
		content += `${status} ${funcName}: ${result.success ? '正常' : result.error}\n`
	})
	
	if (successCount < totalCount) {
		content += '\n建议:\n'
		content += '1. 检查云函数是否已正确部署\n'
		content += '2. 在HBuilderX中重新上传失败的云函数\n'
		content += '3. 检查网络连接状态'
	}
	
	uni.showModal({
		title: '云函数测试结果',
		content: content,
		showCancel: false,
		confirmText: '知道了'
	})
	
	return results
}

// 清除所有诊断缓存
export function clearDiagnosisCache() {
	try {
		uni.removeStorageSync('lastNetworkDiagnosis')
		uni.removeStorageSync('lastStartupCheck')
		uni.showToast({
			title: '诊断缓存已清除',
			icon: 'success'
		})
	} catch (error) {
		console.error('清除缓存失败:', error)
		uni.showToast({
			title: '清除缓存失败',
			icon: 'error'
		})
	}
}
