<template>
	<view class="splash-container">
		<!-- 启动页背景图片 -->
		<image class="splash-bg" src="/static/app-logo-new.png" mode="aspectFill"></image>
		
		<!-- 用户协议弹窗 -->
		<view v-if="showUserAgreement" class="modal-overlay" @tap="handleOverlayTap">
			<view class="modal-content" @tap.stop>
				<view class="modal-header">
					<text class="modal-title">趣嗒同行用户协议</text>
				</view>
				<scroll-view class="modal-body" scroll-y>
					<view class="agreement-content">
						<view class="agreement-paragraph">
							<text class="agreement-text">尊敬的趣嗒同行用户，欢迎使用趣嗒同行社交平台！请仔细阅读并同意以下条款及</text>
							<text class="privacy-link" @tap="goToPrivacyPolicy">《隐私权政策》</text>
							<text class="agreement-text">：</text>
						</view>

						<view class="agreement-list">
							<text class="agreement-item">1. 服务内容：趣嗒同行为您提供搭子匹配、组局社交等服务，您需年满18周岁方可使用。</text>

							<text class="agreement-item">2. 信息收集：我们会收集您的基本信息（昵称、头像、年龄）、位置信息（用于附近搭子推荐）、设备信息（设备ID、系统版本、网络状态等，用于服务优化和安全保障）。</text>

							<text class="agreement-item">3. 信息使用：您的信息仅用于提供服务、改善体验、保障安全，不会用于其他商业目的。</text>

							<text class="agreement-item">4. 信息保护：我们采用行业标准的安全措施保护您的信息，未经授权不会向第三方披露。</text>

							<text class="agreement-item">5. 您的权利：您可随时查看、修改、删除个人信息，或注销账户。如有疑问请联系客服。</text>

							<text class="agreement-item">6. 使用规范：请遵守法律法规，不得发布违法违规内容，维护良好的社交环境。</text>
						</view>
					</view>
				</scroll-view>
				<view class="modal-actions">
					<button class="agree-btn" @tap="agreeUserAgreement">同意</button>
					<button class="disagree-btn" @tap="disagreeUserAgreement">不同意</button>
				</view>
			</view>
		</view>
		
		<!-- 个性化服务体验弹窗 -->
		<view v-if="showPersonalizedService" class="modal-overlay" @tap="handleOverlayTap">
			<view class="modal-content personalized-modal" @tap.stop>
				<view class="personalized-header">
					<view class="mascot-icon">
						<text class="mascot-emoji">🐣</text>
					</view>
					<text class="personalized-title">个性化服务体验</text>
				</view>
				<scroll-view class="personalized-body" scroll-y>
					<view class="personalized-content">
						<text class="personalized-subtitle">您使用的安卓版本需要允许我们跟踪您的数据，您的数据仅用于：</text>
						<view class="feature-list">
							<view class="feature-item">
								<text class="feature-icon">●</text>
								<text class="feature-text">了解您的兴趣，让个性化推荐更精准</text>
							</view>
							<view class="feature-item">
								<text class="feature-icon">●</text>
								<text class="feature-text">标识设备，并保障服务安全</text>
							</view>
						</view>
						<text class="privacy-note">我们有完善的权限措施来保护您的隐私信息不被第三方获取和识别。</text>
					</view>
				</scroll-view>
				<view class="personalized-actions">
					<button class="continue-btn" @tap="agreePersonalizedService">继续</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import userAgreement from '@/utils/userAgreement.js';

export default {
	data() {
		return {
			showUserAgreement: false,
			showPersonalizedService: false
		}
	},
	onLoad() {
		// 检查用户是否已经同意过协议
		this.checkUserAgreementStatus();
	},
	methods: {
		// 检查用户协议状态
		checkUserAgreementStatus() {
			try {
				// 如果不需要显示用户协议，直接跳转到首页
				if (!userAgreement.shouldShowUserAgreement()) {
					this.goToHomePage();
					return;
				}

				// 如果用户已同意协议但未同意个性化服务
				if (userAgreement.hasAgreedToTerms() && !userAgreement.hasAgreedToPersonalized()) {
					this.showPersonalizedService = true;
					return;
				}

				// 如果用户未同意协议，显示用户协议弹窗
				if (!userAgreement.hasAgreedToTerms()) {
					this.showUserAgreement = true;
					return;
				}

				// 默认情况，跳转到首页
				this.goToHomePage();
			} catch (error) {
				console.error('检查用户协议状态失败:', error);
				// 出错时显示用户协议
				this.showUserAgreement = true;
			}
		},

		// 跳转到首页
		goToHomePage() {
			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/index/index'
				});
			}, 300);
		},

		handleOverlayTap() {
			// 点击遮罩层不关闭弹窗
		},
		goToPrivacyPolicy() {
			// 跳转到隐私权政策页面
			uni.navigateTo({
				url: '/pages/privacy-policy/privacy-policy'
			});
		},
		agreeUserAgreement() {
			if (userAgreement.saveAgreementStatus()) {
				this.showUserAgreement = false;
				this.showPersonalizedService = true;
			} else {
				uni.showToast({
					title: '保存失败，请重试',
					icon: 'none'
				});
			}
		},
		disagreeUserAgreement() {
			// 不同意则退出应用
			uni.showModal({
				title: '提示',
				content: '需要同意用户协议才能继续使用应用',
				showCancel: false,
				success: () => {
					// #ifdef APP-PLUS
					plus.runtime.quit();
					// #endif
				}
			});
		},
		agreePersonalizedService() {
			if (userAgreement.savePersonalizedStatus()) {
				this.showPersonalizedService = false;
				// 跳转到主页
				this.goToHomePage();
			} else {
				uni.showToast({
					title: '保存失败，请重试',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style scoped>
.splash-container {
	width: 100vw;
	height: 100vh;
	position: relative;
	overflow: hidden;
}

.splash-bg {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	width: 340px;
	background: #fff;
	border-radius: 20px;
	overflow: hidden;
	margin: 0 20px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
	padding: 24px 24px 16px;
	text-align: center;
	border-bottom: 1px solid #f0f0f0;
}

.modal-title {
	font-size: 20px;
	font-weight: 600;
	color: #333;
}

.modal-body {
	padding: 0;
	max-height: 400px;
}

.agreement-content {
	padding: 24px 32px;
	margin: 0 16px;
}

.agreement-paragraph {
	margin-bottom: 20px;
	line-height: 1.8;
}

.agreement-text {
	font-size: 15px;
	line-height: 1.8;
	color: #555;
}

.privacy-link {
	font-size: 15px;
	line-height: 1.8;
	color: #007AFF;
}

.agreement-list {
	margin-top: 16px;
}

.agreement-item {
	display: block;
	font-size: 15px;
	line-height: 1.8;
	color: #555;
	margin-bottom: 16px;
	text-align: justify;
}

.modal-actions {
	padding: 0 32px 32px;
}

.personalized-body {
	flex: 1;
	max-height: 250px;
	overflow-y: auto;
	margin-bottom: 16px;
}

.personalized-content {
	padding: 20px 32px 24px 32px;
}

.personalized-actions {
	padding: 16px 32px 32px;
	flex-shrink: 0;
	background: #fff;
	border-top: 1px solid #f0f0f0;
}

.agree-btn {
	width: 100%;
	height: 48px;
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
	border: none;
	border-radius: 24px;
	font-size: 17px;
	font-weight: 600;
	color: #333;
	margin-bottom: 12px;
	box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.disagree-btn {
	width: 100%;
	height: 48px;
	background: transparent;
	border: none;
	font-size: 16px;
	color: #999;
}

/* 个性化服务弹窗样式 */
.personalized-modal {
	width: 320px;
	max-height: 70vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.personalized-header {
	padding: 24px 24px 16px;
	text-align: center;
	border-bottom: 1px solid #f0f0f0;
}

.mascot-icon {
	width: 60px;
	height: 60px;
	margin: 0 auto 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #FFE082 0%, #FFD54F 100%);
	border-radius: 30px;
}

.mascot-emoji {
	font-size: 32px;
}

.personalized-title {
	font-size: 20px;
	font-weight: 600;
	color: #333;
}



.personalized-subtitle {
	font-size: 15px;
	color: #555;
	line-height: 1.6;
	margin-bottom: 20px;
	text-align: center;
}

.feature-list {
	margin-bottom: 20px;
}

.feature-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 12px;
	padding: 8px 0;
}

.feature-icon {
	color: #FFD700;
	font-size: 14px;
	margin-right: 12px;
	margin-top: 2px;
}

.feature-text {
	font-size: 15px;
	color: #555;
	line-height: 1.5;
	flex: 1;
}

.privacy-note {
	font-size: 14px;
	color: #666;
	line-height: 1.6;
	text-align: center;
	background: transparent;
	padding: 12px 0;
	margin-top: 8px;
}



.continue-btn {
	width: 100%;
	height: 48px;
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
	border: none;
	border-radius: 24px;
	font-size: 17px;
	font-weight: 600;
	color: #333;
	box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}
</style>
