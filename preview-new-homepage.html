<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>趣嗒同行 - 全新首页设计预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #333;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: #ffffff;
            border-radius: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .top-nav {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            background: #ffffff;
            border-bottom: 1px solid #f5f5f5;
        }

        .location-area {
            display: flex;
            align-items: center;
            margin-right: 10px;
        }

        .location-icon {
            width: 16px;
            height: 16px;
            margin-right: 4px;
            color: #666;
        }

        .city-text {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .search-container {
            flex: 1;
            display: flex;
            align-items: center;
            background: #f8f8f8;
            border-radius: 25px;
            padding: 8px 12px;
            margin: 0 10px;
        }

        .search-placeholder {
            font-size: 13px;
            color: #999;
            margin-left: 6px;
        }

        .avatar-container {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .main-features {
            display: flex;
            justify-content: space-around;
            padding: 20px 15px;
            background: #ffffff;
        }

        .feature-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            color: white;
            font-size: 24px;
        }

        .group-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .city-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .game-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .feature-text {
            font-size: 13px;
            color: #333;
            font-weight: 500;
        }

        .arc-divider {
            padding: 15px 0;
            display: flex;
            justify-content: center;
        }

        .arc-line {
            width: 60px;
            height: 30px;
            border: 2px solid #f0f0f0;
            border-bottom: none;
            border-radius: 30px 30px 0 0;
        }

        .banner-section {
            margin: 0 15px 15px;
        }

        .banner-swiper {
            height: 160px;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        .notice-section {
            margin: 0 15px 15px;
        }

        .notice-container {
            display: flex;
            align-items: center;
            background: #f8f9ff;
            border-radius: 8px;
            padding: 10px 12px;
            border-left: 3px solid #007AFF;
        }

        .notice-icon {
            margin-right: 8px;
            color: #007AFF;
        }

        .notice-text {
            flex: 1;
            font-size: 13px;
            color: #333;
            line-height: 1.4;
        }

        .hot-activities {
            margin: 0 15px 20px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .section-title {
            font-size: 16px;
            color: #333;
            font-weight: 600;
        }

        .section-more {
            font-size: 12px;
            color: #007AFF;
        }

        .activity-item {
            display: flex;
            background: #ffffff;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
            margin-bottom: 10px;
        }

        .activity-image {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
            margin-right: 10px;
        }

        .activity-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .activity-title {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .activity-desc {
            font-size: 12px;
            color: #666;
            margin-bottom: 6px;
        }

        .activity-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .activity-time {
            font-size: 11px;
            color: #999;
        }

        .activity-participants {
            font-size: 11px;
            color: #007AFF;
        }

        .recommended-partners {
            margin: 0 15px 20px;
        }

        .partner-list {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding-bottom: 5px;
        }

        .partner-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: #ffffff;
            border-radius: 8px;
            padding: 12px 10px;
            min-width: 80px;
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
        }

        .partner-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            margin-bottom: 6px;
        }

        .partner-name {
            font-size: 13px;
            color: #333;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .partner-tags {
            font-size: 11px;
            color: #666;
            text-align: center;
        }

        .title {
            text-align: center;
            margin: 20px 0;
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }

        .description {
            text-align: center;
            margin-bottom: 20px;
            color: #666;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <h1 class="title">🎉 趣嗒同行 - 全新首页设计</h1>
    <p class="description">
        按照京东风格重新设计的现代化首页，简洁、年轻、精美！<br>
        主要功能置顶，半弧分割线，白色背景，无emoji图标，完全符合大厂设计风格！
    </p>

    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>9:41</span>
            <span>🔋 100%</span>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="location-area">
                <div class="location-icon">📍</div>
                <span class="city-text">北海</span>
                <div style="margin-left: 4px; color: #666;">▼</div>
            </div>
            <div class="search-container">
                <div style="color: #999;">🔍</div>
                <span class="search-placeholder">搜索活动、搭子、地点...</span>
            </div>
            <div class="avatar-container"></div>
        </div>

        <!-- 主要功能区 -->
        <div class="main-features">
            <div class="feature-item">
                <div class="feature-icon group-icon">👥</div>
                <span class="feature-text">组局约伴</span>
            </div>
            <div class="feature-item">
                <div class="feature-icon city-icon">🏙️</div>
                <span class="feature-text">城市玩伴</span>
            </div>
            <div class="feature-item">
                <div class="feature-icon game-icon">🎮</div>
                <span class="feature-text">游戏玩伴</span>
            </div>
        </div>

        <!-- 半弧分割线 -->
        <div class="arc-divider">
            <div class="arc-line"></div>
        </div>

        <!-- 轮播横幅 -->
        <div class="banner-section">
            <div class="banner-swiper">
                精彩活动轮播
            </div>
        </div>

        <!-- 公告栏 -->
        <div class="notice-section">
            <div class="notice-container">
                <div class="notice-icon">🔊</div>
                <div class="notice-text">欢迎来到趣嗒同行，发现身边有趣的人和事</div>
                <div style="color: #999; margin-left: 8px;">×</div>
            </div>
        </div>

        <!-- 热门活动 -->
        <div class="hot-activities">
            <div class="section-header">
                <span class="section-title">热门活动</span>
                <span class="section-more">查看更多</span>
            </div>
            <div class="activity-item">
                <div class="activity-image"></div>
                <div class="activity-info">
                    <div class="activity-title">周末海边BBQ</div>
                    <div class="activity-desc">一起来海边烧烤，享受阳光沙滩</div>
                    <div class="activity-meta">
                        <span class="activity-time">今天 14:00</span>
                        <span class="activity-participants">12人参与</span>
                    </div>
                </div>
            </div>
            <div class="activity-item">
                <div class="activity-image" style="background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);"></div>
                <div class="activity-info">
                    <div class="activity-title">王者荣耀五排</div>
                    <div class="activity-desc">寻找靠谱队友，冲击王者</div>
                    <div class="activity-meta">
                        <span class="activity-time">明天 20:00</span>
                        <span class="activity-participants">4人参与</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 推荐搭子 -->
        <div class="recommended-partners">
            <div class="section-header">
                <span class="section-title">推荐搭子</span>
                <span class="section-more">查看更多</span>
            </div>
            <div class="partner-list">
                <div class="partner-item">
                    <div class="partner-avatar"></div>
                    <div class="partner-name">小阳哥</div>
                    <div class="partner-tags">游戏·运动</div>
                </div>
                <div class="partner-item">
                    <div class="partner-avatar" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);"></div>
                    <div class="partner-name">萌萌</div>
                    <div class="partner-tags">美食·旅行</div>
                </div>
                <div class="partner-item">
                    <div class="partner-avatar" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
                    <div class="partner-name">阿强</div>
                    <div class="partner-tags">健身·音乐</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
