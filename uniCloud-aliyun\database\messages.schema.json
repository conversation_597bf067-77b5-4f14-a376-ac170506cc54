{"bsonType": "object", "description": "消息表", "required": ["session_id", "sender_type", "content"], "properties": {"_id": {"description": "消息ID"}, "session_id": {"bsonType": "string", "description": "会话ID", "foreignKey": "customer_service_sessions._id"}, "sender_id": {"bsonType": "string", "description": "发送者ID"}, "sender_type": {"bsonType": "string", "description": "发送者类型", "enum": ["user", "customer_service", "bot"]}, "content": {"bsonType": "string", "description": "消息内容"}, "message_type": {"bsonType": "string", "description": "消息类型", "enum": ["text", "image", "emoji"], "default": "text"}, "is_read": {"bsonType": "bool", "description": "是否已读", "default": false}, "created_at": {"bsonType": "timestamp", "description": "创建时间"}}}