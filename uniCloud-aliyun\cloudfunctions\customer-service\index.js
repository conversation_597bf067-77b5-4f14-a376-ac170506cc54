'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
  console.log('event : ', event);
  
  const { action, params } = event;
  let result = {};
  
  switch (action) {
    // 创建客服会话
    case 'createSession':
      result = await createSession(params);
      break;
      
    // 发送消息
    case 'sendMessage':
      result = await sendMessage(params);
      break;
      
    // 获取消息历史
    case 'getMessages':
      result = await getMessages(params);
      break;
      
    // 获取会话列表
    case 'getSessions':
      result = await getSessions(params);
      break;
      
    // 结束会话
    case 'closeSession':
      result = await closeSession(params);
      break;
      
    default:
      result = {
        code: 403,
        message: '非法访问'
      };
  }
  
  return result;
};

// 创建客服会话
async function createSession(params) {
  try {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const sessionData = {
      _id: sessionId,
      user_id: params.userId,
      customer_service_id: null,
      status: 'waiting',
      created_at: new Date(),
      updated_at: new Date()
    };
    
    await db.collection('customer_service_sessions').add(sessionData);
    
    return {
      code: 0,
      message: '会话创建成功',
      data: { sessionId }
    };
  } catch (error) {
    return {
      code: 500,
      message: '创建会话失败',
      error: error.message
    };
  }
}

// 发送消息
async function sendMessage(params) {
  try {
    const messageData = {
      session_id: params.sessionId,
      sender_id: params.senderId,
      sender_type: params.senderType, // 'user', 'customer_service', 'bot'
      content: params.content,
      message_type: params.messageType || 'text',
      is_read: false,
      created_at: new Date()
    };
    
    const result = await db.collection('messages').add(messageData);
    
    // 更新会话时间
    await db.collection('customer_service_sessions')
      .doc(params.sessionId)
      .update({
        updated_at: new Date()
      });
    
    return {
      code: 0,
      message: '消息发送成功',
      data: result
    };
  } catch (error) {
    return {
      code: 500,
      message: '发送消息失败',
      error: error.message
    };
  }
}

// 获取消息历史
async function getMessages(params) {
  try {
    const { sessionId, page = 1, limit = 20 } = params;
    
    const result = await db.collection('messages')
      .where({
        session_id: sessionId
      })
      .orderBy('created_at', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get();
    
    return {
      code: 0,
      message: '获取消息成功',
      data: result.data.reverse() // 反转数组，使最新消息在底部
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取消息失败',
      error: error.message
    };
  }
}

// 获取会话列表
async function getSessions(params) {
  try {
    const { userId, status } = params;
    
    let whereCondition = { user_id: userId };
    if (status) {
      whereCondition.status = status;
    }
    
    const result = await db.collection('customer_service_sessions')
      .where(whereCondition)
      .orderBy('updated_at', 'desc')
      .get();
    
    return {
      code: 0,
      message: '获取会话列表成功',
      data: result.data
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取会话列表失败',
      error: error.message
    };
  }
}

// 结束会话
async function closeSession(params) {
  try {
    await db.collection('customer_service_sessions')
      .doc(params.sessionId)
      .update({
        status: 'closed',
        updated_at: new Date()
      });
    
    return {
      code: 0,
      message: '会话已结束'
    };
  } catch (error) {
    return {
      code: 500,
      message: '结束会话失败',
      error: error.message
    };
  }
}
