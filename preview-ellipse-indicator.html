<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>半弧椭圆形指示器预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 20px 20px;
            color: white;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        /* 标签导航 */
        .tab-navigation {
            padding: 10px 0;
            background: #fff;
            margin: 10px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        }

        .tab-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: relative;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 5px;
            position: relative;
            flex: 1;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-text {
            font-size: 14px;
            color: #666;
            font-weight: 400;
            transition: all 0.3s ease;
        }

        .tab-item.active .tab-text {
            color: #333;
            font-size: 16px;
            font-weight: 600;
        }

        /* 半弧椭圆形指示器 */
        .tab-indicator {
            position: absolute;
            bottom: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 10px;
            background: linear-gradient(90deg, #BBB2FF 0%, #E3D2FF 50%, #A7C0FF 100%);
            border-radius: 50%;
            clip-path: ellipse(20px 5px at 50% 100%);
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                width: 0;
                opacity: 0;
            }
            to {
                width: 40px;
                opacity: 1;
            }
        }
        
        .content {
            padding: 20px;
            text-align: center;
            color: #666;
        }
        
        /* 形状对比展示 */
        .shape-comparison {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin: 20px 0;
        }
        
        .shape-demo {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .shape-title {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .shape-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 60px;
            background: #fff;
            border-radius: 8px;
            margin-bottom: 10px;
            position: relative;
        }
        
        /* 不同形状的指示器 */
        .indicator-straight {
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, #BBB2FF 0%, #E3D2FF 50%, #A7C0FF 100%);
            border-radius: 1.5px;
        }
        
        .indicator-u-shape {
            width: 40px;
            height: 8px;
            background: linear-gradient(90deg, #BBB2FF 0%, #E3D2FF 50%, #A7C0FF 100%);
            border-radius: 0 0 40px 40px;
        }
        
        .indicator-ellipse {
            width: 40px;
            height: 10px;
            background: linear-gradient(90deg, #BBB2FF 0%, #E3D2FF 50%, #A7C0FF 100%);
            border-radius: 50%;
            clip-path: ellipse(20px 5px at 50% 100%);
        }
        
        .shape-description {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        
        .correct-indicator {
            border: 2px solid #4caf50;
            background: #e8f5e8;
        }
        
        .wrong-indicator {
            border: 2px solid #f44336;
            background: #ffebee;
        }
        
        .demo-note {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.5;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>趣嗒同行</h1>
            <p>半弧椭圆形指示器</p>
        </div>
        
        <!-- 标签导航 -->
        <div class="tab-navigation">
            <div class="tab-container">
                <div class="tab-item active" onclick="switchTab(this, 0)">
                    <span class="tab-text">首页推荐</span>
                    <div class="tab-indicator"></div>
                </div>
                <div class="tab-item" onclick="switchTab(this, 1)">
                    <span class="tab-text">组局约伴</span>
                </div>
                <div class="tab-item" onclick="switchTab(this, 2)">
                    <span class="tab-text">城市玩伴</span>
                </div>
                <div class="tab-item" onclick="switchTab(this, 3)">
                    <span class="tab-text">游戏玩伴</span>
                </div>
            </div>
        </div>
        
        <div class="content">
            <h3 id="content-title">首页推荐</h3>
            <p>当前选中的标签内容</p>
            
            <!-- 形状对比 -->
            <div class="shape-comparison">
                <div class="shape-demo wrong-indicator">
                    <div class="shape-title">❌ 直线形状</div>
                    <div class="shape-container">
                        <div class="indicator-straight"></div>
                    </div>
                    <div class="shape-description">
                        普通的直线指示器，没有弧度
                    </div>
                </div>
                
                <div class="shape-demo wrong-indicator">
                    <div class="shape-title">❌ U型弧度</div>
                    <div class="shape-container">
                        <div class="indicator-u-shape"></div>
                    </div>
                    <div class="shape-description">
                        U型弧度，但不是椭圆形
                    </div>
                </div>
                
                <div class="shape-demo correct-indicator">
                    <div class="shape-title">✅ 半弧椭圆形</div>
                    <div class="shape-container">
                        <div class="indicator-ellipse"></div>
                    </div>
                    <div class="shape-description">
                        正确的半弧椭圆形指示器
                    </div>
                </div>
            </div>
            
            <div class="demo-note">
                <strong>✅ 半弧椭圆形指示器特点：</strong><br>
                • 使用 <code>border-radius: 50%</code> 创建椭圆形<br>
                • 使用 <code>clip-path: ellipse(20px 5px at 50% 100%)</code> 裁剪出下半部分<br>
                • 宽度：40px，高度：10px<br>
                • 椭圆参数：水平半径20px，垂直半径5px<br>
                • 位置：底部中心点（50% 100%）<br>
                • 渐变色：#BBB2FF → #E3D2FF → #A7C0FF
            </div>
        </div>
    </div>

    <script>
        const tabNames = ['首页推荐', '组局约伴', '城市玩伴', '游戏玩伴'];
        
        function switchTab(element, index) {
            // 移除所有active状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
                const indicator = item.querySelector('.tab-indicator');
                if (indicator) {
                    indicator.remove();
                }
            });
            
            // 添加active状态到当前选中的标签
            element.classList.add('active');
            
            // 添加指示器
            const indicator = document.createElement('div');
            indicator.className = 'tab-indicator';
            element.appendChild(indicator);
            
            // 更新内容标题
            document.getElementById('content-title').textContent = tabNames[index];
        }
    </script>
</body>
</html>
