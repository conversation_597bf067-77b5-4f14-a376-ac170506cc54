<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲伴 - VIP等级权益系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 100%);
            min-height: 100vh;
            color: #fff;
        }
        
        .vip-levels-section {
            padding: 12px;
        }
        
        .levels-header {
            text-align: center;
            margin-bottom: 16px;
        }
        
        .levels-title {
            font-size: 18px;
            font-weight: 700;
            color: #FFD700;
            margin-bottom: 6px;
            text-shadow: 0 1px 4px rgba(255, 215, 0, 0.3);
        }
        
        .levels-subtitle {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .level-selector {
            margin-bottom: 16px;
        }
        
        .level-tabs {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 8px;
        }
        
        .level-tab {
            min-width: 100px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 8px;
            padding: 12px 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .level-tab.active {
            border-color: #FFD700;
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
            transform: translateY(-2px);
        }
        
        .level-tab.current::after {
            content: '当前';
            position: absolute;
            top: -4px;
            right: -4px;
            background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
            color: #fff;
            font-size: 9px;
            font-weight: 600;
            padding: 2px 4px;
            border-radius: 4px;
        }
        
        .level-icon {
            width: 28px;
            height: 28px;
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 6px;
            color: white;
            font-size: 14px;
        }
        
        .level-icon.gold {
            background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
        }
        
        .level-icon.diamond {
            background: linear-gradient(135deg, #00BCD4 0%, #26C6DA 100%);
        }
        
        .level-icon.platinum {
            background: linear-gradient(135deg, #9E9E9E 0%, #BDBDBD 100%);
        }
        
        .level-icon.black-gold {
            background: linear-gradient(135deg, #212121 0%, #FFD700 100%);
        }
        
        .level-name {
            font-size: 12px;
            color: #fff;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .level-price {
            font-size: 10px;
            color: #FFD700;
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .level-status .status-text {
            font-size: 9px;
            color: #4CAF50;
            font-weight: 500;
        }
        
        .level-privileges {
            
        }
        
        .privileges-header {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .current-level {
            font-size: 16px;
            color: #FFD700;
            font-weight: 700;
            margin-bottom: 4px;
            text-shadow: 0 1px 4px rgba(255, 215, 0, 0.3);
        }
        
        .level-desc {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.4;
        }
        
        .unlock-status.unlocked .status-text {
            color: #4CAF50;
        }
        
        .unlock-status .status-text {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            font-weight: 600;
        }
        
        .privileges-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .privilege-card {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 8px;
            padding: 10px;
            transition: all 0.3s ease;
        }
        
        .privilege-card.unlocked {
            border-color: rgba(255, 215, 0, 0.5);
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.2);
        }
        
        .privilege-card.locked {
            opacity: 0.6;
            filter: grayscale(0.5);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .privilege-icon {
            width: 20px;
            height: 20px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }
        
        .unlock-badge, .lock-badge {
            width: 12px;
            height: 12px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
        }
        
        .unlock-badge {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
        }
        
        .lock-badge {
            background: rgba(158, 158, 158, 0.2);
            color: #999;
        }
        
        .privilege-name {
            font-size: 13px;
            color: #fff;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .privilege-desc {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.4;
        }
        
        .upgrade-hint {
            background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }
        
        .hint-title {
            font-size: 14px;
            color: #000;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .hint-desc {
            font-size: 11px;
            color: rgba(0, 0, 0, 0.7);
            margin-bottom: 8px;
            line-height: 1.4;
        }
        
        .upgrade-action {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            padding: 6px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upgrade-action:active {
            background: rgba(0, 0, 0, 0.2);
            transform: scale(0.98);
        }
        
        .action-text {
            font-size: 12px;
            color: #000;
            font-weight: 600;
        }
        
        .demo-controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            background: rgba(0, 0, 0, 0.8);
            padding: 8px;
            border-radius: 20px;
        }
        
        .demo-btn {
            padding: 6px 12px;
            background: rgba(255, 215, 0, 0.2);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 12px;
            color: #FFD700;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .demo-btn.active {
            background: #FFD700;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="vip-levels-section">
        <div class="levels-header">
            <div class="levels-title">VIP等级权益</div>
            <div class="levels-subtitle">滑动查看不同等级专享特权</div>
        </div>
        
        <div class="level-selector">
            <div class="level-tabs">
                <div class="level-tab active" data-level="1">
                    <div class="level-icon gold">⭐</div>
                    <div class="level-name">黄金会员</div>
                    <div class="level-price">¥19/月</div>
                </div>
                <div class="level-tab current" data-level="2">
                    <div class="level-icon diamond">💎</div>
                    <div class="level-name">钻石会员</div>
                    <div class="level-price">¥39/月</div>
                    <div class="level-status">
                        <div class="status-text">已解锁</div>
                    </div>
                </div>
                <div class="level-tab" data-level="3">
                    <div class="level-icon platinum">👑</div>
                    <div class="level-name">白金会员</div>
                    <div class="level-price">¥69/月</div>
                </div>
                <div class="level-tab" data-level="4">
                    <div class="level-icon black-gold">💼</div>
                    <div class="level-name">黑金会员</div>
                    <div class="level-price">¥99/月</div>
                </div>
            </div>
        </div>
        
        <div class="level-privileges">
            <div class="privileges-header">
                <div class="header-info">
                    <div class="current-level">黄金会员</div>
                    <div class="level-desc">入门级会员，享受基础特权</div>
                </div>
                <div class="unlock-status unlocked">
                    <div class="status-text">您已获得以下权益</div>
                </div>
            </div>
            
            <div class="privileges-grid" id="privilegesGrid">
                <!-- 权益卡片将通过JavaScript动态生成 -->
            </div>
            
            <div class="upgrade-hint" id="upgradeHint" style="display: none;">
                <div class="hint-content">
                    <div class="hint-title">升级至钻石会员</div>
                    <div class="hint-desc">解锁更多专属权益，享受尊贵体验</div>
                    <div class="upgrade-action">
                        <span class="action-text">立即升级</span>
                        <span>›</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="demo-controls">
        <div class="demo-btn active" data-user-level="2">当前用户VIP2</div>
        <div class="demo-btn" data-user-level="0">模拟普通用户</div>
        <div class="demo-btn" data-user-level="4">模拟VIP4用户</div>
    </div>
    
    <script>
        const levelPrivileges = {
            1: [
                { name: '专属身份标识', desc: '个人资料显示黄金VIP标识', icon: '⭐', theme: 'gold' },
                { name: '优先匹配推荐', desc: '优先推荐给其他用户', icon: '🔥', theme: 'gold' },
                { name: '去除广告', desc: '享受无广告纯净体验', icon: '❌', theme: 'gold' },
                { name: '基础筛选', desc: '使用基础筛选功能', icon: '🔍', theme: 'gold' }
            ],
            2: [
                { name: '无限点赞特权', desc: '每日点赞次数无限制', icon: '❤️', theme: 'diamond' },
                { name: '高级筛选功能', desc: '更多筛选条件选择', icon: '🔧', theme: 'diamond' },
                { name: '专属客服通道', desc: '优先客服支持服务', icon: '🎧', theme: 'diamond' },
                { name: '消息已读显示', desc: '查看消息已读状态', icon: '✅', theme: 'diamond' }
            ],
            3: [
                { name: '数据统计分析', desc: '详细的个人数据报告', icon: '📊', theme: 'platinum' },
                { name: '专属活动邀请', desc: '优先参与线下活动', icon: '📅', theme: 'platinum' },
                { name: '隐身浏览模式', desc: '匿名浏览他人资料', icon: '👻', theme: 'platinum' },
                { name: '超级曝光', desc: '获得更多展示机会', icon: '📈', theme: 'platinum' }
            ],
            4: [
                { name: '专属管家服务', desc: '一对一专属管家服务', icon: '👨‍💼', theme: 'black-gold' },
                { name: '定制化推荐', desc: 'AI智能个性化推荐', icon: '🤖', theme: 'black-gold' },
                { name: '线下活动优先', desc: '所有活动优先参与权', icon: '🎪', theme: 'black-gold' },
                { name: '至尊身份标识', desc: '独一无二的黑金标识', icon: '👑', theme: 'black-gold' }
            ]
        };
        
        const levelInfo = {
            1: { name: '黄金会员', desc: '入门级会员，享受基础特权' },
            2: { name: '钻石会员', desc: '进阶会员，解锁更多功能' },
            3: { name: '白金会员', desc: '高级会员，尊享专属服务' },
            4: { name: '黑金会员', desc: '顶级会员，至尊无上体验' }
        };
        
        let currentUserLevel = 2;
        let selectedLevel = 1;
        
        function updatePrivileges() {
            const privileges = levelPrivileges[selectedLevel];
            const grid = document.getElementById('privilegesGrid');
            const header = document.querySelector('.privileges-header');
            const upgradeHint = document.getElementById('upgradeHint');
            
            // 更新头部信息
            const levelNameEl = header.querySelector('.current-level');
            const levelDescEl = header.querySelector('.level-desc');
            const statusEl = header.querySelector('.unlock-status .status-text');
            
            levelNameEl.textContent = levelInfo[selectedLevel].name;
            levelDescEl.textContent = levelInfo[selectedLevel].desc;
            
            const isUnlocked = currentUserLevel >= selectedLevel;
            statusEl.textContent = isUnlocked ? '您已获得以下权益' : '您暂未解锁该等级';
            header.querySelector('.unlock-status').className = `unlock-status ${isUnlocked ? 'unlocked' : ''}`;
            
            // 更新权益卡片
            grid.innerHTML = privileges.map(privilege => `
                <div class="privilege-card ${isUnlocked ? 'unlocked' : 'locked'}">
                    <div class="card-header">
                        <div class="privilege-icon ${privilege.theme}">${privilege.icon}</div>
                        <div class="${isUnlocked ? 'unlock-badge' : 'lock-badge'}">
                            ${isUnlocked ? '✓' : '🔒'}
                        </div>
                    </div>
                    <div class="privilege-content">
                        <div class="privilege-name">${privilege.name}</div>
                        <div class="privilege-desc">${privilege.desc}</div>
                    </div>
                </div>
            `).join('');
            
            // 显示/隐藏升级提示
            upgradeHint.style.display = isUnlocked ? 'none' : 'block';
            if (!isUnlocked) {
                upgradeHint.querySelector('.hint-title').textContent = `升级至${levelInfo[selectedLevel].name}`;
            }
        }
        
        // 等级选择
        document.querySelectorAll('.level-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.level-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                selectedLevel = parseInt(tab.dataset.level);
                updatePrivileges();
            });
        });
        
        // 用户等级切换
        document.querySelectorAll('.demo-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.demo-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                currentUserLevel = parseInt(btn.dataset.userLevel);
                updatePrivileges();
            });
        });
        
        // 初始化
        updatePrivileges();
    </script>
</body>
</html>
