{"easycom": {"autoscan": true, "custom": {"^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"}}, "pages": [{"path": "pages/splash/splash", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/index/index", "style": {"navigationStyle": "custom", "enablePullDownRefresh": false, "onReachBottomDistance": 50, "app-plus": {"titleNView": false}}}, {"path": "pages/discover/discover", "style": {"navigationStyle": "custom", "enablePullDownRefresh": true, "onReachBottomDistance": 50, "app-plus": {"titleNView": false}}}, {"path": "pages/messages/messages", "style": {"navigationBarTitleText": "消息"}}, {"path": "pages/profile/profile", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/test-cloud/test-cloud", "style": {"navigationBarTitleText": "云函数测试"}}, {"path": "pages/city-select/city-select", "style": {"navigationStyle": "custom"}}, {"path": "pages/privacy-policy/privacy-policy", "style": {"navigationStyle": "custom"}}, {"path": "pages/user-agreement/user-agreement", "style": {"navigationStyle": "custom"}}, {"path": "pages/auth/auth", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/auth/phone-register/phone-register", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/auth/verify-code/verify-code", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/auth/login/login", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/auth/phone-login/phone-login", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/auth/forgot-password/forgot-password", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/auth/forgot-password-help/forgot-password-help", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-setup/profile-setup", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/publish/publish", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/verification/verification", "style": {"navigationBarTitleText": "实名认证"}}, {"path": "pages/profile-features/level/level", "style": {"navigationBarTitleText": "等级中心"}}, {"path": "pages/test-avatar/test-avatar", "style": {"navigationBarTitleText": "头像测试"}}, {"path": "pages/community-rules/community-rules", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/community-rules/community-covenant", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/community-rules/punishment-rules", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/community-rules/recommendation-rules", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/community-rules/content-standards", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/community-rules/advertising-service", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/search/search", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/search-result/search-result", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/activity-list/activity-list", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/category/category", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/followers/followers", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/following/following", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/likes/likes", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/works/works", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/coupons/coupons", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/mall/mall", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/my-events/my-events", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/my-works/my-works", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/my-favorites/my-favorites", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/settings/settings", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/help/help", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/about/about", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/creator-center/creator-center", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}, {"path": "pages/profile-features/coming-soon/coming-soon", "style": {"navigationStyle": "custom", "app-plus": {"titleNView": false}}}], "globalStyle": {"navigationBarTextStyle": "white", "navigationBarTitleText": "趣嗒同行", "navigationBarBackgroundColor": "#6F7BF5", "backgroundColor": "#F8F9FA", "app-plus": {"background": "#F8F9FA"}, "h5": {"titleNView": false}}, "tabBar": {"color": "#999999", "selectedColor": "#66D4C8", "backgroundColor": "#FFFFFF", "borderStyle": "black", "fontSize": "14px", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "static/tabbar/home.png", "selectedIconPath": "static/tabbar/home-selected.png"}, {"pagePath": "pages/discover/discover", "text": "发现", "iconPath": "static/tabbar/discover.png", "selectedIconPath": "static/tabbar/discover-selected.png"}, {"pagePath": "pages/messages/messages", "text": "消息", "iconPath": "static/tabbar/messages.png", "selectedIconPath": "static/tabbar/messages-selected.png"}, {"pagePath": "pages/profile/profile", "text": "我的", "iconPath": "static/tabbar/profile.png", "selectedIconPath": "static/tabbar/profile-selected.png"}]}, "uniIdRouter": {}, "condition": {"current": 0, "list": [{"name": "首页", "path": "pages/index/index"}]}}