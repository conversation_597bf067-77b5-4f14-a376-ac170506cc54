/**
 * 响应式适配工具
 * 解决不同屏幕尺寸的适配问题
 */

// 设计稿基准尺寸 (iPhone 6/7/8)
const DESIGN_WIDTH = 375;
const DESIGN_HEIGHT = 667;

// 获取系统信息
let systemInfo = null;

/**
 * 获取系统信息
 */
export function getSystemInfo() {
	if (!systemInfo) {
		try {
			systemInfo = uni.getSystemInfoSync();
		} catch (e) {
			console.error('获取系统信息失败:', e);
			// 设置默认值
			systemInfo = {
				screenWidth: 375,
				screenHeight: 667,
				statusBarHeight: 20,
				platform: 'unknown'
			};
		}
	}
	return systemInfo;
}

/**
 * rpx转px
 * @param {number} rpx rpx值
 * @returns {number} px值
 */
export function rpxToPx(rpx) {
	const info = getSystemInfo();
	return (rpx * info.screenWidth) / 750;
}

/**
 * px转rpx
 * @param {number} px px值
 * @returns {number} rpx值
 */
export function pxToRpx(px) {
	const info = getSystemInfo();
	return (px * 750) / info.screenWidth;
}

/**
 * 获取屏幕宽度比例
 * @returns {number} 宽度比例
 */
export function getWidthRatio() {
	const info = getSystemInfo();
	return info.screenWidth / DESIGN_WIDTH;
}

/**
 * 获取屏幕高度比例
 * @returns {number} 高度比例
 */
export function getHeightRatio() {
	const info = getSystemInfo();
	return info.screenHeight / DESIGN_HEIGHT;
}

/**
 * 获取状态栏高度
 * @returns {number} 状态栏高度(px)
 */
export function getStatusBarHeight() {
	const info = getSystemInfo();
	return info.statusBarHeight || 20;
}

/**
 * 获取导航栏高度
 * @returns {number} 导航栏高度(px)
 */
export function getNavBarHeight() {
	const info = getSystemInfo();
	const statusBarHeight = info.statusBarHeight || 20;
	
	// 不同平台的导航栏高度
	let navBarHeight = 44; // 默认iOS高度
	
	if (info.platform === 'android') {
		navBarHeight = 48;
	} else if (info.platform === 'devtools') {
		navBarHeight = 42;
	}
	
	return statusBarHeight + navBarHeight;
}

/**
 * 获取底部安全区域高度
 * @returns {number} 底部安全区域高度(px)
 */
export function getSafeAreaBottom() {
	const info = getSystemInfo();
	if (info.safeAreaInsets) {
		return info.safeAreaInsets.bottom || 0;
	}
	
	// iPhone X系列的底部安全区域
	if (info.model && info.model.includes('iPhone')) {
		if (info.screenHeight >= 812) {
			return 34;
		}
	}
	
	return 0;
}

/**
 * 判断是否为小屏设备
 * @returns {boolean} 是否为小屏设备
 */
export function isSmallScreen() {
	const info = getSystemInfo();
	return info.screenWidth <= 320 || info.screenHeight <= 568;
}

/**
 * 判断是否为大屏设备
 * @returns {boolean} 是否为大屏设备
 */
export function isLargeScreen() {
	const info = getSystemInfo();
	return info.screenWidth >= 414 || info.screenHeight >= 896;
}

/**
 * 获取适配后的尺寸
 * @param {number} size 原始尺寸
 * @param {string} type 适配类型: 'width' | 'height' | 'font' | 'auto'
 * @returns {number} 适配后的尺寸
 */
export function getAdaptiveSize(size, type = 'auto') {
	const widthRatio = getWidthRatio();
	const heightRatio = getHeightRatio();
	
	switch (type) {
		case 'width':
			return size * widthRatio;
		case 'height':
			return size * heightRatio;
		case 'font':
			// 字体大小使用较小的比例，避免过大
			return size * Math.min(widthRatio, 1.2);
		case 'auto':
		default:
			// 自动选择合适的比例
			return size * Math.min(widthRatio, heightRatio);
	}
}

/**
 * 获取响应式样式对象
 * @param {Object} styles 样式对象
 * @returns {Object} 响应式样式对象
 */
export function getResponsiveStyles(styles) {
	const info = getSystemInfo();
	const isSmall = isSmallScreen();
	const isLarge = isLargeScreen();
	
	const responsiveStyles = { ...styles };
	
	// 小屏适配
	if (isSmall && styles.small) {
		Object.assign(responsiveStyles, styles.small);
	}
	
	// 大屏适配
	if (isLarge && styles.large) {
		Object.assign(responsiveStyles, styles.large);
	}
	
	// 平台适配
	if (styles[info.platform]) {
		Object.assign(responsiveStyles, styles[info.platform]);
	}
	
	return responsiveStyles;
}

/**
 * 获取安全区域样式
 * @returns {Object} 安全区域样式
 */
export function getSafeAreaStyles() {
	const statusBarHeight = getStatusBarHeight();
	const safeAreaBottom = getSafeAreaBottom();
	
	return {
		paddingTop: `${statusBarHeight}px`,
		paddingBottom: `${safeAreaBottom}px`
	};
}

/**
 * 获取页面容器样式
 * @param {Object} options 配置选项
 * @returns {Object} 页面容器样式
 */
export function getPageContainerStyles(options = {}) {
	const {
		includeStatusBar = true,
		includeSafeArea = true,
		backgroundColor = '#f5f5f5'
	} = options;
	
	const styles = {
		minHeight: '100vh',
		backgroundColor,
		display: 'flex',
		flexDirection: 'column'
	};
	
	if (includeStatusBar) {
		styles.paddingTop = `${getStatusBarHeight()}px`;
	}
	
	if (includeSafeArea) {
		styles.paddingBottom = `${getSafeAreaBottom()}px`;
	}
	
	return styles;
}

/**
 * 获取卡片样式
 * @param {Object} options 配置选项
 * @returns {Object} 卡片样式
 */
export function getCardStyles(options = {}) {
	const {
		margin = '16rpx',
		padding = '24rpx',
		borderRadius = '16rpx',
		shadow = true
	} = options;
	
	const styles = {
		background: '#fff',
		margin,
		padding,
		borderRadius,
		overflow: 'hidden'
	};
	
	if (shadow) {
		styles.boxShadow = '0 4rpx 20rpx rgba(0, 0, 0, 0.08)';
	}
	
	// 小屏适配
	if (isSmallScreen()) {
		styles.margin = '12rpx';
		styles.padding = '20rpx';
		styles.borderRadius = '12rpx';
	}
	
	return styles;
}

/**
 * 获取按钮样式
 * @param {Object} options 配置选项
 * @returns {Object} 按钮样式
 */
export function getButtonStyles(options = {}) {
	const {
		size = 'medium',
		type = 'primary'
	} = options;
	
	const sizeMap = {
		small: {
			padding: '12rpx 24rpx',
			fontSize: '24rpx',
			borderRadius: '20rpx'
		},
		medium: {
			padding: '16rpx 32rpx',
			fontSize: '28rpx',
			borderRadius: '24rpx'
		},
		large: {
			padding: '20rpx 48rpx',
			fontSize: '32rpx',
			borderRadius: '32rpx'
		}
	};
	
	const typeMap = {
		primary: {
			background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
			color: '#fff'
		},
		secondary: {
			background: '#f8f9fa',
			color: '#666',
			border: '1rpx solid #e9ecef'
		}
	};
	
	const styles = {
		...sizeMap[size],
		...typeMap[type],
		transition: 'all 0.3s ease',
		display: 'flex',
		alignItems: 'center',
		justifyContent: 'center'
	};
	
	// 小屏适配
	if (isSmallScreen()) {
		styles.fontSize = getAdaptiveSize(parseInt(styles.fontSize), 'font') + 'rpx';
		styles.padding = styles.padding.replace(/\d+/g, (match) => {
			return Math.max(parseInt(match) * 0.8, 8);
		});
	}
	
	return styles;
}

// 导出默认对象
export default {
	getSystemInfo,
	rpxToPx,
	pxToRpx,
	getWidthRatio,
	getHeightRatio,
	getStatusBarHeight,
	getNavBarHeight,
	getSafeAreaBottom,
	isSmallScreen,
	isLargeScreen,
	getAdaptiveSize,
	getResponsiveStyles,
	getSafeAreaStyles,
	getPageContainerStyles,
	getCardStyles,
	getButtonStyles
};
