<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>黄绿主题色预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 50%, #7fcdcd 100%);
            padding: 40px 20px 20px;
            color: white;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        /* 顶部导航区 - 新的黄绿渐变 */
        .top-nav {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 50%, #7fcdcd 100%);
            color: white;
        }
        
        .location-area {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .city-text {
            font-size: 16px;
            font-weight: 500;
        }
        
        .search-container {
            flex: 1;
            margin: 0 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .avatar-container {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 标签导航 - 全屏贴合 */
        .tab-navigation {
            padding: 10px 0;
            background: #fff;
            margin: 0;
            border-radius: 0;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
            border-bottom: 1px solid #f0f0f0;
        }

        .tab-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: relative;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 5px;
            position: relative;
            flex: 1;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-text {
            font-size: 14px;
            color: #666;
            font-weight: 400;
            transition: all 0.3s ease;
        }

        .tab-item.active .tab-text {
            color: #333;
            font-size: 16px;
            font-weight: 600;
        }

        /* 新的黄绿渐变指示器 */
        .tab-indicator {
            position: absolute;
            bottom: 0px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 10px;
            overflow: hidden;
            animation: fadeInUp 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .tab-indicator::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 20px;
            background: linear-gradient(90deg, #a8e6cf 0%, #88d8a3 50%, #7fcdcd 100%);
            border-radius: 50%;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(5px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0) scale(1);
            }
        }
        
        .content {
            padding: 20px;
            color: #666;
        }
        
        /* 热门活动区域 */
        .hot-activities {
            margin-bottom: 20px;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .section-title-container {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .section-title-icon {
            font-size: 18px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }
        
        .section-title {
            font-size: 18px;
            color: #333;
            font-weight: 600;
        }
        
        .section-more {
            font-size: 14px;
            color: #4CAF50;
            cursor: pointer;
        }
        
        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .activity-item {
            display: flex;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            border-left: 4px solid #4CAF50;
        }
        
        .activity-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            margin-right: 15px;
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
        }
        
        .activity-info {
            flex: 1;
        }
        
        .activity-title {
            font-size: 16px;
            color: #333;
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .activity-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .activity-meta {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #999;
        }
        
        .activity-participants {
            color: #4CAF50;
            font-weight: 500;
        }
        
        /* 推荐搭子区域 */
        .recommended-partners {
            margin-bottom: 20px;
        }
        
        .partner-list {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding-bottom: 10px;
        }
        
        .partner-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 80px;
            cursor: pointer;
        }
        
        .partner-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
        }
        
        .partner-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .partner-tags {
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        
        /* 广告banner */
        .ad-banner-section {
            margin: 0 auto 20px;
            padding: 0 15px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
        }

        .ad-banner-img {
            max-width: 80%;
            height: auto;
            border-radius: 8px;
            transition: transform 0.2s ease;
            cursor: pointer;
            display: block;
            margin: 0 auto;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .ad-banner-img:hover {
            transform: scale(1.02);
        }

        .ad-banner-img:active {
            transform: scale(0.98);
        }
        
        .demo-note {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.5;
            text-align: left;
        }
        
        .theme-comparison {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 20px 0;
        }
        
        .theme-demo {
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .theme-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .old-theme {
            border: 2px solid #ccccff;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .new-theme {
            border: 2px solid #ccffcc;
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 50%, #7fcdcd 100%);
            color: white;
        }
        
        .theme-desc {
            font-size: 12px;
            line-height: 1.4;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>趣嗒同行</h1>
            <p>🌿 黄绿主题色预览 🌿</p>
        </div>
        
        <!-- 顶部导航区 -->
        <div class="top-nav">
            <div class="location-area">
                <span class="city-text">📍 北海</span>
            </div>
            <div class="search-container">
                搜索活动、搭子、地点...
            </div>
            <div class="avatar-container">
                👤
            </div>
        </div>
        
        <!-- 标签导航 -->
        <div class="tab-navigation">
            <div class="tab-container">
                <div class="tab-item active" onclick="switchTab(this, 0)">
                    <span class="tab-text">首页推荐</span>
                    <div class="tab-indicator"></div>
                </div>
                <div class="tab-item" onclick="switchTab(this, 1)">
                    <span class="tab-text">组局约伴</span>
                </div>
                <div class="tab-item" onclick="switchTab(this, 2)">
                    <span class="tab-text">城市玩伴</span>
                </div>
                <div class="tab-item" onclick="switchTab(this, 3)">
                    <span class="tab-text">游戏玩伴</span>
                </div>
            </div>
        </div>
        
        <div class="content">
            <!-- 热门活动区域 -->
            <div class="hot-activities">
                <div class="section-header">
                    <div class="section-title-container">
                        <span class="section-title-icon">🔥</span>
                        <span class="section-title">热门活动</span>
                    </div>
                    <span class="section-more">查看更多</span>
                </div>
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-image"></div>
                        <div class="activity-info">
                            <div class="activity-title">🏕️ 周末露营BBQ</div>
                            <div class="activity-desc">一起来户外露营，享受大自然</div>
                            <div class="activity-meta">
                                <span>📍 银滩</span>
                                <span>⏰ 明天 18:00</span>
                                <span class="activity-participants">👥 6人参与</span>
                            </div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-image"></div>
                        <div class="activity-info">
                            <div class="activity-title">🚴‍♀️ 城市骑行探索</div>
                            <div class="activity-desc">骑行穿越城市，发现美好角落</div>
                            <div class="activity-meta">
                                <span>📍 老街</span>
                                <span>⏰ 周六 09:00</span>
                                <span class="activity-participants">👥 8人参与</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 推荐搭子区域 -->
            <div class="recommended-partners">
                <div class="section-header">
                    <div class="section-title">推荐搭子</div>
                    <div class="section-more">查看更多</div>
                </div>
                <div class="partner-list">
                    <div class="partner-item">
                        <div class="partner-avatar"></div>
                        <div class="partner-name">户外小王</div>
                        <div class="partner-tags">露营 · 徒步</div>
                    </div>
                    <div class="partner-item">
                        <div class="partner-avatar"></div>
                        <div class="partner-name">旅行小美</div>
                        <div class="partner-tags">旅游 · 摄影</div>
                    </div>
                    <div class="partner-item">
                        <div class="partner-avatar"></div>
                        <div class="partner-name">骑行阿强</div>
                        <div class="partner-tags">骑行 · 运动</div>
                    </div>
                </div>
            </div>
            
            <!-- 广告banner -->
            <div class="ad-banner-section">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQwIiBoZWlnaHQ9IjgwIiB2aWV3Qm94PSIwIDAgMjQwIDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMjQwIiBoZWlnaHQ9IjgwIiByeD0iNDAiIGZpbGw9InVybCgjZ3JhZGllbnQwXzEyXzMpIiBmaWxsLW9wYWNpdHk9IjAuOSIvPgo8dGV4dCB4PSIxMjAiIHk9IjQ1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+8J+MvyDmiLflpJbmtLvliqgg4oCiIOWPkeeOsOiHqueFtuS5kOi2oyAg8J+YjDwvdGV4dD4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQwXzEyXzMiIHgxPSIwIiB5MT0iMCIgeDI9IjI0MCIgeTI9IjgwIj4KPHN0b3Agc3RvcC1jb2xvcj0iI2E4ZTZjZiIvPgo8c3RvcCBvZmZzZXQ9IjAuNSIgc3RvcC1jb2xvcj0iIzg4ZDhhMyIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM3ZmNkY2QiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K" class="ad-banner-img" alt="户外活动广告" onclick="showAdClick()">
            </div>
            
            <!-- 主题对比 -->
            <div class="theme-comparison">
                <div class="theme-demo old-theme">
                    <div class="theme-title">❌ 之前：蓝紫渐变</div>
                    <div class="theme-desc">
                        科技感强，但不够贴合户外旅游主题
                    </div>
                </div>
                
                <div class="theme-demo new-theme">
                    <div class="theme-title">✅ 现在：黄绿渐变</div>
                    <div class="theme-desc">
                        自然清新，完美契合户外、旅游、露营主题
                    </div>
                </div>
            </div>
            
            <div class="demo-note">
                <strong>🌿 主题色优化完成：</strong><br>
                • <strong>顶部导航</strong>：黄绿渐变 (#a8e6cf → #88d8a3 → #7fcdcd)<br>
                • <strong>标签指示器</strong>：同样的黄绿渐变<br>
                • <strong>强调色</strong>：绿色 (#4CAF50) 替代蓝色<br>
                • <strong>主题契合度</strong>：完美匹配户外、旅游、露营场景<br>
                • <strong>视觉感受</strong>：自然、清新、年轻、活力
            </div>
        </div>
    </div>

    <script>
        const tabNames = ['首页推荐', '组局约伴', '城市玩伴', '游戏玩伴'];
        
        function switchTab(element, index) {
            // 移除所有active状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
                const indicator = item.querySelector('.tab-indicator');
                if (indicator) {
                    indicator.remove();
                }
            });
            
            // 添加active状态到当前选中的标签
            element.classList.add('active');
            
            // 添加指示器
            const indicator = document.createElement('div');
            indicator.className = 'tab-indicator';
            element.appendChild(indicator);
        }
        
        function showAdClick() {
            alert('户外活动广告点击！');
        }
    </script>
</body>
</html>
