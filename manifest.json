{"name": "趣嗒同行", "appid": "__UNI__6AF0E4D", "description": "趣嗒同行 - 年轻人的组局搭子社交平台", "versionName": "1.0.0", "versionCode": "100", "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "uniCloud": {"provider": "<PERSON><PERSON><PERSON>", "spaceId": "mp-4f58ec35-fd98-402d-99e4-2bb423eaf604", "clientSecret": "pZBK1la3eLXxOMrhsDatXg=="}, "vueVersion": "3", "app": {"distribute": {"android": {"packagename": "com.planet.app", "permissions": ["<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"], "targetSdkVersion": 30, "minSdkVersion": 21, "usesCleartextTraffic": true}, "ios": {}, "sdkConfigs": {}, "icons": {"android": {"hdpi": "static/app-logo-new.png", "xhdpi": "static/app-logo-new.png", "xxhdpi": "static/app-logo-new.png", "xxxhdpi": "static/app-logo-new.png"}, "ios": {"appstore": "static/app-logo-new.png", "ipad": {"app": "static/app-logo-new.png", "app@2x": "static/app-logo-new.png"}, "iphone": {"app@2x": "static/app-logo-new.png", "app@3x": "static/app-logo-new.png"}}}}}, "app-plus": {"splashscreen": {"alwaysShowBeforeRender": true, "autoclose": true, "waiting": true, "delay": 2000, "target": "id:splash"}, "networkTimeout": {"request": 60000, "connectSocket": 60000, "uploadFile": 60000, "downloadFile": 60000}, "distribute": {"ios": {"dSYMs": false}, "icons": {"android": {"hdpi": "static/app-logo-new.png", "xhdpi": "static/app-logo-new.png", "xxhdpi": "static/app-logo-new.png", "xxxhdpi": "static/app-logo-new.png"}, "ios": {"appstore": "static/app-logo-new.png"}}}}}