<template>
	<view class="activity-list-page">
		<!-- 状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left" @click="goBack">
				<uni-icons type="left" size="24" color="#333"></uni-icons>
				<text class="back-text">返回</text>
			</view>
			<text class="nav-title">{{ subCategory }}</text>
			<view class="nav-right">
				<view class="search-btn" @click="goToSearch">
					<uni-icons type="search" size="20" color="#333"></uni-icons>
				</view>
			</view>
		</view>
		
		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="filter-item" @click="showLocationFilter">
				<text class="filter-text">{{ selectedLocation }}</text>
				<uni-icons type="down" size="14" color="#999"></uni-icons>
			</view>
			<view class="filter-item" @click="showTimeFilter">
				<text class="filter-text">{{ selectedTime }}</text>
				<uni-icons type="down" size="14" color="#999"></uni-icons>
			</view>
			<view class="filter-item" @click="showPriceFilter">
				<text class="filter-text">{{ selectedPrice }}</text>
				<uni-icons type="down" size="14" color="#999"></uni-icons>
			</view>
			<view class="sort-btn" @click="showSortMenu">
				<uni-icons type="funnel" size="16" color="#FFD700"></uni-icons>
			</view>
		</view>
		
		<!-- 活动列表 -->
		<scroll-view class="activity-scroll" scroll-y="true" @scrolltolower="loadMore">
			<view class="activity-list">
				<view 
					v-for="activity in activityList" 
					:key="activity.id"
					class="activity-card"
					@click="viewActivity(activity)"
				>
					<view class="card-header">
						<image class="activity-image" :src="activity.image" mode="aspectFill"></image>
						<view class="activity-tags">
							<text v-if="activity.isHot" class="tag hot">热门</text>
							<text v-if="activity.isNew" class="tag new">新活动</text>
						</view>
					</view>
					<view class="card-content">
						<text class="activity-title">{{ activity.title }}</text>
						<view class="activity-info">
							<view class="info-item">
								<uni-icons type="location" size="14" color="#999"></uni-icons>
								<text class="info-text">{{ activity.location }}</text>
							</view>
							<view class="info-item">
								<uni-icons type="calendar" size="14" color="#999"></uni-icons>
								<text class="info-text">{{ activity.time }}</text>
							</view>
						</view>
						<view class="activity-footer">
							<view class="participants">
								<view class="avatar-group">
									<image 
										v-for="(avatar, index) in activity.participants.slice(0, 3)" 
										:key="index"
										class="participant-avatar" 
										:src="avatar" 
										mode="aspectFill"
									></image>
								</view>
								<text class="participant-count">{{ activity.participants.length }}人参与</text>
							</view>
							<view class="price-section">
								<text class="price">{{ activity.price }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view v-if="loading" class="loading-more">
				<uni-icons type="spinner-cycle" size="20" color="#999"></uni-icons>
				<text class="loading-text">加载中...</text>
			</view>
			
			<!-- 没有更多 -->
			<view v-if="noMore" class="no-more">
				<text class="no-more-text">没有更多活动了</text>
			</view>
		</scroll-view>
		
		<!-- 发布按钮 -->
		<view class="fab-btn" @click="publishActivity">
			<uni-icons type="plus" size="24" color="#fff"></uni-icons>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				category: '',
				subCategory: '',
				
				// 筛选条件
				selectedLocation: '全部地区',
				selectedTime: '全部时间',
				selectedPrice: '全部价格',
				
				// 活动列表
				activityList: [],
				loading: false,
				noMore: false,
				page: 1,
				
				// 模拟数据
				mockActivities: [
					{
						id: 1,
						title: '周末户外BBQ聚会',
						location: '奥林匹克森林公园',
						time: '周六 14:00-18:00',
						price: '￥68/人',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						participants: [
							'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
							'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
							'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
							'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						],
						isHot: true,
						isNew: false
					},
					{
						id: 2,
						title: '夜跑团队训练',
						location: '朝阳公园',
						time: '今晚 19:00-21:00',
						price: '免费',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						participants: [
							'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
							'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						],
						isHot: false,
						isNew: true
					},
					{
						id: 3,
						title: '王者荣耀五排开黑',
						location: '线上活动',
						time: '今晚 20:00-24:00',
						price: '免费',
						image: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						participants: [
							'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
							'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
							'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'
						],
						isHot: true,
						isNew: false
					}
				]
			}
		},

		onLoad(options) {
			// 获取状态栏高度
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight;
			
			// 获取传递的参数
			if (options.category) {
				this.category = options.category;
			}
			if (options.subCategory) {
				this.subCategory = options.subCategory;
			}
			
			// 加载活动列表
			this.loadActivityList();
		},

		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 跳转到搜索
			goToSearch() {
				uni.navigateTo({
					url: '/pages/search/search'
				});
			},

			// 显示地区筛选
			showLocationFilter() {
				uni.showActionSheet({
					itemList: ['全部地区', '朝阳区', '海淀区', '西城区', '东城区', '丰台区'],
					success: (res) => {
						const locations = ['全部地区', '朝阳区', '海淀区', '西城区', '东城区', '丰台区'];
						this.selectedLocation = locations[res.tapIndex];
						this.filterActivities();
					}
				});
			},

			// 显示时间筛选
			showTimeFilter() {
				uni.showActionSheet({
					itemList: ['全部时间', '今天', '明天', '本周末', '下周'],
					success: (res) => {
						const times = ['全部时间', '今天', '明天', '本周末', '下周'];
						this.selectedTime = times[res.tapIndex];
						this.filterActivities();
					}
				});
			},

			// 显示价格筛选
			showPriceFilter() {
				uni.showActionSheet({
					itemList: ['全部价格', '免费', '50元以下', '50-100元', '100元以上'],
					success: (res) => {
						const prices = ['全部价格', '免费', '50元以下', '50-100元', '100元以上'];
						this.selectedPrice = prices[res.tapIndex];
						this.filterActivities();
					}
				});
			},

			// 显示排序菜单
			showSortMenu() {
				uni.showActionSheet({
					itemList: ['默认排序', '最新发布', '价格从低到高', '价格从高到低', '参与人数'],
					success: (res) => {
						console.log('排序方式:', res.tapIndex);
						this.sortActivities(res.tapIndex);
					}
				});
			},

			// 加载活动列表
			loadActivityList() {
				this.loading = true;
				
				// 模拟网络请求
				setTimeout(() => {
					this.activityList = [...this.mockActivities];
					this.loading = false;
				}, 1000);
			},

			// 筛选活动
			filterActivities() {
				console.log('筛选条件:', {
					location: this.selectedLocation,
					time: this.selectedTime,
					price: this.selectedPrice
				});
				// 这里可以根据筛选条件重新加载数据
			},

			// 排序活动
			sortActivities(sortType) {
				console.log('排序类型:', sortType);
				// 这里可以根据排序类型重新排序数据
			},

			// 加载更多
			loadMore() {
				if (this.loading || this.noMore) return;
				
				this.loading = true;
				this.page++;
				
				// 模拟加载更多数据
				setTimeout(() => {
					if (this.page > 3) {
						this.noMore = true;
					} else {
						// 添加更多数据
						this.activityList.push(...this.mockActivities);
					}
					this.loading = false;
				}, 1000);
			},

			// 查看活动详情
			viewActivity(activity) {
				console.log('查看活动详情:', activity.title);
				// 跳转到活动详情页
			},

			// 发布活动
			publishActivity() {
				console.log('发布活动');
				// 跳转到发布页面
			}
		}
	}
</script>

<style lang="scss" scoped>
	.activity-list-page {
		background: #f8f9fa;
		min-height: 100vh;
		position: relative;
	}

	.status-bar {
		background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
	}

	// 顶部导航
	.top-nav {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16rpx 24rpx;
		background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);

		.nav-left {
			display: flex;
			align-items: center;
			gap: 8rpx;

			.back-text {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
			}
		}

		.nav-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}

		.nav-right {
			width: 80rpx;
			display: flex;
			justify-content: flex-end;

			.search-btn {
				width: 44rpx;
				height: 44rpx;
				border-radius: 12rpx;
				background: rgba(255, 255, 255, 0.2);
				display: flex;
				align-items: center;
				justify-content: center;

				&:active {
					background: rgba(255, 255, 255, 0.3);
				}
			}
		}
	}

	// 筛选栏
	.filter-bar {
		background: #fff;
		padding: 20rpx 24rpx;
		display: flex;
		align-items: center;
		gap: 24rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);

		.filter-item {
			display: flex;
			align-items: center;
			gap: 8rpx;
			padding: 12rpx 16rpx;
			background: #f8f9fa;
			border-radius: 20rpx;
			border: 1rpx solid #e0e0e0;

			&:active {
				background: #e9ecef;
			}

			.filter-text {
				font-size: 24rpx;
				color: #666;
				white-space: nowrap;
			}
		}

		.sort-btn {
			margin-left: auto;
			width: 44rpx;
			height: 44rpx;
			border-radius: 12rpx;
			background: #f8f9fa;
			display: flex;
			align-items: center;
			justify-content: center;

			&:active {
				background: #e9ecef;
			}
		}
	}

	// 活动列表
	.activity-scroll {
		flex: 1;
		height: calc(100vh - 200rpx);
	}

	.activity-list {
		padding: 24rpx;

		.activity-card {
			background: #fff;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
			overflow: hidden;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.98);
			}

			.card-header {
				position: relative;
				height: 320rpx;

				.activity-image {
					width: 100%;
					height: 100%;
				}

				.activity-tags {
					position: absolute;
					top: 16rpx;
					right: 16rpx;
					display: flex;
					gap: 8rpx;

					.tag {
						padding: 6rpx 12rpx;
						border-radius: 12rpx;
						font-size: 20rpx;
						font-weight: 600;
						color: #fff;

						&.hot {
							background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
						}

						&.new {
							background: linear-gradient(135deg, #4ECDC4 0%, #6FE7DD 100%);
						}
					}
				}
			}

			.card-content {
				padding: 24rpx;

				.activity-title {
					font-size: 32rpx;
					font-weight: 700;
					color: #333;
					margin-bottom: 16rpx;
					display: block;
					line-height: 1.4;
				}

				.activity-info {
					margin-bottom: 20rpx;

					.info-item {
						display: flex;
						align-items: center;
						gap: 8rpx;
						margin-bottom: 8rpx;

						.info-text {
							font-size: 26rpx;
							color: #666;
						}
					}
				}

				.activity-footer {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.participants {
						display: flex;
						align-items: center;
						gap: 12rpx;

						.avatar-group {
							display: flex;
							margin-right: 8rpx;

							.participant-avatar {
								width: 48rpx;
								height: 48rpx;
								border-radius: 50%;
								border: 3rpx solid #fff;
								margin-left: -12rpx;

								&:first-child {
									margin-left: 0;
								}
							}
						}

						.participant-count {
							font-size: 24rpx;
							color: #999;
						}
					}

					.price-section {
						.price {
							font-size: 32rpx;
							font-weight: 700;
							color: #FFD700;
						}
					}
				}
			}
		}
	}

	// 加载状态
	.loading-more {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 12rpx;
		padding: 40rpx;

		.loading-text {
			font-size: 26rpx;
			color: #999;
		}
	}

	.no-more {
		text-align: center;
		padding: 40rpx;

		.no-more-text {
			font-size: 26rpx;
			color: #999;
		}
	}

	// 发布按钮
	.fab-btn {
		position: fixed;
		bottom: 120rpx;
		right: 40rpx;
		width: 112rpx;
		height: 112rpx;
		background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.4);
		z-index: 100;

		&:active {
			transform: scale(0.95);
		}
	}
</style>
