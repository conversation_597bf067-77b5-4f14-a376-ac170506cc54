<template>
	<view class="page-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="header-content">
				<view class="back-button" @click="goBack">
					<text class="back-icon">‹</text>
				</view>
				<text class="header-title">数据保护</text>
				<view class="header-placeholder"></view>
			</view>
		</view>

		<!-- 主要内容 -->
		<view class="main-content">
			<scroll-view scroll-y="true" class="scroll-container">
				<view class="document-content">
					<!-- 文档标题 -->
					<view class="document-header">
						<text class="document-title">闲伴数据保护规则</text>
						<text class="document-subtitle">保护用户隐私，确保数据安全</text>
						<view class="document-info">
							<text class="info-item">版本：v3.2</text>
							<text class="info-item">发布日期：2025年6月4日</text>
							<text class="info-item">生效日期：2025年6月4日</text>
						</view>
					</view>

					<!-- 第1章 -->
					<view class="content-section">
						<text class="section-title">1. 数据收集</text>
						<view class="section-item">
							<text class="item-number">1.1</text>
							<view class="item-content">
								<text class="item-title">收集原则</text>
								<text class="item-text">遵循最小化原则，仅收集提供服务所必需的用户数据。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.2</text>
							<view class="item-content">
								<text class="item-title">用户同意</text>
								<text class="item-text">在收集用户数据前，明确告知并获得用户的明示同意。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.3</text>
							<view class="item-content">
								<text class="item-title">数据类型</text>
								<text class="item-text">包括基本信息、使用行为、设备信息等必要数据。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.4</text>
							<view class="item-content">
								<text class="item-title">透明度</text>
								<text class="item-text">清晰说明数据收集的目的、方式和使用范围。</text>
							</view>
						</view>
					</view>
					
					<!-- 第2章 -->
					<view class="content-section">
						<text class="section-title">2. 数据使用</text>
						<view class="section-item">
							<text class="item-number">2.1</text>
							<view class="item-content">
								<text class="item-title">使用目的</text>
								<text class="item-text">仅用于提供服务、改善用户体验和保障平台安全。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.2</text>
							<view class="item-content">
								<text class="item-title">授权范围</text>
								<text class="item-text">严格按照用户授权的范围使用数据，不超出约定用途。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.3</text>
							<view class="item-content">
								<text class="item-title">数据分析</text>
								<text class="item-text">进行匿名化数据分析，用于产品优化和服务改进。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.4</text>
							<view class="item-content">
								<text class="item-title">第三方共享</text>
								<text class="item-text">除法律要求外，不向第三方共享用户个人数据。</text>
							</view>
						</view>
					</view>
					
					<!-- 第3章 -->
					<view class="content-section">
						<text class="section-title">3. 数据安全</text>
						<view class="section-item">
							<text class="item-number">3.1</text>
							<view class="item-content">
								<text class="item-title">技术保护</text>
								<text class="item-text">采用先进的加密技术和安全措施保护用户数据。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.2</text>
							<view class="item-content">
								<text class="item-title">访问控制</text>
								<text class="item-text">建立严格的数据访问权限管理和审计机制。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.3</text>
							<view class="item-content">
								<text class="item-title">安全监控</text>
								<text class="item-text">24小时监控数据安全状况，及时发现和处理安全威胁。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.4</text>
							<view class="item-content">
								<text class="item-title">事件响应</text>
								<text class="item-text">建立完善的数据安全事件应急响应机制。</text>
							</view>
						</view>
					</view>
					
					<!-- 第4章 -->
					<view class="content-section">
						<text class="section-title">4. 用户权利</text>
						<view class="section-item">
							<text class="item-number">4.1</text>
							<view class="item-content">
								<text class="item-title">知情权</text>
								<text class="item-text">用户有权了解个人数据的收集、使用和处理情况。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.2</text>
							<view class="item-content">
								<text class="item-title">访问权</text>
								<text class="item-text">用户可以查看和获取平台存储的个人数据副本。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.3</text>
							<view class="item-content">
								<text class="item-title">更正权</text>
								<text class="item-text">用户可以要求更正不准确或不完整的个人数据。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.4</text>
							<view class="item-content">
								<text class="item-title">删除权</text>
								<text class="item-text">在符合条件的情况下，用户可以要求删除个人数据。</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'DataProtectionRules',
	data() {
		return {
			statusBarHeight: 0
		};
	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 0;
	},
	methods: {
		goBack() {
			const pages = getCurrentPages();
			if (pages.length > 1) {
				uni.navigateBack({
					delta: 1
				});
			} else {
				uni.redirectTo({
					url: '/pages/community-rules/community-rules'
				});
			}
		}
	}
};
</script>

<style lang="scss">
.page-container {
	min-height: 100vh;
	background: #f8f9fa;
	display: flex;
	flex-direction: column;
}

.status-bar {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
}

.header {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
	padding: 0 32rpx 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(255, 215, 0, 0.2);

	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;

		.back-button {
			width: 64rpx;
			height: 64rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50%;
			backdrop-filter: blur(10rpx);

			.back-icon {
				font-size: 36rpx;
				color: #333;
				font-weight: bold;
				line-height: 1;
			}
		}

		.header-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			flex: 1;
			text-align: center;
		}

		.header-placeholder {
			width: 64rpx;
		}
	}
}

.main-content {
	flex: 1;
	overflow: hidden;

	.scroll-container {
		height: 100%;
	}
}

.document-content {
	padding: 32rpx;

	.document-header {
		text-align: center;
		margin-bottom: 48rpx;
		padding: 48rpx 32rpx;
		background: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

		.document-title {
			font-size: 48rpx;
			font-weight: 700;
			color: #333;
			display: block;
			margin-bottom: 16rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}

		.document-subtitle {
			font-size: 28rpx;
			color: #999;
			display: block;
			margin-bottom: 32rpx;
			font-style: italic;
		}

		.document-info {
			display: flex;
			justify-content: center;
			flex-wrap: wrap;
			gap: 24rpx;

			.info-item {
				font-size: 24rpx;
				color: #666;
				padding: 8rpx 16rpx;
				background: #f8f9fa;
				border-radius: 8rpx;
			}
		}
	}

	.content-section {
		margin-bottom: 48rpx;
		background: #fff;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

		.section-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			display: block;
			margin-bottom: 32rpx;
			padding: 24rpx 32rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
			border-radius: 12rpx;
			text-align: center;
			box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.2);
		}

		.section-item {
			display: flex;
			margin-bottom: 24rpx;
			align-items: flex-start;

			&:last-child {
				margin-bottom: 0;
			}

			.item-number {
				font-size: 28rpx;
				color: #FFD700;
				font-weight: 600;
				margin-right: 20rpx;
				flex-shrink: 0;
				margin-top: 4rpx;
				min-width: 60rpx;
			}

			.item-content {
				flex: 1;
				line-height: 1.6;

				.item-title {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
					display: block;
					margin-bottom: 12rpx;
				}

				.item-text {
					font-size: 28rpx;
					color: #666;
					line-height: 1.8;
					text-align: justify;
				}
			}
		}
	}
}
</style>
