<template>
	<view class="container">
		<!-- 背景图片 -->
		<image src="/static/logii.png" class="background-image" mode="aspectFill"></image>

		<!-- 状态栏占位 -->
		<view class="status-bar" :style="getStatusBarStyle()"></view>

		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="back-button" @click="goBack">
				<image src="/static/fanhuilogo.png" class="back-icon" mode="aspectFit"></image>
			</view>
			<view class="help-button" @click="goToForgotPassword">
				<text class="help-icon">?</text>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<!-- 欢迎文字 - 移到Hello左侧下方 -->
			<view class="welcome-text">
				<text class="welcome-subtitle">{{ maskedPhone }} 已注册</text>
			</view>

			<!-- 主要操作区域 - 在深蓝色区域 -->
			<view class="main-section">
				<!-- 密码输入区域 -->
				<view class="password-input-container">
					<input
						class="password-input"
						:type="showPassword ? 'text' : 'password'"
						placeholder="请输入登录密码"
						placeholder-style="color: #999999"
						v-model="password"
						maxlength="20"
					/>
					<view class="password-toggle" @click="togglePassword">
						<image :src="showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'"
							   class="eye-icon" mode="aspectFit"></image>
					</view>
				</view>

				<!-- 登录按钮 -->
				<view class="login-btn" @click="doLogin" :class="{ disabled: !canLogin }">
					<text class="login-btn-text">登录</text>
				</view>

				<!-- 忘记密码 -->
				<view class="forgot-password" @click="goToResetPassword">
					<text class="forgot-text">忘记密码？</text>
				</view>



				<!-- 分割线 -->
				<view class="divider">
					<view class="divider-line"></view>
					<text class="divider-text">其他登录方式</text>
					<view class="divider-line"></view>
				</view>

				<!-- 其他登录方式 -->
				<view class="other-methods">
					<view class="method-item" @click="loginWithCode">
						<view class="method-icon-circle">
							<image src="/static/icons/shoujihao.png" class="icon-image" mode="aspectFit"></image>
						</view>
						<text class="method-text">验证码登录</text>
					</view>
					<view class="method-item" @click="wechatLogin">
						<view class="method-icon-circle">
							<image src="/static/weixinlogo.png" class="icon-image" mode="aspectFit"></image>
						</view>
						<text class="method-text">微信登录</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			mobile: '',
			password: '',
			showPassword: false
		}
	},

	computed: {
		maskedPhone() {
			if (this.mobile && this.mobile.length === 11) {
				return this.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
			}
			return this.mobile
		},
		
		canLogin() {
			return this.password.length >= 6
		}
	},

	onLoad(options) {
		if (options.mobile) {
			this.mobile = options.mobile
		}
	},

	methods: {
		// 获取状态栏样式
		getStatusBarStyle() {
			const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0
			return {
				height: statusBarHeight + 'px'
			}
		},

		// 返回
		goBack() {
			uni.navigateBack()
		},

		// 切换密码显示
		togglePassword() {
			this.showPassword = !this.showPassword
		},

		// 执行登录
		async doLogin() {
			if (!this.canLogin) return

			try {
				uni.showLoading({
					title: '登录中...'
				})

				// 调用云函数进行登录
				const result = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'login',
						mobile: this.mobile,
						password: this.password
					}
				})

				uni.hideLoading()

				if (result.result.code === 0) {
					// 保存用户信息到本地
					console.log('🔍 密码登录成功，完整返回数据:', result)
					console.log('🔍 result.result:', result.result)
					console.log('🔍 result.result.data:', result.result.data)
					console.log('🔍 result.result.data.userInfo:', result.result.data.userInfo)
					console.log('🔍 result.result.data.token:', result.result.data.token)

					// 检查数据结构是否正确
					if (result && result.result && result.result.data) {
						console.log('✅ 数据结构检查通过')
					} else {
						console.error('❌ 数据结构异常:', {
							hasResult: !!result,
							hasResultResult: !!(result && result.result),
							hasResultData: !!(result && result.result && result.result.data)
						})
					}

					// 智能处理不同的数据结构
					let userInfo, token;

					if (result.result.data.userInfo && result.result.data.token) {
						// 标准结构：data.userInfo 和 data.token
						userInfo = result.result.data.userInfo;
						token = result.result.data.token;
						console.log('✅ 使用标准数据结构');
					} else if (result.result.data._id) {
						// 直接结构：data 就是用户信息，token 在 result.result.token
						userInfo = result.result.data;
						token = result.result.token || result.result.data.token || 'temp_token_' + result.result.data._id;
						console.log('✅ 使用直接数据结构');
					} else {
						console.error('❌ 无法识别的数据结构!');
						uni.showToast({
							title: '登录数据异常',
							icon: 'none'
						})
						return
					}

					console.log('📋 最终用户信息:', userInfo);
					console.log('🔑 最终token:', token);

					// 先尝试存储
					try {
						uni.setStorageSync('userInfo', userInfo)
						uni.setStorageSync('token', token)
						uni.setStorageSync('isLoggedIn', true)
						console.log('✅ 存储操作完成')
					} catch (storageError) {
						console.error('❌ 存储失败:', storageError)
						uni.showToast({
							title: '存储失败，请重试',
							icon: 'none'
						})
						return
					}

					// 立即验证存储是否成功
					const savedUserInfo = uni.getStorageSync('userInfo')
					const savedToken = uni.getStorageSync('token')
					const savedLoginStatus = uni.getStorageSync('isLoggedIn')
					console.log('✅ 立即验证存储 - userInfo:', savedUserInfo)
					console.log('✅ 立即验证存储 - token:', savedToken)
					console.log('✅ 立即验证存储 - isLoggedIn:', savedLoginStatus)

					// 检查存储是否真的成功
					if (!savedUserInfo || !savedToken) {
						console.error('❌ 存储验证失败，数据未正确保存')
						uni.showToast({
							title: '数据保存失败，请重试',
							icon: 'none'
						})
						return
					}

					uni.showToast({
						title: '登录成功',
						icon: 'success'
					})

					// 延迟跳转，并在跳转前再次验证存储
					setTimeout(() => {
						// 跳转前再次检查存储
						const finalUserInfo = uni.getStorageSync('userInfo')
						const finalToken = uni.getStorageSync('token')
						console.log('🔍 跳转前最终验证 - userInfo:', finalUserInfo)
						console.log('🔍 跳转前最终验证 - token:', finalToken)

						uni.switchTab({
							url: '/pages/profile/profile'
						})
					}, 1500)
				} else {
					uni.showToast({
						title: result.result.message || '登录失败',
						icon: 'none',
						duration: 3000
					})
				}
			} catch (error) {
				uni.hideLoading()
				console.error('登录失败:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			}
		},

		// 忘记密码
		forgotPassword() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			})
		},

		// 跳转到忘记密码帮助页面（帮助按钮）
		goToForgotPassword() {
			console.log('点击了帮助按钮')
			uni.navigateTo({
				url: '/pages/auth/forgot-password-help/forgot-password-help'
			})
		},

		// 跳转到重置密码页面（忘记密码文字）
		goToResetPassword() {
			console.log('点击了忘记密码文字')
			uni.navigateTo({
				url: '/pages/auth/forgot-password/forgot-password'
			})
		},

		// 验证码登录
		loginWithCode() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			})
		},

		// 微信登录
		wechatLogin() {
			uni.showToast({
				title: '微信登录开发中',
				icon: 'none',
				duration: 2000
			})
		},


	}
}
</script>

<style scoped>
@import '@/styles/global.scss';

/* 强制消除所有可能的边距和留白 */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

page, body, html, uni-page-body, uni-page, uni-page-wrapper {
	margin: 0 !important;
	padding: 0 !important;
	width: 100% !important;
	height: 100% !important;
	overflow: hidden !important;
	border: none !important;
	outline: none !important;
}

.container {
	position: fixed;
	top: -10px;
	left: -10px;
	right: -10px;
	bottom: -10px;
	width: calc(100vw + 20px);
	height: calc(100vh + 20px);
	overflow: hidden;
	margin: 0;
	padding: 10px;
}

.background-image {
	width: calc(100% + 20px);
	height: calc(100% + 20px);
	position: fixed;
	top: -10px;
	left: -10px;
	z-index: 1;
}

.status-bar {
	background: transparent;
	position: relative;
	z-index: 2;
}

.nav-bar {
	position: fixed;
	top: var(--status-bar-height);
	left: 0;
	right: 0;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 var(--spacing-lg);
	z-index: 100;
	background: transparent;
}

.back-button {
	width: 50rpx;
	height: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.3);
	border-radius: 50%;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
}

.back-button:active {
	transform: scale(0.95);
	background: rgba(0, 0, 0, 0.5);
}

.back-icon {
	width: 28rpx;
	height: 28rpx;
	filter: brightness(0) invert(1);
}

.help-button {
	width: 50rpx;
	height: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.3);
	border-radius: 50%;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
	z-index: 1000;
	position: relative;
}

.help-button:active {
	transform: scale(0.95);
	background: rgba(0, 0, 0, 0.5);
}

.help-icon {
	color: #FFFFFF;
	font-size: 32rpx;
	font-weight: bold;
}

.content {
	position: relative;
	z-index: 2;
	padding: 0 60rpx 60rpx;
	height: 100%;
	display: flex;
	flex-direction: column;
}

.welcome-text {
	position: absolute;
	top: 820rpx;
	left: 60rpx;
	right: 200rpx;
	text-align: left;
}



.welcome-subtitle {
	display: block;
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

.main-section {
	margin-top: 900rpx;
	flex: 1;
	display: flex;
	flex-direction: column;
}

.password-input-container {
	position: relative;
	display: flex;
	align-items: center;
	height: 120rpx;
	background: #FFFFFF;
	border-radius: 60rpx;
	padding: 0 40rpx;
	border: 3rpx solid #66D4C8;
	margin-bottom: 40rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 212, 200, 0.2);
	margin-left: 30rpx;
	margin-right: 30rpx;
}

.password-input {
	flex: 1;
	height: 120rpx;
	padding: 0 20rpx;
	color: #333333;
	font-size: 36rpx;
	font-weight: bold;
	background: transparent;
	border: none;
	outline: none;
}

.password-toggle {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.eye-icon {
	width: 40rpx;
	height: 40rpx;
	opacity: 0.8;
	transition: all 0.3s ease;
}

.password-toggle:active .eye-icon {
	transform: scale(1.1);
	opacity: 1;
}

.login-btn {
	height: 120rpx;
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 100%);
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	margin-left: 30rpx;
	margin-right: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(102, 212, 200, 0.4);
	transition: all 0.3s ease;
}

.login-btn.disabled {
	opacity: 0.5;
	box-shadow: none;
}

.login-btn:active:not(.disabled) {
	transform: scale(0.98);
}

.login-btn-text {
	color: #FFFFFF;
	font-size: 36rpx;
	font-weight: 600;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.forgot-password {
	text-align: center;
	margin: 30rpx 30rpx 40rpx;
}

.forgot-text {
	color: #66D4C8;
	font-size: 28rpx;
	text-decoration: none;
	font-weight: 500;
}

.divider {
	display: flex;
	align-items: center;
	margin: 60rpx 0 50rpx;
}

.divider-line {
	flex: 1;
	height: 2rpx;
	background: rgba(51, 51, 51, 0.3);
}

.divider-text {
	color: #333333;
	font-size: 26rpx;
	margin: 0 30rpx;
	font-weight: 500;
}

.other-methods {
	display: flex;
	justify-content: space-around;
	align-items: center;
	margin-bottom: 60rpx;
	padding: 0 80rpx;
}

.method-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	transition: all 0.3s ease;
}

.method-item:active {
	transform: scale(0.95);
}

.method-icon {
	margin-bottom: 15rpx;
}

.method-icon-circle {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 15rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	border: 2rpx solid rgba(102, 212, 200, 0.3);
}

.icon-text {
	font-size: 40rpx;
}

.method-text {
	color: #66D4C8;
	font-size: 28rpx;
	font-weight: 500;
}

.icon-image {
	width: 48rpx;
	height: 48rpx;
}

/* 微信图标特殊处理 - 改为黑色 */
.method-item:last-child .icon-image {
	filter: brightness(0) saturate(100%);
}
</style>
