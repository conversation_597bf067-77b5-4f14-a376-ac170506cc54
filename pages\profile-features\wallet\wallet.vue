<template>
	<view class="wallet-page">
		<!-- 状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left" @click="goBack">
				<view class="back-btn">
					<uni-icons type="left" size="18" color="#fff"></uni-icons>
				</view>
			</view>
			<view class="nav-center">
				<text class="nav-title">我的钱包</text>
			</view>
			<view class="nav-right">
				<view class="help-btn" @click="showHelp">
					<uni-icons type="help" size="18" color="#fff"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 余额卡片 -->
		<view class="balance-section">
			<view class="balance-card">
				<view class="card-bg">
					<view class="balance-header">
						<text class="balance-label">账户余额</text>
						<view class="eye-btn" @click="toggleBalanceVisible">
							<uni-icons :type="balanceVisible ? 'eye' : 'eye-slash'" size="16" color="rgba(255,255,255,0.8)"></uni-icons>
						</view>
					</view>
					<view class="balance-amount">
						<text class="currency">¥</text>
						<text class="amount">{{ balanceVisible ? userWallet.balance.toFixed(2) : '****' }}</text>
					</view>
					<view class="balance-actions">
						<view class="action-btn recharge" @click="goToRecharge">
							<uni-icons type="plus" size="16" color="#fff"></uni-icons>
							<text class="action-text">充值</text>
						</view>
						<view class="action-btn withdraw" @click="goToWithdraw">
							<uni-icons type="minus" size="16" color="#fff"></uni-icons>
							<text class="action-text">提现</text>
						</view>
						<view class="action-btn transfer" @click="goToTransfer">
							<uni-icons type="redo" size="16" color="#fff"></uni-icons>
							<text class="action-text">转账</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 快捷功能 -->
		<view class="quick-functions">
			<view class="function-grid">
				<view class="function-item" @click="goToRecharge">
					<view class="function-icon recharge">
						<uni-icons type="plus" size="24" color="#fff"></uni-icons>
					</view>
					<text class="function-text">充值</text>
				</view>
				<view class="function-item" @click="goToWithdraw">
					<view class="function-icon withdraw">
						<uni-icons type="minus" size="24" color="#fff"></uni-icons>
					</view>
					<text class="function-text">提现</text>
				</view>
				<view class="function-item" @click="goToTransfer">
					<view class="function-icon transfer">
						<uni-icons type="redo" size="24" color="#fff"></uni-icons>
					</view>
					<text class="function-text">转账</text>
				</view>
				<view class="function-item" @click="goToBills">
					<view class="function-icon bills">
						<uni-icons type="list" size="24" color="#fff"></uni-icons>
					</view>
					<text class="function-text">账单</text>
				</view>
			</view>
		</view>

		<!-- 交易记录 -->
		<view class="transactions-section">
			<view class="section-header">
				<text class="section-title">最近交易</text>
				<view class="more-btn" @click="goToBills">
					<text class="more-text">查看全部</text>
					<uni-icons type="right" size="12" color="#999"></uni-icons>
				</view>
			</view>
			<view class="transactions-list">
				<view 
					class="transaction-item" 
					v-for="transaction in recentTransactions" 
					:key="transaction.id"
					@click="viewTransactionDetail(transaction)"
				>
					<view class="transaction-icon" :class="transaction.type">
						<uni-icons :type="getTransactionIcon(transaction.type)" size="20" color="#fff"></uni-icons>
					</view>
					<view class="transaction-info">
						<text class="transaction-title">{{ transaction.title }}</text>
						<text class="transaction-time">{{ transaction.time }}</text>
					</view>
					<view class="transaction-amount" :class="transaction.type">
						<text class="amount-text">{{ transaction.type === 'income' ? '+' : '-' }}¥{{ transaction.amount }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 安全提示 -->
		<view class="security-section">
			<view class="security-card">
				<view class="security-header">
					<uni-icons type="locked" size="20" color="#4ECDC4"></uni-icons>
					<text class="security-title">安全提示</text>
				</view>
				<text class="security-desc">为了您的资金安全，请妥善保管账户信息，不要向他人透露密码。</text>
				<view class="security-actions">
					<view class="security-btn" @click="setPayPassword">
						<text class="btn-text">设置支付密码</text>
					</view>
					<view class="security-btn" @click="bindPhone">
						<text class="btn-text">绑定手机号</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				balanceVisible: true,
				
				userWallet: {
					balance: 168.50
				},
				
				recentTransactions: [
					{
						id: 1,
						type: 'income',
						title: '充值',
						amount: 100.00,
						time: '今天 14:30'
					},
					{
						id: 2,
						type: 'expense',
						title: '购买VIP会员',
						amount: 45.00,
						time: '昨天 16:20'
					},
					{
						id: 3,
						type: 'income',
						title: '退款',
						amount: 23.50,
						time: '2天前'
					}
				]
			}
		},
		
		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},
		
		methods: {
			goBack() {
				uni.navigateBack();
			},
			
			showHelp() {
				console.log('显示帮助');
			},
			
			toggleBalanceVisible() {
				this.balanceVisible = !this.balanceVisible;
			},
			
			goToRecharge() {
				console.log('去充值');
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			},
			
			goToWithdraw() {
				console.log('去提现');
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			},
			
			goToTransfer() {
				console.log('去转账');
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			},
			
			goToBills() {
				console.log('查看账单');
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			},
			
			viewTransactionDetail(transaction) {
				console.log('查看交易详情:', transaction.title);
			},
			
			setPayPassword() {
				console.log('设置支付密码');
			},
			
			bindPhone() {
				console.log('绑定手机号');
			},
			
			getTransactionIcon(type) {
				const icons = {
					income: 'plus',
					expense: 'minus'
				};
				return icons[type] || 'list';
			}
		}
	}
</script>

<style lang="scss" scoped>
	.wallet-page {
		background: #f5f6fa;
		min-height: 100vh;
	}
	
	.status-bar {
		background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
	}
	
	// 顶部导航
	.top-nav {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16rpx 24rpx;
		background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
		
		.nav-left, .nav-right {
			.back-btn, .help-btn {
				width: 40rpx;
				height: 40rpx;
				border-radius: 20rpx;
				background: rgba(255, 255, 255, 0.2);
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				
				&:active {
					background: rgba(255, 255, 255, 0.3);
					transform: scale(0.95);
				}
			}
		}
		
		.nav-center {
			flex: 1;
			text-align: center;
			
			.nav-title {
				font-size: 34rpx;
				font-weight: 700;
				color: #fff;
			}
		}
	}
	
	// 余额卡片
	.balance-section {
		padding: 16rpx 24rpx;
		
		.balance-card {
			border-radius: 24rpx;
			overflow: hidden;
			box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
			
			.card-bg {
				background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
				padding: 32rpx;
				color: #fff;
				
				.balance-header {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 16rpx;
					
					.balance-label {
						font-size: 26rpx;
						color: rgba(255, 255, 255, 0.8);
					}
					
					.eye-btn {
						width: 32rpx;
						height: 32rpx;
						border-radius: 16rpx;
						background: rgba(255, 255, 255, 0.2);
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}
				
				.balance-amount {
					display: flex;
					align-items: baseline;
					margin-bottom: 32rpx;
					
					.currency {
						font-size: 32rpx;
						font-weight: 600;
						margin-right: 8rpx;
					}
					
					.amount {
						font-size: 56rpx;
						font-weight: 700;
					}
				}
				
				.balance-actions {
					display: flex;
					gap: 16rpx;
					
					.action-btn {
						flex: 1;
						display: flex;
						align-items: center;
						justify-content: center;
						gap: 8rpx;
						padding: 16rpx;
						background: rgba(255, 255, 255, 0.2);
						border-radius: 20rpx;
						transition: all 0.3s ease;
						
						&:active {
							background: rgba(255, 255, 255, 0.3);
							transform: scale(0.98);
						}
						
						.action-text {
							font-size: 24rpx;
							color: #fff;
							font-weight: 500;
						}
					}
				}
			}
		}
	}

	// 快捷功能
	.quick-functions {
		padding: 0 24rpx 24rpx;

		.function-grid {
			background: #fff;
			border-radius: 20rpx;
			padding: 32rpx 24rpx;
			display: flex;
			justify-content: space-around;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

			.function-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 12rpx;
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.95);
				}

				.function-icon {
					width: 64rpx;
					height: 64rpx;
					border-radius: 20rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

					&.recharge {
						background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
					}

					&.withdraw {
						background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
					}

					&.transfer {
						background: linear-gradient(135deg, #9B59B6 0%, #BB6BD9 100%);
					}

					&.bills {
						background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					}
				}

				.function-text {
					font-size: 24rpx;
					color: #333;
					font-weight: 500;
				}
			}
		}
	}

	// 交易记录
	.transactions-section {
		padding: 0 24rpx 24rpx;

		.section-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 16rpx;

			.section-title {
				font-size: 30rpx;
				font-weight: 700;
				color: #333;
			}

			.more-btn {
				display: flex;
				align-items: center;
				gap: 6rpx;

				.more-text {
					font-size: 24rpx;
					color: #999;
				}
			}
		}

		.transactions-list {
			background: #fff;
			border-radius: 20rpx;
			overflow: hidden;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

			.transaction-item {
				display: flex;
				align-items: center;
				padding: 24rpx;
				border-bottom: 1rpx solid #f8f9fa;
				transition: all 0.3s ease;

				&:last-child {
					border-bottom: none;
				}

				&:active {
					background: #f8f9fa;
				}

				.transaction-icon {
					width: 48rpx;
					height: 48rpx;
					border-radius: 24rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 16rpx;

					&.income {
						background: #2ed573;
					}

					&.expense {
						background: #FF6B6B;
					}
				}

				.transaction-info {
					flex: 1;

					.transaction-title {
						font-size: 28rpx;
						font-weight: 500;
						color: #333;
						display: block;
						margin-bottom: 4rpx;
					}

					.transaction-time {
						font-size: 22rpx;
						color: #999;
					}
				}

				.transaction-amount {
					.amount-text {
						font-size: 28rpx;
						font-weight: 600;
					}

					&.income .amount-text {
						color: #2ed573;
					}

					&.expense .amount-text {
						color: #FF6B6B;
					}
				}
			}
		}
	}

	// 安全提示
	.security-section {
		padding: 0 24rpx 120rpx;

		.security-card {
			background: #fff;
			border-radius: 20rpx;
			padding: 24rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

			.security-header {
				display: flex;
				align-items: center;
				gap: 12rpx;
				margin-bottom: 16rpx;

				.security-title {
					font-size: 28rpx;
					font-weight: 600;
					color: #333;
				}
			}

			.security-desc {
				font-size: 24rpx;
				color: #666;
				line-height: 1.5;
				margin-bottom: 20rpx;
			}

			.security-actions {
				display: flex;
				gap: 16rpx;

				.security-btn {
					flex: 1;
					background: rgba(78, 205, 196, 0.1);
					border: 1rpx solid rgba(78, 205, 196, 0.3);
					border-radius: 20rpx;
					padding: 16rpx;
					text-align: center;
					transition: all 0.3s ease;

					&:active {
						background: rgba(78, 205, 196, 0.2);
						transform: scale(0.98);
					}

					.btn-text {
						font-size: 24rpx;
						color: #4ECDC4;
						font-weight: 500;
					}
				}
			}
		}
	}
</style>
