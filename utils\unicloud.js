// uniCloud 工具函数
const db = uniCloud.database();

/**
 * 用户相关API
 */
export const userAPI = {
  // 发送短信验证码
  async sendSmsCode(mobile) {
    try {
      const result = await uniCloud.callFunction({
        name: 'user-center',
        data: {
          action: 'sendSmsCode',
          params: { mobile }
        }
      });
      return result.result;
    } catch (error) {
      console.error('发送验证码失败:', error);
      return { code: 500, message: '发送失败' };
    }
  },

  // 手机号验证码登录
  async loginBySms(mobile, code) {
    try {
      const result = await uniCloud.callFunction({
        name: 'user-center',
        data: {
          action: 'loginBySms',
          params: { mobile, code }
        }
      });
      
      if (result.result.code === 0) {
        // 保存用户信息到本地
        uni.setStorageSync('userInfo', result.result.userInfo);
        uni.setStorageSync('token', result.result.token);
      }
      
      return result.result;
    } catch (error) {
      console.error('登录失败:', error);
      return { code: 500, message: '登录失败' };
    }
  },

  // 获取用户信息
  async getUserInfo(uid) {
    try {
      const result = await uniCloud.callFunction({
        name: 'user-center',
        data: {
          action: 'getUserInfo',
          params: { uid }
        }
      });
      return result.result;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return { code: 500, message: '获取失败' };
    }
  },

  // 更新用户信息
  async updateUser(userInfo) {
    try {
      const result = await uniCloud.callFunction({
        name: 'user-center',
        data: {
          action: 'updateUser',
          params: userInfo
        }
      });
      
      if (result.result.code === 0) {
        // 更新本地用户信息
        const localUserInfo = uni.getStorageSync('userInfo');
        uni.setStorageSync('userInfo', { ...localUserInfo, ...userInfo });
      }
      
      return result.result;
    } catch (error) {
      console.error('更新用户信息失败:', error);
      return { code: 500, message: '更新失败' };
    }
  },

  // 退出登录
  async logout() {
    try {
      const result = await uniCloud.callFunction({
        name: 'user-center',
        data: {
          action: 'logout'
        }
      });
      
      // 清除本地存储
      uni.removeStorageSync('userInfo');
      uni.removeStorageSync('token');
      
      return result.result;
    } catch (error) {
      console.error('退出登录失败:', error);
      return { code: 500, message: '退出失败' };
    }
  }
};

/**
 * 客服相关API
 */
export const customerServiceAPI = {
  // 创建客服会话
  async createSession(userId) {
    try {
      const result = await uniCloud.callFunction({
        name: 'customer-service',
        data: {
          action: 'createSession',
          params: { userId }
        }
      });
      return result.result;
    } catch (error) {
      console.error('创建会话失败:', error);
      return { code: 500, message: '创建失败' };
    }
  },

  // 发送消息
  async sendMessage(sessionId, senderId, senderType, content, messageType = 'text') {
    try {
      const result = await uniCloud.callFunction({
        name: 'customer-service',
        data: {
          action: 'sendMessage',
          params: {
            sessionId,
            senderId,
            senderType,
            content,
            messageType
          }
        }
      });
      return result.result;
    } catch (error) {
      console.error('发送消息失败:', error);
      return { code: 500, message: '发送失败' };
    }
  },

  // 获取消息历史
  async getMessages(sessionId, page = 1, limit = 20) {
    try {
      const result = await uniCloud.callFunction({
        name: 'customer-service',
        data: {
          action: 'getMessages',
          params: { sessionId, page, limit }
        }
      });
      return result.result;
    } catch (error) {
      console.error('获取消息失败:', error);
      return { code: 500, message: '获取失败' };
    }
  },

  // 获取会话列表
  async getSessions(userId, status) {
    try {
      const result = await uniCloud.callFunction({
        name: 'customer-service',
        data: {
          action: 'getSessions',
          params: { userId, status }
        }
      });
      return result.result;
    } catch (error) {
      console.error('获取会话列表失败:', error);
      return { code: 500, message: '获取失败' };
    }
  },

  // 结束会话
  async closeSession(sessionId) {
    try {
      const result = await uniCloud.callFunction({
        name: 'customer-service',
        data: {
          action: 'closeSession',
          params: { sessionId }
        }
      });
      return result.result;
    } catch (error) {
      console.error('结束会话失败:', error);
      return { code: 500, message: '结束失败' };
    }
  }
};

/**
 * 通用工具函数
 */
export const utils = {
  // 检查登录状态
  checkLogin() {
    const token = uni.getStorageSync('token');
    const userInfo = uni.getStorageSync('userInfo');
    return !!(token && userInfo);
  },

  // 获取当前用户信息
  getCurrentUser() {
    return uni.getStorageSync('userInfo');
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      return date.toLocaleDateString();
    }
  }
};
