<template>
	<view class="creator-center-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<uni-icons type="left" size="24" color="#fff"></uni-icons>
					<text class="back-text">返回</text>
				</view>
				<text class="nav-title">创作者中心</text>
				<view class="nav-right"></view>
			</view>
		</view>

		<!-- 创作者统计 -->
		<view class="creator-stats">
			<view class="stats-container">
				<view class="stat-item">
					<text class="stat-number">{{ totalWorks }}</text>
					<text class="stat-label">作品数</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-number">{{ totalViews }}</text>
					<text class="stat-label">总浏览</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-number">{{ totalFans }}</text>
					<text class="stat-label">粉丝数</text>
				</view>
			</view>
		</view>

		<!-- 功能菜单 -->
		<scroll-view class="creator-content" scroll-y="true">
			<!-- 内容管理 -->
			<view class="menu-section">
				<text class="section-title">内容管理</text>
				<view class="menu-list">
					<view class="menu-item" @tap="goToMyWorks">
						<view class="item-icon content">
							<uni-icons type="compose" size="20" color="#fff"></uni-icons>
						</view>
						<view class="item-info">
							<text class="item-title">我的作品</text>
							<text class="item-desc">管理已发布的内容</text>
						</view>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>

					<view class="menu-item" @tap="createContent">
						<view class="item-icon create">
							<uni-icons type="plus" size="20" color="#fff"></uni-icons>
						</view>
						<view class="item-info">
							<text class="item-title">创作内容</text>
							<text class="item-desc">发布图文、视频、音频</text>
						</view>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>

					<view class="menu-item" @tap="manageDrafts">
						<view class="item-icon draft">
							<uni-icons type="folder" size="20" color="#fff"></uni-icons>
						</view>
						<view class="item-info">
							<text class="item-title">草稿箱</text>
							<text class="item-desc">管理未发布的草稿</text>
						</view>
						<view class="item-badge">{{ draftCount }}</view>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 数据分析 -->
			<view class="menu-section">
				<text class="section-title">数据分析</text>
				<view class="menu-list">
					<view class="menu-item" @tap="viewAnalytics">
						<view class="item-icon analytics">
							<uni-icons type="bars" size="20" color="#fff"></uni-icons>
						</view>
						<view class="item-info">
							<text class="item-title">数据概览</text>
							<text class="item-desc">查看作品数据统计</text>
						</view>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>

					<view class="menu-item" @tap="viewFansData">
						<view class="item-icon fans">
							<uni-icons type="person" size="20" color="#fff"></uni-icons>
						</view>
						<view class="item-info">
							<text class="item-title">粉丝分析</text>
							<text class="item-desc">了解粉丝画像</text>
						</view>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 创作工具 -->
			<view class="menu-section">
				<text class="section-title">创作工具</text>
				<view class="menu-list">
					<view class="menu-item" @tap="openEditor">
						<view class="item-icon editor">
							<uni-icons type="compose" size="20" color="#fff"></uni-icons>
						</view>
						<view class="item-info">
							<text class="item-title">在线编辑器</text>
							<text class="item-desc">强大的内容编辑工具</text>
						</view>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>

					<view class="menu-item" @tap="materialLibrary">
						<view class="item-icon material">
							<uni-icons type="image" size="20" color="#fff"></uni-icons>
						</view>
						<view class="item-info">
							<text class="item-title">素材库</text>
							<text class="item-desc">丰富的创作素材</text>
						</view>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 创作指南 -->
			<view class="menu-section">
				<text class="section-title">创作指南</text>
				<view class="guide-cards">
					<view class="guide-card" v-for="guide in guides" :key="guide.id" @tap="viewGuide(guide)">
						<image class="guide-image" :src="guide.image" mode="aspectFill"></image>
						<view class="guide-info">
							<text class="guide-title">{{ guide.title }}</text>
							<text class="guide-desc">{{ guide.description }}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				totalWorks: 12,
				totalViews: '2.3k',
				totalFans: 156,
				draftCount: 3,
				
				// 创作指南
				guides: [
					{
						id: 1,
						title: '如何写出爆款内容',
						description: '掌握内容创作的核心技巧',
						image: 'https://s1.imagehub.cc/images/2025/05/26/guide1.png'
					},
					{
						id: 2,
						title: '视频拍摄技巧',
						description: '用手机拍出专业级视频',
						image: 'https://s1.imagehub.cc/images/2025/05/26/guide2.png'
					},
					{
						id: 3,
						title: '社区运营策略',
						description: '建立自己的粉丝社群',
						image: 'https://s1.imagehub.cc/images/2025/05/26/guide3.png'
					}
				]
			}
		},

		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},

		methods: {
			goBack() {
				uni.switchTab({
					url: '/pages/profile/profile'
				});
			},

			goToMyWorks() {
				uni.navigateTo({
					url: '/pages/profile-features/my-works/my-works'
				});
			},

			createContent() {
				uni.showActionSheet({
					itemList: ['发布图文', '发布视频', '发布音频'],
					success: (res) => {
						uni.showToast({
							title: '创作功能开发中',
							icon: 'none'
						});
					}
				});
			},

			manageDrafts() {
				uni.showToast({
					title: '草稿箱功能开发中',
					icon: 'none'
				});
			},

			viewAnalytics() {
				uni.showToast({
					title: '数据分析功能开发中',
					icon: 'none'
				});
			},

			viewFansData() {
				uni.showToast({
					title: '粉丝分析功能开发中',
					icon: 'none'
				});
			},

			openEditor() {
				uni.showToast({
					title: '编辑器功能开发中',
					icon: 'none'
				});
			},

			materialLibrary() {
				uni.showToast({
					title: '素材库功能开发中',
					icon: 'none'
				});
			},

			viewGuide(guide) {
				uni.showToast({
					title: `${guide.title}指南开发中`,
					icon: 'none'
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.creator-center-page {
		background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.status-bar {
		background: transparent;
	}

	.header {
		background: transparent;

		.nav-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;
			height: 88rpx;

			.nav-left {
				display: flex;
				align-items: center;
				gap: 8rpx;
				flex: 1;

				.back-text {
					font-size: 28rpx;
					color: #fff;
					font-weight: 500;
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: #fff;
				font-weight: 600;
				text-align: center;
				flex: 2;
			}

			.nav-right {
				flex: 1;
			}
		}
	}

	// 创作者统计
	.creator-stats {
		background: rgba(255, 255, 255, 0.15);
		margin: 24rpx;
		border-radius: 20rpx;
		backdrop-filter: blur(10rpx);

		.stats-container {
			display: flex;
			align-items: center;
			padding: 32rpx 24rpx;

			.stat-item {
				flex: 1;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
				gap: 8rpx;
				padding: 8rpx 12rpx;
				border-radius: 12rpx;
				transition: all 0.3s ease;

				&:active {
					background: rgba(255, 255, 255, 0.1);
					transform: scale(0.98);
				}

				.stat-number {
					font-size: 48rpx;
					color: #fff;
					font-weight: 700;
				}

				.stat-label {
					font-size: 24rpx;
					color: rgba(255, 255, 255, 0.8);
					font-weight: 500;
				}
			}

			.stat-divider {
				width: 1rpx;
				height: 60rpx;
				background: rgba(255, 255, 255, 0.3);
			}
		}
	}

	// 创作者内容区域
	.creator-content {
		flex: 1;
		background: #f5f5f5;
		border-radius: 32rpx 32rpx 0 0;
		padding: 32rpx 24rpx 120rpx;

		.menu-section {
			margin-bottom: 32rpx;

			.section-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 700;
				margin-bottom: 16rpx;
				padding-left: 8rpx;
				display: block;
			}

			.menu-list {
				background: #fff;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.menu-item {
					display: flex;
					align-items: center;
					padding: 24rpx;
					border-bottom: 1rpx solid #f5f5f5;
					transition: all 0.3s ease;

					&:last-child {
						border-bottom: none;
					}

					&:active {
						background: #f8f9fa;
					}

					.item-icon {
						width: 48rpx;
						height: 48rpx;
						border-radius: 24rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 16rpx;

						&.content {
							background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
						}

						&.create {
							background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
						}

						&.draft {
							background: linear-gradient(135deg, #ffa726 0%, #ffcc02 100%);
						}

						&.analytics {
							background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
						}

						&.fans {
							background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
						}

						&.editor {
							background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
						}

						&.material {
							background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
						}
					}

					.item-info {
						flex: 1;

						.item-title {
							font-size: 30rpx;
							color: #333;
							font-weight: 600;
							display: block;
							margin-bottom: 8rpx;
						}

						.item-desc {
							font-size: 24rpx;
							color: #999;
							line-height: 1.4;
						}
					}

					.item-badge {
						background: #ff4757;
						color: #fff;
						font-size: 20rpx;
						padding: 4rpx 8rpx;
						border-radius: 10rpx;
						margin-right: 16rpx;
						min-width: 24rpx;
						text-align: center;
					}
				}
			}

			// 创作指南卡片
			.guide-cards {
				display: flex;
				flex-direction: column;
				gap: 16rpx;

				.guide-card {
					background: #fff;
					border-radius: 16rpx;
					overflow: hidden;
					box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
					display: flex;
					transition: all 0.3s ease;

					&:active {
						transform: scale(0.98);
					}

					.guide-image {
						width: 120rpx;
						height: 120rpx;
						background: #f5f5f5;
					}

					.guide-info {
						flex: 1;
						padding: 24rpx;

						.guide-title {
							font-size: 28rpx;
							color: #333;
							font-weight: 600;
							display: block;
							margin-bottom: 8rpx;
						}

						.guide-desc {
							font-size: 24rpx;
							color: #999;
							line-height: 1.4;
						}
					}
				}
			}
		}
	}
</style>
