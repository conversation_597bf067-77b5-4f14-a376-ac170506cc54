/**
 * 头像工具函数
 * 统一处理头像相关逻辑
 */

// 默认头像路径
export const DEFAULT_AVATAR_PATH = '/static/default-avatar.jpg'

/**
 * 获取有效的头像路径
 * @param {string} avatarPath - 用户头像路径
 * @returns {string} 有效的头像路径
 */
export function getValidAvatarPath(avatarPath) {
	// 如果没有头像或头像为空，返回默认头像
	if (!avatarPath || avatarPath.trim() === '') {
		return DEFAULT_AVATAR_PATH
	}
	
	// 如果已经是默认头像，直接返回
	if (avatarPath === DEFAULT_AVATAR_PATH) {
		return avatarPath
	}
	
	// 检查是否是有效的图片路径
	if (isValidImagePath(avatarPath)) {
		return avatarPath
	}
	
	// 无效路径，返回默认头像
	return DEFAULT_AVATAR_PATH
}

/**
 * 检查是否是有效的图片路径
 * @param {string} path - 图片路径
 * @returns {boolean} 是否有效
 */
function isValidImagePath(path) {
	if (!path || typeof path !== 'string') {
		return false
	}
	
	// 检查是否是支持的图片格式（不包含透明格式PNG和GIF）
	const imageExtensions = ['.jpg', '.jpeg', '.webp', '.bmp']
	const lowerPath = path.toLowerCase()
	
	// 检查文件扩展名
	const hasValidExtension = imageExtensions.some(ext => lowerPath.includes(ext))
	
	// 检查是否是有效的URL或本地路径
	const isValidUrl = path.startsWith('http://') || path.startsWith('https://') || 
					   path.startsWith('/') || path.startsWith('./') || path.startsWith('../')
	
	return hasValidExtension && isValidUrl
}

/**
 * 处理头像上传
 * @param {Object} options - 上传选项
 * @returns {Promise} 上传结果
 */
export function chooseAndUploadAvatar(options = {}) {
	return new Promise((resolve, reject) => {
		const defaultOptions = {
			count: 1,
			sizeType: ['compressed'],
			sourceType: ['album', 'camera'],
			...options
		}
		
		uni.chooseImage({
			...defaultOptions,
			success: (res) => {
				if (res.tempFilePaths && res.tempFilePaths.length > 0) {
					const avatarPath = res.tempFilePaths[0]
					
					// 验证选择的图片
					if (isValidImagePath(avatarPath)) {
						resolve({
							success: true,
							avatarPath: avatarPath,
							message: '头像选择成功'
						})
					} else {
						reject({
							success: false,
							message: '选择的文件不是有效的图片格式'
						})
					}
				} else {
					reject({
						success: false,
						message: '未选择任何图片'
					})
				}
			},
			fail: (error) => {
				console.error('选择头像失败:', error)
				reject({
					success: false,
					message: '选择头像失败，请重试',
					error: error
				})
			}
		})
	})
}

/**
 * 压缩头像图片
 * @param {string} imagePath - 图片路径
 * @param {Object} options - 压缩选项
 * @returns {Promise} 压缩结果
 */
export function compressAvatar(imagePath, options = {}) {
	return new Promise((resolve, reject) => {
		const defaultOptions = {
			src: imagePath,
			quality: 0.8, // 压缩质量
			width: 400,   // 目标宽度
			height: 400,  // 目标高度
			...options
		}
		
		// 使用uni-app的图片压缩API
		uni.compressImage({
			...defaultOptions,
			success: (res) => {
				resolve({
					success: true,
					compressedPath: res.tempFilePath,
					originalPath: imagePath
				})
			},
			fail: (error) => {
				console.error('压缩头像失败:', error)
				// 压缩失败时返回原图片
				resolve({
					success: false,
					compressedPath: imagePath,
					originalPath: imagePath,
					error: error
				})
			}
		})
	})
}

/**
 * 保存头像到本地
 * @param {string} tempPath - 临时文件路径
 * @returns {Promise} 保存结果
 */
export function saveAvatarToLocal(tempPath) {
	return new Promise((resolve, reject) => {
		const fileName = `avatar_${Date.now()}.jpg`

		uni.saveFile({
			tempFilePath: tempPath,
			success: (res) => {
				resolve({
					success: true,
					savedPath: res.savedFilePath,
					fileName: fileName
				})
			},
			fail: (error) => {
				console.error('保存头像失败:', error)
				reject({
					success: false,
					message: '保存头像失败',
					error: error
				})
			}
		})
	})
}

/**
 * 上传头像到云存储并更新用户信息
 * @param {string} tempPath - 临时文件路径
 * @param {string} uid - 用户ID
 * @returns {Promise} 上传结果
 */
export function uploadAvatarToCloud(tempPath, uid) {
	return new Promise((resolve, reject) => {
		// 首先上传文件到云存储
		uniCloud.callFunction({
			name: 'upload-avatar',
			data: {
				action: 'uploadAvatar',
				filePath: tempPath,
				uid: uid
			}
		}).then(uploadRes => {
			console.log('云存储上传结果:', uploadRes)

			if (uploadRes.result.code === 0) {
				const avatarUrl = uploadRes.result.data.fileID

				// 然后更新用户头像URL到数据库
				return uniCloud.callFunction({
					name: 'upload-avatar',
					data: {
						action: 'updateUserAvatar',
						uid: uid,
						avatarUrl: avatarUrl
					}
				})
			} else {
				throw new Error(uploadRes.result.message || '上传失败')
			}
		}).then(updateRes => {
			console.log('数据库更新结果:', updateRes)

			if (updateRes.result.code === 0) {
				resolve({
					success: true,
					avatarUrl: updateRes.result.data.avatar,
					message: '头像上传成功'
				})
			} else {
				throw new Error(updateRes.result.message || '更新失败')
			}
		}).catch(error => {
			console.error('头像上传失败:', error)
			reject({
				success: false,
				message: error.message || '头像上传失败，请重试',
				error: error
			})
		})
	})
}

/**
 * 获取头像显示尺寸
 * @param {string} size - 尺寸类型 (small, medium, large)
 * @returns {Object} 尺寸对象
 */
export function getAvatarSize(size = 'medium') {
	const sizes = {
		small: { width: '60rpx', height: '60rpx' },
		medium: { width: '120rpx', height: '120rpx' },
		large: { width: '200rpx', height: '200rpx' },
		xlarge: { width: '300rpx', height: '300rpx' }
	}
	
	return sizes[size] || sizes.medium
}

/**
 * 生成头像缓存key
 * @param {string} userId - 用户ID
 * @returns {string} 缓存key
 */
export function getAvatarCacheKey(userId) {
	return `avatar_cache_${userId}`
}

/**
 * 清理头像缓存
 * @param {string} userId - 用户ID（可选，不传则清理所有）
 */
export function clearAvatarCache(userId = null) {
	try {
		if (userId) {
			const cacheKey = getAvatarCacheKey(userId)
			uni.removeStorageSync(cacheKey)
		} else {
			// 清理所有头像缓存
			const storage = uni.getStorageInfoSync()
			storage.keys.forEach(key => {
				if (key.startsWith('avatar_cache_')) {
					uni.removeStorageSync(key)
				}
			})
		}
	} catch (error) {
		console.error('清理头像缓存失败:', error)
	}
}
