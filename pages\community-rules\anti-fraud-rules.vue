<template>
	<view class="page-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="header-content">
				<view class="back-button" @click="goBack">
					<text class="back-icon">‹</text>
				</view>
				<text class="header-title">反作弊规则</text>
				<view class="header-placeholder"></view>
			</view>
		</view>

		<!-- 主要内容 -->
		<view class="main-content">
			<scroll-view scroll-y="true" class="scroll-container">
				<view class="document-content">
					<!-- 文档标题 -->
					<view class="document-header">
						<text class="document-title">闲伴反作弊管理规则</text>
						<text class="document-subtitle">维护公平环境，打击违规行为</text>
						<view class="document-info">
							<text class="info-item">版本：v2.7</text>
							<text class="info-item">发布日期：2025年6月4日</text>
							<text class="info-item">生效日期：2025年6月4日</text>
						</view>
					</view>

					<!-- 第1章 -->
					<view class="content-section">
						<text class="section-title">1. 作弊行为定义</text>
						<view class="section-item">
							<text class="item-number">1.1</text>
							<view class="item-content">
								<text class="item-title">多设备操作</text>
								<text class="item-text">使用多个设备或账号进行刷赞、刷评论、刷关注等虚假互动行为。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.2</text>
							<view class="item-content">
								<text class="item-title">机器人行为</text>
								<text class="item-text">使用自动化脚本、机器人程序进行批量操作和数据造假。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.3</text>
							<view class="item-content">
								<text class="item-title">虚假账号</text>
								<text class="item-text">创建或使用虚假身份账号进行恶意操作和数据刷量。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.4</text>
							<view class="item-content">
								<text class="item-title">数据造假</text>
								<text class="item-text">通过技术手段伪造用户数据、互动数据等平台统计信息。</text>
							</view>
						</view>
					</view>
					
					<!-- 第2章 -->
					<view class="content-section">
						<text class="section-title">2. 检测机制</text>
						<view class="section-item">
							<text class="item-number">2.1</text>
							<view class="item-content">
								<text class="item-title">设备指纹</text>
								<text class="item-text">通过设备指纹技术识别同一设备的多账号异常行为。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.2</text>
							<view class="item-content">
								<text class="item-title">行为分析</text>
								<text class="item-text">分析用户行为模式，识别异常的操作频率和时间规律。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.3</text>
							<view class="item-content">
								<text class="item-title">IP监控</text>
								<text class="item-text">监控IP地址异常，识别批量注册和集中操作行为。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.4</text>
							<view class="item-content">
								<text class="item-title">算法识别</text>
								<text class="item-text">运用机器学习算法实时识别和预警可疑作弊行为。</text>
							</view>
						</view>
					</view>
					
					<!-- 第3章 -->
					<view class="content-section">
						<text class="section-title">3. 处罚措施</text>
						<view class="section-item">
							<text class="item-number">3.1</text>
							<view class="item-content">
								<text class="item-title">数据清零</text>
								<text class="item-text">清除通过作弊获得的虚假数据，包括点赞、评论、关注等。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.2</text>
							<view class="item-content">
								<text class="item-title">功能限制</text>
								<text class="item-text">限制账号的发布、互动、搜索等核心功能使用权限。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.3</text>
							<view class="item-content">
								<text class="item-title">降权处理</text>
								<text class="item-text">降低账号在推荐算法中的权重，减少内容曝光机会。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.4</text>
							<view class="item-content">
								<text class="item-title">账号封禁</text>
								<text class="item-text">严重作弊行为将导致账号临时或永久封禁。</text>
							</view>
						</view>
					</view>
					
					<!-- 第4章 -->
					<view class="content-section">
						<text class="section-title">4. 防范建议</text>
						<view class="section-item">
							<text class="item-number">4.1</text>
							<view class="item-content">
								<text class="item-title">合规使用</text>
								<text class="item-text">严格按照平台规则使用账号，避免任何形式的作弊行为。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.2</text>
							<view class="item-content">
								<text class="item-title">设备管理</text>
								<text class="item-text">合理管理多设备使用，避免在同一设备上频繁切换账号。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.3</text>
							<view class="item-content">
								<text class="item-title">自然互动</text>
								<text class="item-text">保持自然的互动频率和行为模式，避免机械化操作。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.4</text>
							<view class="item-content">
								<text class="item-title">举报配合</text>
								<text class="item-text">发现作弊行为及时举报，配合平台维护公平环境。</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'AntiFraudRules',
	data() {
		return {
			statusBarHeight: 0
		};
	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 0;
	},
	methods: {
		goBack() {
			const pages = getCurrentPages();
			if (pages.length > 1) {
				uni.navigateBack({
					delta: 1
				});
			} else {
				uni.redirectTo({
					url: '/pages/community-rules/community-rules'
				});
			}
		}
	}
};
</script>

<style lang="scss">
.page-container {
	min-height: 100vh;
	background: #f8f9fa;
	display: flex;
	flex-direction: column;
}

.status-bar {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
}

.header {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
	padding: 0 32rpx 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(255, 215, 0, 0.2);

	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;

		.back-button {
			width: 64rpx;
			height: 64rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50%;
			backdrop-filter: blur(10rpx);

			.back-icon {
				font-size: 36rpx;
				color: #333;
				font-weight: bold;
				line-height: 1;
			}
		}

		.header-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			flex: 1;
			text-align: center;
		}

		.header-placeholder {
			width: 64rpx;
		}
	}
}

.main-content {
	flex: 1;
	overflow: hidden;

	.scroll-container {
		height: 100%;
	}
}

.document-content {
	padding: 32rpx;

	.document-header {
		text-align: center;
		margin-bottom: 48rpx;
		padding: 48rpx 32rpx;
		background: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

		.document-title {
			font-size: 48rpx;
			font-weight: 700;
			color: #333;
			display: block;
			margin-bottom: 16rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}

		.document-subtitle {
			font-size: 28rpx;
			color: #999;
			display: block;
			margin-bottom: 32rpx;
			font-style: italic;
		}

		.document-info {
			display: flex;
			justify-content: center;
			flex-wrap: wrap;
			gap: 24rpx;

			.info-item {
				font-size: 24rpx;
				color: #666;
				padding: 8rpx 16rpx;
				background: #f8f9fa;
				border-radius: 8rpx;
			}
		}
	}

	.content-section {
		margin-bottom: 48rpx;
		background: #fff;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

		.section-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			display: block;
			margin-bottom: 32rpx;
			padding: 24rpx 32rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
			border-radius: 12rpx;
			text-align: center;
			box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.2);
		}

		.section-item {
			display: flex;
			margin-bottom: 24rpx;
			align-items: flex-start;

			&:last-child {
				margin-bottom: 0;
			}

			.item-number {
				font-size: 28rpx;
				color: #FFD700;
				font-weight: 600;
				margin-right: 20rpx;
				flex-shrink: 0;
				margin-top: 4rpx;
				min-width: 60rpx;
			}

			.item-content {
				flex: 1;
				line-height: 1.6;

				.item-title {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
					display: block;
					margin-bottom: 12rpx;
				}

				.item-text {
					font-size: 28rpx;
					color: #666;
					line-height: 1.8;
					text-align: justify;
				}
			}
		}
	}
}
</style>
