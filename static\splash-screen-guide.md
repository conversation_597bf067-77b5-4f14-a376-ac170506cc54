# 启动屏幕图片制作指南

## 📱 推荐制作尺寸

### 主要尺寸（必做）
- **1080×1920px** - 最常用的Android手机分辨率
- **750×1334px** - iPhone标准尺寸

### 完整尺寸列表（可选）
#### Android
- hdpi: 480×800px
- xhdpi: 720×1280px  
- xxhdpi: 1080×1920px
- xxxhdpi: 1440×2560px

#### iOS
- iPhone: 750×1334px
- iPhone Plus: 1242×2208px
- iPhone X: 1125×2436px
- iPad: 1536×2048px

## 🎨 设计要求

### 基本要求
- **格式**: PNG
- **背景**: 建议使用与应用主题一致的渐变背景
- **Logo位置**: 居中偏上，距离顶部约1/3位置
- **安全区域**: 四周留出100px边距

### 建议设计
- **背景色**: 使用应用的品牌渐变色
- **Logo**: 居中显示，尺寸约为屏幕宽度的1/4
- **应用名称**: Logo下方显示"趣嗒同行"
- **标语**: 可选添加"探索有趣的生活"等标语

## 📁 文件命名
制作完成后，请将文件保存为：
- `splash-1080x1920.png` (主要尺寸)
- `splash-750x1334.png` (iPhone尺寸)

## 🔧 配置说明
图片制作完成后，我们会：
1. 将图片放入 `/static/` 目录
2. 在 `manifest.json` 中配置启动屏幕
3. 设置2秒显示时间
4. 配置自动关闭
