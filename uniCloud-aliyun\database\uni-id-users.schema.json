{"bsonType": "object", "description": "用户表", "required": ["username", "mobile"], "properties": {"_id": {"description": "ID，系统自动生成"}, "username": {"bsonType": "string", "description": "用户名", "maxLength": 50}, "nickname": {"bsonType": "string", "description": "昵称", "maxLength": 50}, "mobile": {"bsonType": "string", "description": "手机号", "pattern": "^1[3-9]\\d{9}$"}, "mobile_confirmed": {"bsonType": "int", "description": "手机号是否已验证，0未验证 1已验证", "enum": [0, 1]}, "email": {"bsonType": "string", "description": "邮箱地址", "maxLength": 100}, "email_confirmed": {"bsonType": "int", "description": "邮箱是否已验证，0未验证 1已验证", "enum": [0, 1]}, "password": {"bsonType": "string", "description": "密码（加密后）"}, "planet_id": {"bsonType": "string", "description": "星球ID（7位数字）", "maxLength": 10}, "avatar": {"bsonType": "string", "description": "头像URL"}, "gender": {"bsonType": "string", "description": "性别", "enum": ["male", "female", "unknown"]}, "birthday": {"bsonType": "string", "description": "生日", "maxLength": 20}, "location": {"bsonType": "string", "description": "位置信息"}, "ip_location": {"bsonType": "string", "description": "IP位置"}, "bio": {"bsonType": "string", "description": "个人简介", "maxLength": 200}, "level": {"bsonType": "int", "description": "用户等级", "minimum": 1}, "exp": {"bsonType": "int", "description": "经验值", "minimum": 0}, "is_verified": {"bsonType": "bool", "description": "是否认证用户"}, "is_vip": {"bsonType": "bool", "description": "是否VIP用户"}, "vip_level": {"bsonType": "int", "description": "VIP等级", "minimum": 0}, "followers_count": {"bsonType": "int", "description": "粉丝数", "minimum": 0}, "following_count": {"bsonType": "int", "description": "关注数", "minimum": 0}, "likes_count": {"bsonType": "int", "description": "获赞数", "minimum": 0}, "posts_count": {"bsonType": "int", "description": "发帖数", "minimum": 0}, "register_date": {"bsonType": "date", "description": "注册时间"}, "last_login_date": {"bsonType": "date", "description": "最后登录时间"}, "register_ip": {"bsonType": "string", "description": "注册IP"}, "status": {"bsonType": "int", "description": "用户状态，0正常 1禁用", "enum": [0, 1]}, "role": {"bsonType": "array", "description": "用户角色", "items": {"bsonType": "string"}}}}