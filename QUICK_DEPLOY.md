# 快速部署指南

## 问题解决
已修复 `uni-id-common` 依赖问题，现在可以正常部署云函数了。

## 立即部署步骤

### 第一步：确认环境
1. 确保HBuilderX已登录DCloud账号
2. 确认项目已关联到uniCloud服务空间

### 第二步：部署云函数
按以下顺序部署云函数：

1. **部署 simple-test**
   - 右键 `uniCloud-aliyun/cloudfunctions/simple-test`
   - 选择 "上传部署"
   - 等待部署成功

2. **部署 auth**
   - 右键 `uniCloud-aliyun/cloudfunctions/auth`
   - 选择 "上传部署"
   - 等待部署成功

3. **部署 test-db**
   - 右键 `uniCloud-aliyun/cloudfunctions/test-db`
   - 选择 "上传部署"
   - 等待部署成功

4. **部署 user-center**
   - 右键 `uniCloud-aliyun/cloudfunctions/user-center`
   - 选择 "上传部署"
   - 等待部署成功

### 第三步：初始化数据库
1. 右键 `uniCloud-aliyun/database`
2. 选择 "初始化云数据库"
3. 等待初始化完成

### 第四步：验证部署
1. 运行应用
2. 在登录页面点击 "快速诊断"
3. 确认所有项目显示正常

## 如果仍有问题

### 清理缓存重试
1. 关闭HBuilderX
2. 删除项目中的 `node_modules` 文件夹（如果存在）
3. 重新打开HBuilderX
4. 重新部署云函数

### 检查网络
1. 确认网络连接正常
2. 尝试切换网络环境
3. 检查防火墙设置

### 重新关联服务空间
1. 右键项目根目录
2. 选择 "关联云服务空间"
3. 重新选择服务空间
4. 重新部署

## 部署成功标志
- 所有云函数上传无错误
- 数据库初始化完成
- 应用内网络诊断显示正常
- 可以正常注册/登录

## 联系支持
如果按照以上步骤仍无法解决，请提供：
1. HBuilderX版本号
2. 具体错误信息
3. 网络环境信息
