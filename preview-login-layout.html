<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码登录页面布局调整预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .title {
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 40px;
            color: #FFD700;
        }
        
        .comparison {
            display: flex;
            gap: 40px;
            justify-content: center;
            align-items: flex-start;
            flex-wrap: wrap;
        }
        
        .phone-mockup {
            width: 300px;
            height: 600px;
            background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 50%, #3498DB 100%);
            border-radius: 30px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        .mockup-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #FFD700;
        }
        
        .status-bar {
            height: 30px;
            background: transparent;
        }
        
        .nav-bar {
            height: 50px;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }
        
        .back-button {
            width: 35px;
            height: 35px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            color: white;
            font-size: 18px;
        }
        
        .hello-text {
            position: absolute;
            top: 120px;
            right: 20px;
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        
        .character {
            position: absolute;
            top: 140px;
            right: 30px;
            font-size: 40px;
        }
        
        .welcome-section {
            position: absolute;
            top: 130px;
            left: 20px;
            right: 100px;
            text-align: left;
        }

        .welcome-title {
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 8px;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .welcome-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }

        .main-section {
            position: absolute;
            top: 190px;
            left: 20px;
            right: 20px;
            bottom: 20px;
        }

        .red-box {
            position: absolute;
            border: 2px solid #FF4444;
            border-radius: 10px;
            background: rgba(255, 68, 68, 0.1);
        }

        .red-box-1 {
            top: 120px;
            left: 15px;
            right: 95px;
            height: 80px;
        }

        .red-box-2 {
            top: 185px;
            left: 15px;
            right: 15px;
            height: 60px;
        }
        
        .password-input {
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 0 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            color: white;
            font-size: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .login-btn {
            height: 50px;
            background: linear-gradient(135deg, #BBB2FF 0%, #E3D2FF 50%, #A7C0FF 100%);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 25px;
            box-shadow: 0 6px 20px rgba(187, 178, 255, 0.4);
            color: white;
            font-size: 16px;
            font-weight: 600;
        }
        
        .forgot-password {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .forgot-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            text-decoration: underline;
        }
        
        .divider {
            display: flex;
            align-items: center;
            margin: 30px 0 25px;
        }
        
        .divider-line {
            flex: 1;
            height: 1px;
            background: rgba(255, 255, 255, 0.3);
        }
        
        .divider-text {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            margin: 0 15px;
        }
        
        .other-methods {
            display: flex;
            justify-content: center;
        }
        
        .method-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 25px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 25px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .method-icon {
            font-size: 20px;
            margin-bottom: 8px;
        }
        
        .method-text {
            color: white;
            font-size: 12px;
            font-weight: 500;
        }
        
        .changes-list {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .changes-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #FFD700;
            text-align: center;
        }
        
        .change-item {
            margin-bottom: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #FFD700;
        }
        
        .change-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #FFD700;
        }
        
        .change-desc {
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.5;
        }
        
        .old-layout {
            opacity: 0.7;
        }
        
        .old-welcome {
            position: absolute;
            top: 200px;
            left: 20px;
            right: 20px;
            text-align: center;
        }
        
        .old-main {
            position: absolute;
            top: 300px;
            left: 20px;
            right: 20px;
            bottom: 20px;
        }
        
        .old-other {
            position: absolute;
            bottom: 40px;
            left: 20px;
            right: 20px;
            text-align: center;
        }
        
        .old-other-text {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 15px;
        }
        
        .old-method {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            display: inline-block;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">📱 密码登录页面布局调整对比</h1>
        
        <div class="comparison">
            <div>
                <div class="mockup-title">❌ 调整前</div>
                <div class="phone-mockup old-layout">
                    <div class="status-bar"></div>
                    <div class="nav-bar">
                        <div class="back-button">←</div>
                    </div>
                    <div class="hello-text">HELLO!</div>
                    <div class="character">🐻</div>
                    
                    <div class="old-welcome">
                        <div class="welcome-title">欢迎回来！</div>
                        <div class="welcome-subtitle">166****1214 已注册</div>
                    </div>
                    
                    <div class="old-main">
                        <div class="password-input">请输入登录密码</div>
                        <div class="login-btn">登录</div>
                        <div class="forgot-password">
                            <span class="forgot-text">忘记密码？</span>
                        </div>
                    </div>
                    
                    <div class="old-other">
                        <div class="old-other-text">其他登录方式</div>
                        <div class="old-method">验证码登录</div>
                    </div>
                </div>
            </div>
            
            <div>
                <div class="mockup-title">✅ 精确调整后</div>
                <div class="phone-mockup">
                    <div class="status-bar"></div>
                    <div class="nav-bar">
                        <div class="back-button">←</div>
                    </div>
                    <div class="hello-text">HELLO!</div>
                    <div class="character">🐻</div>

                    <!-- 红框标记 -->
                    <div class="red-box red-box-1"></div>
                    <div class="red-box red-box-2"></div>

                    <div class="welcome-section">
                        <div class="welcome-title">欢迎回来！</div>
                        <div class="welcome-subtitle">166****1214 已注册</div>
                    </div>

                    <div class="main-section">
                        <div class="password-input">请输入登录密码</div>
                        <div class="login-btn">登录</div>
                        <div class="forgot-password">
                            <span class="forgot-text">忘记密码？</span>
                        </div>

                        <div class="divider">
                            <div class="divider-line"></div>
                            <div class="divider-text">其他登录方式</div>
                            <div class="divider-line"></div>
                        </div>

                        <div class="other-methods">
                            <div class="method-item">
                                <div class="method-icon">📱</div>
                                <div class="method-text">验证码登录</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="changes-list">
            <div class="changes-title">🎯 布局调整详情</div>
            
            <div class="change-item">
                <div class="change-title">1. 欢迎文字呼吸感布局 🌬️</div>
                <div class="change-desc">
                    • 最终位置：top: 260rpx，在Hello！和深蓝色框之间<br>
                    • 与Hello！保持适当距离，避免过于紧凑<br>
                    • 与密码输入框保持合理间距，营造呼吸感<br>
                    • 右边距：right: 200rpx，为角色留出空间
                </div>
            </div>

            <div class="change-item">
                <div class="change-title">2. 密码输入框位置精调 📍</div>
                <div class="change-desc">
                    • 位置大幅上移：margin-top: 380rpx（原500rpx）<br>
                    • 现在位置在第二个红框区域内<br>
                    • 与欢迎文字保持合适间距<br>
                    • 仍在深蓝色背景区域，视觉效果佳
                </div>
            </div>
            
            <div class="change-item">
                <div class="change-title">3. 登录按钮样式升级</div>
                <div class="change-desc">
                    • 采用新的紫色渐变配色（#BBB2FF → #E3D2FF → #A7C0FF）<br>
                    • 增强了阴影效果和文字阴影<br>
                    • 与整体设计风格保持一致
                </div>
            </div>
            
            <div class="change-item">
                <div class="change-title">4. 底部区域重新设计</div>
                <div class="change-desc">
                    • 增加了优雅的分割线设计<br>
                    • "验证码登录"改为卡片式布局，添加图标<br>
                    • 增加了间距，避免布局紧凑<br>
                    • 添加了交互动画效果
                </div>
            </div>
            
            <div class="change-item">
                <div class="change-title">5. 整体视觉优化</div>
                <div class="change-desc">
                    • 所有元素都在深蓝色区域内，视觉更统一<br>
                    • 增强了毛玻璃效果和透明度<br>
                    • 优化了字体权重和阴影效果<br>
                    • 提升了整体的现代感和专业度
                </div>
            </div>
        </div>
    </div>
</body>
</html>
