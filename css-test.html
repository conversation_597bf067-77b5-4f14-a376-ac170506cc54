<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>CSS语法测试</title>
    <style>
        /* 测试CSS语法是否正确 */
        .test-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 16px;
            margin: 20px;
        }
        
        .test-item {
            background: #fff;
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .test-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
        
        .test-button {
            background: #ff4757;
            color: #fff;
            padding: 12px 24px;
            border-radius: 20px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #ff3742;
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-item">
            <div class="test-title">CSS语法测试</div>
            <div class="test-desc">如果你能看到这个页面正常显示，说明CSS语法没有问题。</div>
            <button class="test-button" onclick="alert('CSS正常工作！')">测试按钮</button>
        </div>
        
        <div class="test-item">
            <div class="test-title">优惠券页面状态</div>
            <div class="test-desc">优惠券页面的CSS语法错误已经修复。如果还看到报错，请尝试：</div>
            <ul>
                <li>重启HBuilderX</li>
                <li>清理项目缓存</li>
                <li>重新编译项目</li>
            </ul>
        </div>
        
        <div class="test-item">
            <div class="test-title">修复内容</div>
            <div class="test-desc">
                ✅ 修复了CSS中的 // 注释语法错误<br>
                ✅ 删除了多余的大括号和空行<br>
                ✅ 重新创建了完整的优惠券页面<br>
                ✅ 简化了页面设计，删除了复杂背景色<br>
            </div>
        </div>
    </div>
</body>
</html>
