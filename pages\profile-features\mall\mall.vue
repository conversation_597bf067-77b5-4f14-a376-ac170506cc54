<template>
	<view class="mall-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<uni-icons type="left" size="24" color="#333"></uni-icons>
					<text class="back-text">返回</text>
				</view>
				<text class="nav-title">闲伴商城</text>
				<view class="nav-right">
					<view class="search-btn" @tap="searchProducts">
						<uni-icons type="search" size="20" color="#666"></uni-icons>
					</view>
					<view class="cart-btn" @tap="goToCart">
						<uni-icons type="cart" size="20" color="#666"></uni-icons>
						<view class="cart-badge" v-if="cartCount > 0">{{ cartCount }}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 轮播图 -->
		<view class="banner-section">
			<swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
				<swiper-item v-for="(banner, index) in banners" :key="index">
					<image class="banner-image" :src="banner.image" mode="aspectFill" @tap="bannerClick(banner)"></image>
				</swiper-item>
			</swiper>
		</view>

		<!-- 快捷入口 -->
		<view class="quick-entry">
			<view class="entry-container">
				<view class="entry-item" v-for="entry in quickEntries" :key="entry.id" @tap="entryClick(entry)">
					<view class="entry-icon" :class="entry.type">
						<uni-icons :type="entry.icon" size="28" color="#fff"></uni-icons>
					</view>
					<text class="entry-text">{{ entry.name }}</text>
				</view>
			</view>
		</view>

		<!-- 商品分类 -->
		<view class="category-tabs">
			<scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false">
				<view class="tab-item" 
					v-for="(category, index) in categories" 
					:key="index"
					:class="{ active: currentCategory === index }"
					@tap="switchCategory(index)">
					<text class="tab-text">{{ category.name }}</text>
				</view>
			</scroll-view>
		</view>

		<!-- 商品列表 -->
		<scroll-view class="products-content" scroll-y="true" @scrolltolower="loadMore">
			<!-- 推荐商品 -->
			<view class="featured-section" v-if="currentCategory === 0">
				<view class="section-header">
					<text class="section-title">精选推荐</text>
					<text class="section-subtitle">为你精心挑选</text>
				</view>
				<view class="featured-products">
					<view class="featured-item" v-for="product in featuredProducts" :key="product.id" @tap="viewProduct(product)">
						<image class="featured-image" :src="product.image" mode="aspectFill"></image>
						<view class="featured-info">
							<text class="featured-title">{{ product.name }}</text>
							<text class="featured-desc">{{ product.description }}</text>
							<view class="featured-price">
								<text class="price-current">¥{{ product.price }}</text>
								<text class="price-original" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
							</view>
						</view>
						<view class="featured-tag" v-if="product.tag">
							<text class="tag-text">{{ product.tag }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 商品网格 -->
			<view class="products-grid">
				<view class="product-item" v-for="product in currentProducts" :key="product.id" @tap="viewProduct(product)">
					<view class="product-image-container">
						<image class="product-image" :src="product.image" mode="aspectFill"></image>
						<view class="product-badge" v-if="product.badge">
							<text class="badge-text">{{ product.badge }}</text>
						</view>
					</view>
					<view class="product-info">
						<text class="product-name">{{ product.name }}</text>
						<text class="product-desc">{{ product.description }}</text>
						<view class="product-price">
							<text class="price-current">¥{{ product.price }}</text>
							<text class="price-original" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
						</view>
						<view class="product-stats">
							<text class="sales-count">已售{{ product.sales }}</text>
							<view class="rating">
								<uni-icons type="star-filled" size="12" color="#ffc107"></uni-icons>
								<text class="rating-score">{{ product.rating }}</text>
							</view>
						</view>
					</view>
					<view class="add-to-cart" @tap.stop="addToCart(product)">
						<uni-icons type="plus" size="16" color="#fff"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore">
				<text class="load-text">加载更多商品...</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				currentCategory: 0,
				hasMore: true,
				cartCount: 3,
				
				// 轮播图
				banners: [
					{
						id: 1,
						image: 'https://s1.imagehub.cc/images/2025/05/26/banner1.png',
						title: '新用户专享',
						link: '/pages/activity/newuser'
					},
					{
						id: 2,
						image: 'https://s1.imagehub.cc/images/2025/05/26/banner2.png',
						title: 'VIP会员特惠',
						link: '/pages/profile-features/vip/vip'
					},
					{
						id: 3,
						image: 'https://s1.imagehub.cc/images/2025/05/26/banner3.png',
						title: '年终大促',
						link: '/pages/activity/promotion'
					}
				],

				// 快捷入口
				quickEntries: [
					{ id: 1, name: '会员中心', icon: 'star-filled', type: 'vip', link: '/pages/profile-features/vip/vip' },
					{ id: 2, name: '优惠券', icon: 'gift', type: 'coupon', link: '/pages/profile-features/coupons/coupons' },
					{ id: 3, name: '积分商城', icon: 'medal', type: 'points', link: '/pages/mall/points' },
					{ id: 4, name: '限时抢购', icon: 'fire', type: 'flash', link: '/pages/mall/flash-sale' }
				],

				// 商品分类
				categories: [
					{ name: '推荐', id: 'featured' },
					{ name: '会员服务', id: 'vip' },
					{ name: '周边商品', id: 'merchandise' },
					{ name: '数码配件', id: 'digital' },
					{ name: '生活用品', id: 'lifestyle' }
				],

				// 精选商品
				featuredProducts: [
					{
						id: 'f001',
						name: '闲伴年度VIP',
						description: '享受全年专属特权',
						price: '168.00',
						originalPrice: '298.00',
						image: 'https://s1.imagehub.cc/images/2025/05/26/vip-annual.png',
						tag: '限时特惠'
					},
					{
						id: 'f002',
						name: '闲伴定制礼品盒',
						description: '精美包装，送礼首选',
						price: '299.00',
						originalPrice: '399.00',
						image: 'https://s1.imagehub.cc/images/2025/05/26/gift-box-deluxe.png',
						tag: '新品上市'
					}
				],

				// 所有商品
				allProducts: [
					{
						id: 'p001',
						name: '闲伴黄金会员',
						description: '1个月专享特权',
						price: '19.00',
						originalPrice: '29.00',
						image: 'https://s1.imagehub.cc/images/2025/05/26/vip-gold.png',
						category: 'vip',
						badge: '热销',
						sales: '1.2k',
						rating: '4.9'
					},
					{
						id: 'p002',
						name: '闲伴钻石会员',
						description: '3个月超值套餐',
						price: '49.00',
						originalPrice: '87.00',
						image: 'https://s1.imagehub.cc/images/2025/05/26/vip-diamond.png',
						category: 'vip',
						badge: '推荐',
						sales: '856',
						rating: '4.8'
					},
					{
						id: 'p003',
						name: '闲伴定制T恤',
						description: '100%纯棉，舒适透气',
						price: '89.00',
						originalPrice: '129.00',
						image: 'https://s1.imagehub.cc/images/2025/05/26/tshirt.png',
						category: 'merchandise',
						badge: '',
						sales: '432',
						rating: '4.7'
					},
					{
						id: 'p004',
						name: '闲伴无线耳机',
						description: '蓝牙5.0，降噪技术',
						price: '199.00',
						originalPrice: '299.00',
						image: 'https://s1.imagehub.cc/images/2025/05/26/earphones.png',
						category: 'digital',
						badge: '新品',
						sales: '234',
						rating: '4.6'
					}
				]
			}
		},

		computed: {
			currentProducts() {
				if (this.currentCategory === 0) {
					return this.allProducts;
				}
				const categoryId = this.categories[this.currentCategory].id;
				return this.allProducts.filter(product => product.category === categoryId);
			}
		},

		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},

		methods: {
			goBack() {
				uni.switchTab({
					url: '/pages/profile/profile'
				});
			},

			searchProducts() {
				uni.showToast({
					title: '搜索功能开发中',
					icon: 'none'
				});
			},

			goToCart() {
				uni.showToast({
					title: '购物车功能开发中',
					icon: 'none'
				});
			},

			bannerClick(banner) {
				if (banner.link) {
					uni.navigateTo({
						url: banner.link
					});
				}
			},

			entryClick(entry) {
				if (entry.link) {
					uni.navigateTo({
						url: entry.link
					});
				}
			},

			switchCategory(index) {
				this.currentCategory = index;
			},

			viewProduct(product) {
				uni.navigateTo({
					url: `/pages/mall/product-detail?id=${product.id}`
				});
			},

			addToCart(product) {
				this.cartCount++;
				uni.showToast({
					title: '已加入购物车',
					icon: 'success'
				});
			},

			loadMore() {
				if (!this.hasMore) return;
				
				setTimeout(() => {
					this.hasMore = false;
				}, 1000);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.mall-page {
		background: #f5f5f5;
		min-height: 100vh;
	}

	.status-bar {
		background: #fff;
	}

	.header {
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;

		.nav-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;
			height: 88rpx;

			.nav-left {
				display: flex;
				align-items: center;
				gap: 8rpx;
				flex: 1;

				.back-text {
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				text-align: center;
				flex: 2;
			}

			.nav-right {
				flex: 1;
				display: flex;
				justify-content: flex-end;
				gap: 16rpx;

				.search-btn, .cart-btn {
					width: 48rpx;
					height: 48rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: #f8f9fa;
					border-radius: 24rpx;
					position: relative;
				}

				.cart-badge {
					position: absolute;
					top: -8rpx;
					right: -8rpx;
					background: #ff4757;
					color: #fff;
					font-size: 18rpx;
					padding: 2rpx 6rpx;
					border-radius: 10rpx;
					min-width: 20rpx;
					text-align: center;
					line-height: 1;
				}
			}
		}
	}

	// 轮播图
	.banner-section {
		margin: 16rpx 24rpx;
		border-radius: 16rpx;
		overflow: hidden;

		.banner-swiper {
			height: 320rpx;
			border-radius: 16rpx;

			.banner-image {
				width: 100%;
				height: 100%;
				border-radius: 16rpx;
			}
		}
	}

	// 快捷入口
	.quick-entry {
		background: #fff;
		margin: 16rpx 24rpx;
		border-radius: 16rpx;
		padding: 32rpx 24rpx;

		.entry-container {
			display: flex;
			justify-content: space-around;

			.entry-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 12rpx;

				.entry-icon {
					width: 64rpx;
					height: 64rpx;
					border-radius: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					&.vip {
						background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
					}

					&.coupon {
						background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
					}

					&.points {
						background: linear-gradient(135deg, #4ecdc4 0%, #6bcf7f 100%);
					}

					&.flash {
						background: linear-gradient(135deg, #ff9500 0%, #ff5722 100%);
					}
				}

				.entry-text {
					font-size: 24rpx;
					color: #333;
					font-weight: 500;
				}
			}
		}
	}

	// 商品分类
	.category-tabs {
		background: #fff;
		margin: 16rpx 24rpx;
		border-radius: 16rpx;
		padding: 8rpx;

		.tabs-scroll {
			white-space: nowrap;

			.tab-item {
				display: inline-block;
				padding: 16rpx 24rpx;
				margin-right: 8rpx;
				border-radius: 12rpx;
				transition: all 0.3s ease;

				&.active {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

					.tab-text {
						color: #fff;
						font-weight: 600;
					}
				}

				.tab-text {
					font-size: 26rpx;
					color: #666;
					transition: all 0.3s ease;
				}
			}
		}
	}

	// 商品内容区域
	.products-content {
		flex: 1;
		padding: 0 24rpx 120rpx;

		// 精选推荐
		.featured-section {
			background: #fff;
			border-radius: 16rpx;
			padding: 24rpx;
			margin-bottom: 24rpx;

			.section-header {
				margin-bottom: 24rpx;

				.section-title {
					font-size: 32rpx;
					color: #333;
					font-weight: 700;
					display: block;
					margin-bottom: 8rpx;
				}

				.section-subtitle {
					font-size: 24rpx;
					color: #999;
				}
			}

			.featured-products {
				.featured-item {
					position: relative;
					background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
					border-radius: 16rpx;
					padding: 24rpx;
					margin-bottom: 16rpx;
					display: flex;
					align-items: center;
					overflow: hidden;

					&:last-child {
						margin-bottom: 0;
					}

					.featured-image {
						width: 120rpx;
						height: 120rpx;
						border-radius: 12rpx;
						margin-right: 16rpx;
						background: #fff;
					}

					.featured-info {
						flex: 1;

						.featured-title {
							font-size: 28rpx;
							color: #333;
							font-weight: 600;
							display: block;
							margin-bottom: 8rpx;
						}

						.featured-desc {
							font-size: 22rpx;
							color: #666;
							display: block;
							margin-bottom: 12rpx;
						}

						.featured-price {
							display: flex;
							align-items: baseline;
							gap: 8rpx;

							.price-current {
								font-size: 32rpx;
								color: #ff4757;
								font-weight: 700;
							}

							.price-original {
								font-size: 22rpx;
								color: #999;
								text-decoration: line-through;
							}
						}
					}

					.featured-tag {
						position: absolute;
						top: 16rpx;
						right: 16rpx;
						background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
						color: #fff;
						font-size: 20rpx;
						padding: 6rpx 12rpx;
						border-radius: 12rpx;

						.tag-text {
							font-size: 20rpx;
						}
					}
				}
			}
		}

		// 商品网格
		.products-grid {
			display: flex;
			flex-wrap: wrap;
			gap: 16rpx;

			.product-item {
				width: calc(50% - 8rpx);
				background: #fff;
				border-radius: 16rpx;
				overflow: hidden;
				position: relative;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.product-image-container {
					position: relative;
					height: 300rpx;

					.product-image {
						width: 100%;
						height: 100%;
						background: #f5f5f5;
					}

					.product-badge {
						position: absolute;
						top: 12rpx;
						left: 12rpx;
						background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
						color: #fff;
						font-size: 18rpx;
						padding: 4rpx 8rpx;
						border-radius: 8rpx;

						.badge-text {
							font-size: 18rpx;
						}
					}
				}

				.product-info {
					padding: 16rpx;

					.product-name {
						font-size: 26rpx;
						color: #333;
						font-weight: 600;
						display: block;
						margin-bottom: 8rpx;
						line-height: 1.3;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					.product-desc {
						font-size: 22rpx;
						color: #666;
						display: block;
						margin-bottom: 12rpx;
						line-height: 1.3;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					.product-price {
						display: flex;
						align-items: baseline;
						gap: 8rpx;
						margin-bottom: 12rpx;

						.price-current {
							font-size: 28rpx;
							color: #ff4757;
							font-weight: 700;
						}

						.price-original {
							font-size: 20rpx;
							color: #999;
							text-decoration: line-through;
						}
					}

					.product-stats {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.sales-count {
							font-size: 20rpx;
							color: #999;
						}

						.rating {
							display: flex;
							align-items: center;
							gap: 4rpx;

							.rating-score {
								font-size: 20rpx;
								color: #999;
							}
						}
					}
				}

				.add-to-cart {
					position: absolute;
					bottom: 16rpx;
					right: 16rpx;
					width: 48rpx;
					height: 48rpx;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					border-radius: 24rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);

					&:active {
						transform: scale(0.9);
					}
				}
			}
		}

		// 加载更多
		.load-more {
			text-align: center;
			padding: 32rpx;

			.load-text {
				font-size: 26rpx;
				color: #999;
			}
		}
	}
</style>
