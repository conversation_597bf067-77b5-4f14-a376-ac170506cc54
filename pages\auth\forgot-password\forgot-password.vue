<template>
	<view class="container">
		<!-- 背景图片 -->
		<image src="/static/wangjimima.png" class="background-image" mode="aspectFill"></image>

		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="back-button" @click="goBack">
				<image src="/static/fanhuilogo.png" class="back-icon" mode="aspectFit"></image>
			</view>
		</view>

		<!-- 主要内容 -->
		<view class="main-content">
			<!-- 标题 -->
			<view class="title-section">
				<text class="main-title">重置密码</text>
				<text class="sub-title">通过手机验证码重置您的密码</text>
			</view>

			<!-- 步骤指示器 -->
			<view class="steps-indicator">
				<view class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
					<view class="step-number">1</view>
					<text class="step-text">验证手机</text>
				</view>
				<view class="step-line" :class="{ active: currentStep > 1 }"></view>
				<view class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
					<view class="step-number">2</view>
					<text class="step-text">重置密码</text>
				</view>
				<view class="step-line" :class="{ active: currentStep > 2 }"></view>
				<view class="step" :class="{ active: currentStep >= 3 }">
					<view class="step-number">3</view>
					<text class="step-text">完成</text>
				</view>
			</view>

			<!-- 步骤1: 验证手机号 -->
			<view v-if="currentStep === 1" class="step-content">
				<view class="input-section">
					<view class="phone-input-container">
						<view class="country-code-selector">
							<text class="country-code">+86</text>
						</view>
						<input
							class="phone-input"
							type="number"
							placeholder="请输入手机号"
							placeholder-style="color: #999999"
							v-model="phoneNumber"
							maxlength="11"
						/>
					</view>
					<view class="verify-btn" @click="sendVerifyCode" :class="{ disabled: !canSendCode }">
						<text class="verify-btn-text">{{ codeButtonText }}</text>
					</view>
					
					<view v-if="codeSent" class="code-input-container">
						<input
							class="code-input"
							type="number"
							placeholder="请输入验证码"
							placeholder-style="color: #999999"
							v-model="verifyCode"
							maxlength="6"
						/>
					</view>
				</view>
				
				<view class="next-btn" @click="verifyPhone" :class="{ disabled: !canVerifyPhone }">
					<text class="next-btn-text">下一步</text>
				</view>
			</view>

			<!-- 步骤2: 重置密码 -->
			<view v-if="currentStep === 2" class="step-content">
				<view class="input-section">
					<view class="password-input-container">
						<input
							class="password-input"
							:type="showPassword ? 'text' : 'password'"
							placeholder="请输入新密码"
							placeholder-style="color: #999999"
							v-model="newPassword"
						/>
						<view class="eye-icon" @click="togglePassword">
							<image :src="showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'" 
								   class="eye-image" mode="aspectFit"></image>
						</view>
					</view>
					
					<view class="password-input-container">
						<input
							class="password-input"
							:type="showConfirmPassword ? 'text' : 'password'"
							placeholder="请确认新密码"
							placeholder-style="color: #999999"
							v-model="confirmPassword"
						/>
						<view class="eye-icon" @click="toggleConfirmPassword">
							<image :src="showConfirmPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'" 
								   class="eye-image" mode="aspectFit"></image>
						</view>
					</view>
				</view>
				
				<view class="password-tips">
					<text class="tip-title">密码要求：</text>
					<text class="tip-item" :class="{ valid: passwordValidation.length }">• 长度6-20位</text>
					<text class="tip-item" :class="{ valid: passwordValidation.hasLetter }">• 包含字母</text>
					<text class="tip-item" :class="{ valid: passwordValidation.hasNumber }">• 包含数字</text>
				</view>
				
				<view class="next-btn" @click="resetPassword" :class="{ disabled: !canResetPassword }">
					<text class="next-btn-text">重置密码</text>
				</view>
			</view>

			<!-- 步骤3: 完成 -->
			<view v-if="currentStep === 3" class="step-content success-content">
				<view class="success-icon">
					<text class="success-text">✓</text>
				</view>
				<text class="success-title">密码重置成功！</text>
				<text class="success-desc">您的密码已成功重置，请使用新密码登录</text>
				
				<view class="success-btn" @click="goToLogin">
					<text class="success-btn-text">立即登录</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentStep: 1,
			phoneNumber: '',
			verifyCode: '',
			newPassword: '',
			confirmPassword: '',
			showPassword: false,
			showConfirmPassword: false,
			codeSent: false,
			countdown: 0,
			timer: null
		}
	},
	
	computed: {
		canSendCode() {
			return /^1[3-9]\d{9}$/.test(this.phoneNumber) && this.countdown === 0
		},
		
		canVerifyPhone() {
			return /^1[3-9]\d{9}$/.test(this.phoneNumber) && this.verifyCode.length === 6
		},
		
		passwordValidation() {
			return {
				length: this.newPassword.length >= 6 && this.newPassword.length <= 20,
				hasLetter: /[a-zA-Z]/.test(this.newPassword),
				hasNumber: /\d/.test(this.newPassword)
			}
		},
		
		canResetPassword() {
			return this.passwordValidation.length && 
				   this.passwordValidation.hasLetter && 
				   this.passwordValidation.hasNumber &&
				   this.newPassword === this.confirmPassword &&
				   this.confirmPassword.length > 0
		},
		
		codeButtonText() {
			return this.countdown > 0 ? `${this.countdown}s后重发` : '获取验证码'
		}
	},
	
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		sendVerifyCode() {
			if (!this.canSendCode) return
			
			// 模拟发送验证码
			uni.showToast({
				title: '验证码已发送',
				icon: 'success'
			})
			
			this.codeSent = true
			this.startCountdown()
		},
		
		startCountdown() {
			this.countdown = 60
			this.timer = setInterval(() => {
				this.countdown--
				if (this.countdown <= 0) {
					clearInterval(this.timer)
				}
			}, 1000)
		},
		
		verifyPhone() {
			if (!this.canVerifyPhone) return
			
			// 模拟验证手机号
			uni.showToast({
				title: '验证成功',
				icon: 'success'
			})
			
			setTimeout(() => {
				this.currentStep = 2
			}, 1000)
		},
		
		togglePassword() {
			this.showPassword = !this.showPassword
		},
		
		toggleConfirmPassword() {
			this.showConfirmPassword = !this.showConfirmPassword
		},
		
		resetPassword() {
			if (!this.canResetPassword) return
			
			// 模拟重置密码
			uni.showLoading({
				title: '重置中...'
			})
			
			setTimeout(() => {
				uni.hideLoading()
				this.currentStep = 3
			}, 2000)
		},
		
		goToLogin() {
			uni.navigateBack({
				delta: 2
			})
		}
	},
	
	onUnload() {
		if (this.timer) {
			clearInterval(this.timer)
		}
	}
}
</script>

<style scoped>
@import '@/styles/global.scss';

/* 强制消除所有可能的边距和留白 */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

page, body, html, uni-page-body, uni-page, uni-page-wrapper {
	margin: 0 !important;
	padding: 0 !important;
	width: 100% !important;
	height: 100% !important;
	overflow: hidden !important;
	border: none !important;
	outline: none !important;
}

.container {
	position: fixed;
	top: -10px;
	left: -10px;
	right: -10px;
	bottom: -10px;
	width: calc(100vw + 20px);
	height: calc(100vh + 20px);
	overflow: hidden;
	margin: 0;
	padding: 10px;
}

.background-image {
	width: calc(100% + 20px);
	height: calc(100% + 20px);
	position: fixed;
	top: -10px;
	left: -10px;
	z-index: 1;
}

.nav-bar {
	position: fixed;
	top: var(--status-bar-height);
	left: 0;
	right: 0;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	padding: 0 var(--spacing-lg);
	z-index: 100;
	background: transparent;
}

.back-button {
	width: 50rpx;
	height: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.3);
	border-radius: 50%;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
}

.back-button:active {
	transform: scale(0.95);
	background: rgba(0, 0, 0, 0.5);
}

.back-icon {
	width: 28rpx;
	height: 28rpx;
	filter: brightness(0) invert(1);
}

.main-content {
	position: fixed;
	top: calc(var(--status-bar-height) + 88rpx);
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 10;
	overflow-y: auto;
	padding: 600rpx 30rpx 60rpx;
	box-sizing: border-box;
}

.title-section {
	text-align: center;
	margin-bottom: 40rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	backdrop-filter: blur(20rpx);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.main-title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 16rpx;
}

.sub-title {
	display: block;
	font-size: 28rpx;
	color: #666666;
}

.steps-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	padding: 0 20rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx 20rpx;
	backdrop-filter: blur(20rpx);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.step {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.step-number {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #E0E0E0;
	color: #999999;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 16rpx;
	transition: all 0.3s ease;
}

.step.active .step-number {
	background: #66D4C8;
	color: #FFFFFF;
}

.step.completed .step-number {
	background: #4CAF50;
	color: #FFFFFF;
}

.step-text {
	font-size: 24rpx;
	color: #999999;
	text-align: center;
}

.step.active .step-text {
	color: #66D4C8;
	font-weight: 500;
}

.step-line {
	flex: 1;
	height: 4rpx;
	background: #E0E0E0;
	margin: 0 20rpx;
	margin-bottom: 40rpx;
	transition: all 0.3s ease;
}

.step-line.active {
	background: #66D4C8;
}

.step-content {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 30rpx;
	padding: 60rpx 40rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(20rpx);
}

.input-section {
	margin-bottom: 60rpx;
}

.phone-input-container, .code-input-container, .password-input-container {
	position: relative;
	display: flex;
	align-items: center;
	height: 120rpx;
	background: #FFFFFF;
	border-radius: 60rpx;
	padding: 0 40rpx;
	border: 3rpx solid #66D4C8;
	margin-bottom: 30rpx;
	margin-left: 0;
	margin-right: 0;
	box-shadow: 0 4rpx 16rpx rgba(102, 212, 200, 0.2);
}

.country-code-selector {
	display: flex;
	align-items: center;
	margin-right: 20rpx;
	padding-right: 20rpx;
	border-right: 2rpx solid #E0E0E0;
}

.country-code {
	color: #333333;
	font-size: 36rpx;
	font-weight: bold;
	margin-right: 10rpx;
}

.phone-input, .code-input, .password-input {
	flex: 1;
	height: 120rpx;
	padding: 0 20rpx;
	color: #333333;
	font-size: 36rpx;
	font-weight: bold;
	background: transparent;
	border: none;
	outline: none;
}

.verify-btn, .next-btn {
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 100%);
	border-radius: 60rpx;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 0;
	margin-right: 0;
	transition: all 0.3s ease;
	box-shadow: 0 8rpx 32rpx rgba(102, 212, 200, 0.4);
}

.verify-btn:active, .next-btn:active {
	transform: scale(0.98);
}

.verify-btn.disabled, .next-btn.disabled {
	opacity: 0.5;
	pointer-events: none;
}

.verify-btn-text, .next-btn-text {
	color: #FFFFFF;
	font-size: 36rpx;
	font-weight: bold;
}

.eye-icon {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.eye-image {
	width: 40rpx;
	height: 40rpx;
}

.password-tips {
	background: rgba(102, 212, 200, 0.1);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 40rpx;
}

.tip-title {
	display: block;
	font-size: 28rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 20rpx;
}

.tip-item {
	display: block;
	font-size: 26rpx;
	color: #999999;
	margin-bottom: 10rpx;
	transition: all 0.3s ease;
}

.tip-item.valid {
	color: #4CAF50;
}

.success-content {
	text-align: center;
}

.success-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: #4CAF50;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 40rpx;
}

.success-text {
	color: #FFFFFF;
	font-size: 60rpx;
	font-weight: bold;
}

.success-title {
	display: block;
	font-size: 40rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 20rpx;
}

.success-desc {
	display: block;
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 60rpx;
}

.success-btn {
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 100%);
	border-radius: 60rpx;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 0;
	margin-right: 0;
	box-shadow: 0 8rpx 32rpx rgba(102, 212, 200, 0.4);
}

.success-btn-text {
	color: #FFFFFF;
	font-size: 36rpx;
	font-weight: bold;
}
</style>
