<template>
	<view class="works-page">
		<!-- 状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left" @click="goBack">
				<view class="back-btn">
					<uni-icons type="left" size="18" color="#fff"></uni-icons>
				</view>
			</view>
			<view class="nav-center">
				<text class="nav-title">我的作品</text>
				<text class="nav-subtitle">共{{ totalWorks }}个作品</text>
			</view>
			<view class="nav-right">
				<view class="layout-btn" @click="toggleLayout">
					<uni-icons :type="isGridLayout ? 'list' : 'grid'" size="18" color="#fff"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 统计概览 -->
		<view class="overview-section">
			<view class="overview-container">
				<view class="overview-item">
					<text class="overview-number">{{ totalViews }}</text>
					<text class="overview-label">总浏览</text>
				</view>
				<view class="overview-item">
					<text class="overview-number">{{ totalLikes }}</text>
					<text class="overview-label">总点赞</text>
				</view>
				<view class="overview-item">
					<text class="overview-number">{{ totalComments }}</text>
					<text class="overview-label">总评论</text>
				</view>
				<view class="overview-item">
					<text class="overview-number">{{ totalShares }}</text>
					<text class="overview-label">总分享</text>
				</view>
			</view>
		</view>

		<!-- 分类筛选 -->
		<view class="category-filter">
			<scroll-view scroll-x class="filter-scroll" :show-scrollbar="false">
				<view class="filter-container">
					<view 
						class="filter-item" 
						:class="{ active: activeCategory === index }"
						v-for="(category, index) in categories" 
						:key="index"
						@click="switchCategory(index)"
					>
						<uni-icons :type="category.icon" size="16" :color="activeCategory === index ? '#fff' : '#666'"></uni-icons>
						<text class="filter-text">{{ category.name }}</text>
						<text class="filter-count">{{ category.count }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 自定义下拉刷新 -->
		<view class="custom-refresh" v-if="refreshing">
			<view class="refresh-container">
				<view class="stars-animation">
					<view class="star-item star-1">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-2">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-3">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-4">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-5">
						<text class="star-text">✦</text>
					</view>
				</view>
				<text class="refresh-text">正在刷新...</text>
			</view>
		</view>

		<!-- 作品列表 -->
		<scroll-view
			class="works-list"
			scroll-y
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd"
			@scrolltolower="loadMore"
		>
			<!-- 网格布局 -->
			<view v-if="isGridLayout" class="grid-container">
				<view class="grid-list">
					<view 
						class="grid-item" 
						v-for="work in filteredWorks" 
						:key="work.id"
						@click="viewWork(work)"
					>
						<view class="work-cover">
							<image :src="work.cover" class="cover-image"></image>
							<view v-if="work.type === 'video'" class="video-overlay">
								<uni-icons type="play-filled" size="20" color="#fff"></uni-icons>
								<text class="video-duration">{{ work.duration }}</text>
							</view>
							<view class="work-stats">
								<view class="stat-item">
									<uni-icons type="heart-filled" size="12" color="#fff"></uni-icons>
									<text class="stat-text">{{ work.likes }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 列表布局 -->
			<view v-else class="list-container">
				<view 
					class="work-card" 
					v-for="work in filteredWorks" 
					:key="work.id"
					@click="viewWork(work)"
				>
					<view class="card-content">
						<view class="work-cover">
							<image :src="work.cover" class="cover-image"></image>
							<view v-if="work.type === 'video'" class="video-overlay">
								<uni-icons type="play-filled" size="16" color="#fff"></uni-icons>
							</view>
						</view>
						
						<view class="work-info">
							<text class="work-title">{{ work.title }}</text>
							<text class="work-desc">{{ work.description }}</text>
							<view class="work-meta">
								<text class="publish-time">{{ work.publishTime }}</text>
								<view class="status-badge" :class="work.status">
									<text class="status-text">{{ getStatusText(work.status) }}</text>
								</view>
							</view>
							<view class="work-stats">
								<view class="stat-item">
									<uni-icons type="eye" size="12" color="#999"></uni-icons>
									<text class="stat-text">{{ work.views }}</text>
								</view>
								<view class="stat-item">
									<uni-icons type="heart-filled" size="12" color="#FF6B6B"></uni-icons>
									<text class="stat-text">{{ work.likes }}</text>
								</view>
								<view class="stat-item">
									<uni-icons type="chat" size="12" color="#4ECDC4"></uni-icons>
									<text class="stat-text">{{ work.comments }}</text>
								</view>
							</view>
						</view>
						
						<view class="action-section">
							<view class="action-btn" @click.stop="editWork(work)">
								<uni-icons type="compose" size="16" color="#667eea"></uni-icons>
							</view>
							<view class="action-btn" @click.stop="shareWork(work)">
								<uni-icons type="redo" size="16" color="#4ECDC4"></uni-icons>
							</view>
							<view class="action-btn" @click.stop="deleteWork(work)">
								<uni-icons type="trash" size="16" color="#FF6B6B"></uni-icons>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 加载更多 -->
				<view class="load-more" v-if="hasMore">
					<uni-icons type="spinner-cycle" size="20" color="#999"></uni-icons>
					<text class="load-text">加载更多...</text>
				</view>
				
				<!-- 空状态 -->
				<view class="empty-state" v-if="filteredWorks.length === 0 && !loading">
					<view class="empty-icon">
						<uni-icons type="compose" size="60" color="#ddd"></uni-icons>
					</view>
					<text class="empty-title">暂无作品</text>
					<text class="empty-desc">快去创作你的第一个作品吧~</text>
					<view class="create-btn" @click="goToCreate">
						<text class="create-text">去创作</text>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 悬浮创作按钮 -->
		<view class="fab-btn" @click="goToCreate">
			<uni-icons type="plus" size="24" color="#fff"></uni-icons>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				isGridLayout: true,
				activeCategory: 0,
				refreshing: false,
				loading: false,
				hasMore: true,
				touchStartY: 0,
				scrollTop: 0,
				
				totalWorks: 32,
				totalViews: 12567,
				totalLikes: 1234,
				totalComments: 567,
				totalShares: 89,
				
				categories: [
					{ name: '全部', icon: 'list', count: 32 },
					{ name: '图片', icon: 'image', count: 18 },
					{ name: '视频', icon: 'videocam', count: 12 },
					{ name: '文字', icon: 'compose', count: 2 }
				],
				
				works: [
					{
						id: 1,
						title: '今天的夕阳真美',
						description: '在海边看到的绝美夕阳，分享给大家',
						cover: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						type: 'image',
						publishTime: '2天前',
						status: 'published',
						views: 1234,
						likes: 89,
						comments: 23,
						shares: 12
					},
					{
						id: 2,
						title: '王者荣耀五杀集锦',
						description: '最近的精彩操作合集',
						cover: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						type: 'video',
						duration: '02:34',
						publishTime: '1周前',
						status: 'published',
						views: 5678,
						likes: 234,
						comments: 67,
						shares: 45
					}
				]
			}
		},
		
		computed: {
			filteredWorks() {
				if (this.activeCategory === 0) {
					return this.works;
				}
				
				const typeMap = {
					1: 'image',
					2: 'video', 
					3: 'text'
				};
				
				const targetType = typeMap[this.activeCategory];
				return this.works.filter(work => work.type === targetType);
			}
		},
		
		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},
		
		methods: {
			goBack() {
				uni.navigateBack();
			},
			
			toggleLayout() {
				this.isGridLayout = !this.isGridLayout;
			},
			
			switchCategory(index) {
				this.activeCategory = index;
			},

			// 触摸开始
			onTouchStart(e) {
				this.touchStartY = e.touches[0].clientY;
				this.scrollTop = 0;
			},

			// 触摸移动
			onTouchMove(e) {
				const currentY = e.touches[0].clientY;
				const deltaY = currentY - this.touchStartY;

				// 只有在页面顶部且向下拉时才触发刷新
				if (this.scrollTop <= 0 && deltaY > 100 && !this.refreshing) {
					this.triggerRefresh();
				}
			},

			// 触摸结束
			onTouchEnd() {
				this.touchStartY = 0;
			},

			// 触发刷新
			triggerRefresh() {
				this.refreshing = true;
				setTimeout(() => {
					this.refreshing = false;
					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					});
				}, 2000);
			},
			
			loadMore() {
				if (this.hasMore && !this.loading) {
					this.loading = true;
					setTimeout(() => {
						this.loading = false;
					}, 1000);
				}
			},
			
			viewWork(work) {
				console.log('查看作品:', work.title);
			},
			
			editWork(work) {
				console.log('编辑作品:', work.title);
			},
			
			shareWork(work) {
				console.log('分享作品:', work.title);
			},
			
			deleteWork(work) {
				uni.showModal({
					title: '确认删除',
					content: `确定要删除作品"${work.title}"吗？`,
					success: (res) => {
						if (res.confirm) {
							const index = this.works.findIndex(w => w.id === work.id);
							if (index > -1) {
								this.works.splice(index, 1);
								this.totalWorks--;
							}
						}
					}
				});
			},
			
			goToCreate() {
				console.log('去创作');
			},
			
			getStatusText(status) {
				const statusMap = {
					published: '已发布',
					draft: '草稿',
					reviewing: '审核中',
					rejected: '未通过'
				};
				return statusMap[status] || '未知';
			}
		}
	}
</script>

<style lang="scss" scoped>
	.works-page {
		background: #f5f6fa;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		position: relative;
	}
	
	.status-bar {
		background: linear-gradient(135deg, #9B59B6 0%, #BB6BD9 100%);
	}

	// 自定义下拉刷新
	.custom-refresh {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10rpx);

		.refresh-container {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 40rpx 20rpx 20rpx;

			.stars-animation {
				position: relative;
				width: 80rpx;
				height: 80rpx;
				margin-bottom: 16rpx;

				.star-item {
					position: absolute;
					display: flex;
					align-items: center;
					justify-content: center;

					.star-text {
						font-size: 20rpx;
						color: #9B59B6;
						font-weight: bold;
					}

					&.star-1 {
						top: 0;
						left: 50%;
						transform: translateX(-50%);
						animation: starPulse 1.5s ease-in-out infinite;
					}

					&.star-2 {
						top: 20rpx;
						right: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 0.3s;
					}

					&.star-3 {
						bottom: 20rpx;
						right: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 0.6s;
					}

					&.star-4 {
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						animation: starPulse 1.5s ease-in-out infinite 0.9s;
					}

					&.star-5 {
						top: 20rpx;
						left: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 1.2s;
					}
				}
			}

			.refresh-text {
				font-size: 24rpx;
				color: #666;
				font-weight: 500;
			}
		}
	}

	// 顶部导航
	.top-nav {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16rpx 24rpx;
		background: linear-gradient(135deg, #9B59B6 0%, #BB6BD9 100%);
		
		.nav-left, .nav-right {
			.back-btn, .layout-btn {
				width: 40rpx;
				height: 40rpx;
				border-radius: 20rpx;
				background: rgba(255, 255, 255, 0.2);
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				
				&:active {
					background: rgba(255, 255, 255, 0.3);
					transform: scale(0.95);
				}
			}
		}
		
		.nav-center {
			flex: 1;
			text-align: center;
			
			.nav-title {
				font-size: 34rpx;
				font-weight: 700;
				color: #fff;
				display: block;
				margin-bottom: 4rpx;
			}
			
			.nav-subtitle {
				font-size: 22rpx;
				color: rgba(255, 255, 255, 0.8);
			}
		}
	}

	// 统计概览
	.overview-section {
		background: #fff;
		margin: 16rpx 24rpx;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

		.overview-container {
			display: flex;

			.overview-item {
				flex: 1;
				text-align: center;
				padding: 24rpx 16rpx;
				border-right: 1rpx solid #f0f0f0;

				&:last-child {
					border-right: none;
				}

				.overview-number {
					font-size: 32rpx;
					font-weight: 700;
					color: #333;
					display: block;
					margin-bottom: 8rpx;
				}

				.overview-label {
					font-size: 22rpx;
					color: #999;
				}
			}
		}
	}

	// 分类筛选
	.category-filter {
		background: #fff;
		margin: 0 24rpx 16rpx;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

		.filter-scroll {
			white-space: nowrap;

			.filter-container {
				display: flex;
				padding: 16rpx 24rpx;

				.filter-item {
					display: flex;
					align-items: center;
					gap: 8rpx;
					padding: 12rpx 20rpx;
					margin-right: 16rpx;
					border-radius: 20rpx;
					transition: all 0.3s ease;

					&.active {
						background: linear-gradient(135deg, #9B59B6 0%, #BB6BD9 100%);

						.filter-text {
							color: #fff;
						}

						.filter-count {
							background: rgba(255, 255, 255, 0.2);
							color: #fff;
						}
					}

					.filter-text {
						font-size: 24rpx;
						color: #666;
						font-weight: 500;
					}

					.filter-count {
						background: #f0f0f0;
						color: #999;
						font-size: 18rpx;
						padding: 4rpx 8rpx;
						border-radius: 8rpx;
						min-width: 28rpx;
						text-align: center;
					}
				}
			}
		}
	}

	// 作品列表
	.works-list {
		flex: 1;
		padding-bottom: 120rpx; // 为悬浮按钮留空间

		// 网格布局
		.grid-container {
			padding: 0 24rpx;

			.grid-list {
				display: flex;
				flex-wrap: wrap;
				gap: 16rpx;

				.grid-item {
					width: calc(50% - 8rpx);

					.work-cover {
						position: relative;
						width: 100%;
						height: 240rpx;
						border-radius: 16rpx;
						overflow: hidden;

						.cover-image {
							width: 100%;
							height: 100%;
							object-fit: cover;
						}

						.video-overlay {
							position: absolute;
							top: 12rpx;
							right: 12rpx;
							background: rgba(0, 0, 0, 0.6);
							border-radius: 12rpx;
							padding: 6rpx 12rpx;
							display: flex;
							align-items: center;
							gap: 6rpx;

							.video-duration {
								font-size: 20rpx;
								color: #fff;
								font-weight: 500;
							}
						}

						.work-stats {
							position: absolute;
							bottom: 12rpx;
							left: 12rpx;

							.stat-item {
								display: flex;
								align-items: center;
								gap: 6rpx;
								background: rgba(0, 0, 0, 0.6);
								border-radius: 12rpx;
								padding: 6rpx 12rpx;

								.stat-text {
									font-size: 20rpx;
									color: #fff;
									font-weight: 500;
								}
							}
						}
					}
				}
			}
		}

		// 列表布局
		.list-container {
			padding: 0 24rpx;

			.work-card {
				background: #fff;
				border-radius: 20rpx;
				margin-bottom: 16rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
				}

				.card-content {
					display: flex;
					padding: 24rpx;
					gap: 20rpx;

					.work-cover {
						position: relative;
						width: 120rpx;
						height: 120rpx;
						border-radius: 16rpx;
						overflow: hidden;
						flex-shrink: 0;

						.cover-image {
							width: 100%;
							height: 100%;
							object-fit: cover;
						}

						.video-overlay {
							position: absolute;
							top: 50%;
							left: 50%;
							transform: translate(-50%, -50%);
							width: 32rpx;
							height: 32rpx;
							border-radius: 16rpx;
							background: rgba(0, 0, 0, 0.6);
							display: flex;
							align-items: center;
							justify-content: center;
						}
					}

					.work-info {
						flex: 1;
						min-width: 0;

						.work-title {
							font-size: 30rpx;
							font-weight: 600;
							color: #333;
							display: block;
							margin-bottom: 8rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						.work-desc {
							font-size: 24rpx;
							color: #666;
							line-height: 1.4;
							margin-bottom: 12rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							-webkit-box-orient: vertical;
						}

						.work-meta {
							display: flex;
							align-items: center;
							justify-content: space-between;
							margin-bottom: 12rpx;

							.publish-time {
								font-size: 22rpx;
								color: #999;
							}

							.status-badge {
								padding: 4rpx 12rpx;
								border-radius: 12rpx;

								&.published {
									background: rgba(46, 213, 115, 0.1);

									.status-text {
										color: #2ed573;
									}
								}

								&.draft {
									background: rgba(255, 193, 7, 0.1);

									.status-text {
										color: #ffc107;
									}
								}

								&.reviewing {
									background: rgba(52, 152, 219, 0.1);

									.status-text {
										color: #3498db;
									}
								}

								&.rejected {
									background: rgba(231, 76, 60, 0.1);

									.status-text {
										color: #e74c3c;
									}
								}

								.status-text {
									font-size: 20rpx;
									font-weight: 500;
								}
							}
						}

						.work-stats {
							display: flex;
							gap: 20rpx;

							.stat-item {
								display: flex;
								align-items: center;
								gap: 6rpx;

								.stat-text {
									font-size: 22rpx;
									color: #999;
								}
							}
						}
					}

					.action-section {
						display: flex;
						flex-direction: column;
						gap: 12rpx;
						flex-shrink: 0;

						.action-btn {
							width: 44rpx;
							height: 44rpx;
							border-radius: 22rpx;
							background: #f8f9fa;
							display: flex;
							align-items: center;
							justify-content: center;
							transition: all 0.3s ease;

							&:active {
								transform: scale(0.9);
							}
						}
					}
				}
			}

			// 加载更多
			.load-more {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 12rpx;
				padding: 32rpx;

				.load-text {
					font-size: 26rpx;
					color: #999;
				}
			}

			// 空状态
			.empty-state {
				text-align: center;
				padding: 120rpx 40rpx;

				.empty-icon {
					margin-bottom: 24rpx;
				}

				.empty-title {
					font-size: 32rpx;
					color: #666;
					font-weight: 600;
					display: block;
					margin-bottom: 12rpx;
				}

				.empty-desc {
					font-size: 26rpx;
					color: #999;
					line-height: 1.5;
					margin-bottom: 32rpx;
				}

				.create-btn {
					background: linear-gradient(135deg, #9B59B6 0%, #BB6BD9 100%);
					color: #fff;
					padding: 16rpx 32rpx;
					border-radius: 24rpx;
					display: inline-block;

					.create-text {
						font-size: 28rpx;
						font-weight: 600;
						color: #fff;
					}
				}
			}
		}
	}

	// 悬浮创作按钮
	.fab-btn {
		position: fixed;
		bottom: 120rpx;
		right: 32rpx;
		width: 88rpx;
		height: 88rpx;
		border-radius: 44rpx;
		background: linear-gradient(135deg, #9B59B6 0%, #BB6BD9 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 24rpx rgba(155, 89, 182, 0.4);
		transition: all 0.3s ease;
		z-index: 100;

		&:active {
			transform: scale(0.9);
		}
	}

	// 星星脉冲动画
	@keyframes starPulse {
		0%, 100% {
			transform: scale(0.8);
			opacity: 0.5;
		}
		50% {
			transform: scale(1.2);
			opacity: 1;
		}
	}
</style>
