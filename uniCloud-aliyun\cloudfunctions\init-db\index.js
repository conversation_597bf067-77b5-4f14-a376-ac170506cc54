'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	try {
		console.log('开始初始化数据库...');
		
		// 创建 uni-id-users 集合
		try {
			const usersCollection = db.collection('uni-id-users');
			// 尝试插入一条测试数据来确保集合存在
			const testResult = await usersCollection.add({
				_test: true,
				created_at: new Date()
			});
			console.log('uni-id-users 集合创建成功:', testResult);
			
			// 删除测试数据
			await usersCollection.doc(testResult.id).remove();
			console.log('测试数据已删除');
		} catch (error) {
			console.log('uni-id-users 集合可能已存在:', error.message);
		}
		
		// 创建 verification_codes 集合
		try {
			const codesCollection = db.collection('verification_codes');
			const testResult = await codesCollection.add({
				_test: true,
				created_at: new Date()
			});
			console.log('verification_codes 集合创建成功:', testResult);
			
			// 删除测试数据
			await codesCollection.doc(testResult.id).remove();
			console.log('测试数据已删除');
		} catch (error) {
			console.log('verification_codes 集合可能已存在:', error.message);
		}
		
		// 检查集合是否存在
		const collections = await db.collection('uni-id-users').get();
		console.log('uni-id-users 集合检查结果:', collections);
		
		return {
			code: 0,
			message: '数据库初始化成功',
			data: {
				timestamp: new Date(),
				collections: ['uni-id-users', 'verification_codes']
			}
		};
		
	} catch (error) {
		console.error('数据库初始化失败:', error);
		return {
			code: -1,
			message: '数据库初始化失败: ' + error.message,
			error: error
		};
	}
};
