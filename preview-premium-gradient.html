<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲伴 - 高级感不规则渐变预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .demo-container {
            padding: 20px;
        }
        
        .gradient-showcase {
            display: grid;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .gradient-card {
            height: 200px;
            border-radius: 20px;
            padding: 20px;
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
        }
        
        .premium-gradient {
            background: 
                radial-gradient(ellipse 400px 300px at 20% 10%, rgba(255, 248, 220, 0.8) 0%, transparent 50%),
                radial-gradient(ellipse 300px 400px at 80% 70%, rgba(255, 228, 196, 0.6) 0%, transparent 50%),
                linear-gradient(135deg, #FFF8DC 0%, #F5DEB3 40%, #DEB887 100%);
        }
        
        .comparison-old {
            background: linear-gradient(135deg, 
                #FFF9E6 0%, #FFF3CD 15%, #FFE69C 30%, #FFD93D 45%, 
                #6BCF7F 60%, #4D96FF 75%, #9775FA 90%, #FF6B9D 100%
            );
        }
        
        .gradient-title {
            font-size: 24px;
            font-weight: 700;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            margin-bottom: 8px;
        }
        
        .gradient-desc {
            font-size: 14px;
            opacity: 0.9;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
        }
        
        .gradient-features {
            list-style: none;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .gradient-features li {
            margin-bottom: 4px;
        }
        
        .gradient-features li:before {
            content: "✨ ";
            margin-right: 4px;
        }
        
        .profile-demo {
            background: 
                radial-gradient(ellipse 400px 300px at 20% 10%, rgba(255, 248, 220, 0.8) 0%, transparent 50%),
                radial-gradient(ellipse 300px 400px at 80% 70%, rgba(255, 228, 196, 0.6) 0%, transparent 50%),
                linear-gradient(135deg, #FFF8DC 0%, #F5DEB3 40%, #DEB887 100%);
            border-radius: 20px;
            padding: 20px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: transparent;
        }
        
        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
        }
        
        .circle-1 {
            width: 80px;
            height: 80px;
            top: 20px;
            right: 30px;
            animation: float 6s ease-in-out infinite;
        }
        
        .circle-2 {
            width: 50px;
            height: 50px;
            top: 120px;
            left: 40px;
            animation: float 8s ease-in-out infinite reverse;
        }
        
        .circle-3 {
            width: 30px;
            height: 30px;
            top: 80px;
            left: 150px;
            animation: float 7s ease-in-out infinite;
        }
        
        .profile-content {
            position: relative;
            z-index: 2;
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-top: 20px;
        }
        
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            border: 3px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            object-fit: cover;
        }
        
        .user-info {
            flex: 1;
            color: white;
        }
        
        .user-name {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        .user-id {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
        }
        
        .user-desc {
            font-size: 15px;
            opacity: 0.95;
            line-height: 1.4;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
        }
        
        .stats-demo {
            display: flex;
            justify-content: space-around;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
            color: white;
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: 700;
            display: block;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-15px);
            }
        }
        
        .comparison-title {
            text-align: center;
            font-size: 28px;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
        }
        
        .vs-badge {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: 600;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="comparison-title">🎨 高级感渐变色对比</h1>
        
        <div class="gradient-showcase">
            <div class="gradient-card premium-gradient">
                <div>
                    <div class="gradient-title">✨ 新版 - 高级感不规则渐变</div>
                    <div class="gradient-desc">三色温暖系渐变 + 不规则径向光晕</div>
                </div>
                <ul class="gradient-features">
                    <li>温暖的奶茶色系，高级感十足</li>
                    <li>不规则径向渐变，自然光影效果</li>
                    <li>三色搭配，简约而不简单</li>
                    <li>符合大厂设计趋势</li>
                </ul>
            </div>
            
            <div class="vs-badge">VS</div>
            
            <div class="gradient-card comparison-old">
                <div>
                    <div class="gradient-title">❌ 旧版 - 规则彩虹渐变</div>
                    <div class="gradient-desc">八色规则渐变，过于花哨</div>
                </div>
                <ul class="gradient-features">
                    <li>颜色过多，视觉混乱</li>
                    <li>规则渐变，缺乏层次感</li>
                    <li>彩虹色系，不够专业</li>
                    <li>容易产生视觉疲劳</li>
                </ul>
            </div>
        </div>
        
        <div class="profile-demo">
            <div class="status-bar"></div>
            
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
            
            <div class="profile-content">
                <img class="avatar" src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="用户头像">
                <div class="user-info">
                    <div class="user-name">闲伴用户</div>
                    <div class="user-id">ID: 1001</div>
                    <div class="user-desc">热爱生活，享受每一个美好时刻 ✨</div>
                </div>
            </div>
            
            <div class="stats-demo">
                <div class="stat-item">
                    <span class="stat-number">128</span>
                    <span class="stat-label">关注</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">256</span>
                    <span class="stat-label">粉丝</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">1024</span>
                    <span class="stat-label">获赞</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">32</span>
                    <span class="stat-label">作品</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
