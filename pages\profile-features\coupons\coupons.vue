<template>
	<view class="coupons-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 简洁导航栏 -->
		<view class="nav-bar">
			<view class="nav-left" @tap="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
			</view>
			<text class="nav-title">优惠券</text>
			<view class="nav-right"></view>
		</view>

		<!-- 券包统计 -->
		<view class="coupon-stats">
			<view class="stats-container">
				<view class="stat-item">
					<text class="stat-number">{{ totalCoupons }}</text>
					<text class="stat-label">全部券</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-number">{{ availableCoupons }}</text>
					<text class="stat-label">可用券</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-number">{{ expiringSoon }}</text>
					<text class="stat-label">即将过期</text>
				</view>
			</view>
		</view>

		<!-- 券类型筛选 -->
		<view class="coupon-tabs">
			<scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false">
				<view class="tab-item" 
					v-for="(tab, index) in couponTabs" 
					:key="index"
					:class="{ active: currentTab === index }"
					@tap="switchTab(index)">
					<text class="tab-text">{{ tab.name }}</text>
					<view class="tab-count" v-if="tab.count > 0">{{ tab.count }}</view>
				</view>
			</scroll-view>
		</view>

		<!-- 券列表 -->
		<scroll-view class="coupons-content" scroll-y="true" @scrolltolower="loadMore">
			<!-- 空状态 -->
			<view class="empty-state" v-if="currentCoupons.length === 0">
				<view class="empty-icon">
					<uni-icons type="gift" size="80" color="#ddd"></uni-icons>
				</view>
				<text class="empty-title">暂无优惠券</text>
				<text class="empty-desc">{{ getEmptyDesc() }}</text>
				<view class="empty-action" @tap="getCoupons">
					<text class="action-text">去领券</text>
				</view>
			</view>

			<!-- 券列表 -->
			<view class="coupon-list" v-else>
				<view class="coupon-item" 
					v-for="coupon in currentCoupons" 
					:key="coupon.id"
					:class="{ expired: coupon.status === 'expired', used: coupon.status === 'used' }"
					@tap="useCoupon(coupon)">
					
					<view class="coupon-content">
						<view class="coupon-amount">
							<text class="amount-symbol">¥</text>
							<text class="amount-value">{{ coupon.amount }}</text>
							<text class="coupon-condition">{{ coupon.condition }}</text>
						</view>

						<view class="coupon-info">
							<text class="coupon-title">{{ coupon.title }}</text>
							<text class="coupon-desc">{{ coupon.description }}</text>
							<text class="validity-text">有效期：{{ coupon.validUntil }}</text>
						</view>

						<view class="coupon-action">
							<view class="action-btn" v-if="coupon.status === 'available'">
								<text class="btn-text">立即使用</text>
							</view>
							<view class="status-tag" v-else>
								<text class="status-text">{{ getStatusText(coupon.status) }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore">
				<text class="load-text">加载更多...</text>
			</view>
		</scroll-view>

		<!-- 领券中心入口 -->
		<view class="coupon-center-entry" @tap="goToCouponCenter">
			<view class="entry-content">
				<view class="entry-icon">
					<uni-icons type="gift-filled" size="24" color="#fff"></uni-icons>
				</view>
				<text class="entry-text">领券中心</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				currentTab: 0,
				hasMore: true,
				
				// 券类型标签
				couponTabs: [
					{ name: '全部', count: 0 },
					{ name: '可用', count: 5 },
					{ name: '已用', count: 3 },
					{ name: '过期', count: 2 }
				],

				// 所有券数据
				allCoupons: [
					{
						id: '001',
						type: 'discount',
						amount: '20',
						condition: '满100可用',
						title: '通用优惠券',
						description: '全场商品可用',
						validUntil: '2024-12-31',
						status: 'available',
						isExpiringSoon: false
					},
					{
						id: '002',
						type: 'cashback',
						amount: '50',
						condition: '满200可用',
						title: '新人专享券',
						description: '仅限新用户使用',
						validUntil: '2024-11-30',
						status: 'available',
						isExpiringSoon: true
					},
					{
						id: '003',
						type: 'special',
						amount: '100',
						condition: '满500可用',
						title: '生日特惠券',
						description: '生日月专享优惠',
						validUntil: '2024-10-15',
						status: 'used',
						isExpiringSoon: false
					}
				]
			}
		},
		
		computed: {
			totalCoupons() {
				return this.allCoupons.length;
			},
			
			availableCoupons() {
				return this.allCoupons.filter(c => c.status === 'available').length;
			},
			
			expiringSoon() {
				return this.allCoupons.filter(c => c.isExpiringSoon && c.status === 'available').length;
			},
			
			currentCoupons() {
				if (this.currentTab === 0) return this.allCoupons;
				if (this.currentTab === 1) return this.allCoupons.filter(c => c.status === 'available');
				if (this.currentTab === 2) return this.allCoupons.filter(c => c.status === 'used');
				if (this.currentTab === 3) return this.allCoupons.filter(c => c.status === 'expired');
				return [];
			}
		},
		
		onLoad() {
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight;
		},
		
		methods: {
			goBack() {
				uni.navigateBack();
			},
			
			switchTab(index) {
				this.currentTab = index;
			},
			
			getEmptyDesc() {
				const descs = ['暂无优惠券', '暂无可用券', '暂无已用券', '暂无过期券'];
				return descs[this.currentTab] || '暂无优惠券';
			},
			
			getStatusText(status) {
				const statusMap = {
					'used': '已使用',
					'expired': '已过期'
				};
				return statusMap[status] || '未知状态';
			},
			
			useCoupon(coupon) {
				if (coupon.status === 'available') {
					uni.showToast({
						title: '使用优惠券',
						icon: 'success'
					});
				}
			},
			
			getCoupons() {
				uni.showToast({
					title: '跳转到领券中心',
					icon: 'none'
				});
			},
			
			goToCouponCenter() {
				uni.showToast({
					title: '跳转到领券中心',
					icon: 'none'
				});
			},
			
			loadMore() {
				// 加载更多逻辑
			}
		}
	}
</script>

<style lang="scss" scoped>
.coupons-page {
	background: #f8f9fa;
	min-height: 100vh;
}

.status-bar {
	background: transparent;
}

/* 简洁导航栏 */
.nav-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 40rpx;
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

/* 简洁统计区域 */
.coupon-stats {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

	.stats-container {
		display: flex;
		align-items: center;
		padding: 32rpx 24rpx;

		.stat-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 8rpx;

			.stat-number {
				font-size: 36rpx;
				color: #333;
				font-weight: 600;
			}

			.stat-label {
				font-size: 24rpx;
				color: #666;
			}
		}

		.stat-divider {
			width: 1rpx;
			height: 40rpx;
			background: #f0f0f0;
		}
	}
}

/* 券类型筛选 */
.coupon-tabs {
	background: #fff;
	margin: 0 20rpx 20rpx;
	border-radius: 16rpx;
	padding: 8rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

	.tabs-scroll {
		white-space: nowrap;

		.tab-item {
			display: inline-block;
			position: relative;
			padding: 16rpx 24rpx;
			margin-right: 8rpx;
			border-radius: 12rpx;
			transition: all 0.3s ease;

			&.active {
				background: #ff4757;

				.tab-text {
					color: #fff;
					font-weight: 600;
				}

				.tab-count {
					background: rgba(255, 255, 255, 0.3);
					color: #fff;
				}
			}

			.tab-text {
				font-size: 26rpx;
				color: #666;
				transition: all 0.3s ease;
			}

			.tab-count {
				position: absolute;
				top: 4rpx;
				right: 4rpx;
				background: #f0f0f0;
				color: #999;
				font-size: 18rpx;
				padding: 2rpx 6rpx;
				border-radius: 8rpx;
				min-width: 20rpx;
				text-align: center;
				line-height: 1;
			}
		}
	}
}

/* 券内容区域 */
.coupons-content {
	flex: 1;
	padding: 0 20rpx 120rpx;

	/* 空状态 */
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 120rpx 40rpx;
		text-align: center;

		.empty-icon {
			margin-bottom: 32rpx;
			opacity: 0.5;
		}

		.empty-title {
			font-size: 32rpx;
			color: #333;
			font-weight: 600;
			margin-bottom: 16rpx;
		}

		.empty-desc {
			font-size: 26rpx;
			color: #666;
			margin-bottom: 48rpx;
			line-height: 1.5;
		}

		.empty-action {
			background: #ff4757;
			padding: 20rpx 48rpx;
			border-radius: 24rpx;

			.action-text {
				font-size: 28rpx;
				color: #fff;
				font-weight: 600;
			}
		}
	}

	/* 简洁券列表 */
	.coupon-list {
		.coupon-item {
			background: #fff;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			padding: 24rpx;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
			border: 1rpx solid #f0f0f0;
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.98);
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
			}

			&.expired, &.used {
				opacity: 0.5;
				background: #f8f9fa;
			}

			.coupon-content {
				display: flex;
				align-items: center;
				gap: 24rpx;
			}

			.coupon-amount {
				display: flex;
				flex-direction: column;
				align-items: center;
				min-width: 120rpx;

				.amount-symbol {
					font-size: 24rpx;
					color: #ff4757;
					font-weight: 600;
				}

				.amount-value {
					font-size: 48rpx;
					color: #ff4757;
					font-weight: 700;
					line-height: 1;
				}

				.coupon-condition {
					font-size: 20rpx;
					color: #999;
					margin-top: 8rpx;
				}
			}

			.coupon-info {
				flex: 1;

				.coupon-title {
					font-size: 30rpx;
					color: #333;
					font-weight: 600;
					display: block;
					margin-bottom: 8rpx;
				}

				.coupon-desc {
					font-size: 24rpx;
					color: #666;
					display: block;
					margin-bottom: 12rpx;
				}

				.validity-text {
					font-size: 22rpx;
					color: #999;
				}
			}

			.coupon-action {
				.action-btn {
					padding: 16rpx 24rpx;
					background: #ff4757;
					color: #fff;
					border-radius: 20rpx;
					font-size: 24rpx;
					font-weight: 500;
				}

				.status-tag {
					padding: 8rpx 16rpx;
					background: #f0f0f0;
					color: #999;
					border-radius: 12rpx;
					font-size: 22rpx;
				}
			}
		}
	}

	/* 加载更多 */
	.load-more {
		text-align: center;
		padding: 32rpx;

		.load-text {
			font-size: 26rpx;
			color: #999;
		}
	}
}

/* 简洁领券入口 */
.coupon-center-entry {
	position: fixed;
	bottom: 120rpx;
	right: 40rpx;
	z-index: 100;

	.entry-content {
		background: #ff4757;
		padding: 16rpx 24rpx;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		gap: 8rpx;
		box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.3);

		.entry-text {
			font-size: 26rpx;
			color: #fff;
			font-weight: 500;
		}
	}
}
</style>
