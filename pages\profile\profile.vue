<template>
	<view class="profile-container">
		<!-- 个人信息背景区域 -->
		<view class="profile-header">
			<!-- IP和地区显示 -->
			<view class="location-info">
				<view class="location-row">
					<text class="location-text">
						<text v-if="userInfo.region">所在地：{{ userInfo.region }} | </text>IP：{{ currentIPLocation || userInfo.ipLocation || '获取中...' }}
					</text>
				</view>
			</view>

			<!-- 更多功能按钮 - 只在已登录时显示 -->
			<view v-if="isLoggedIn" class="more-btn-header" @click="showMoreMenu">
				<image src="/static/icons/more-filled.png" class="icon-22" mode="aspectFit"></image>
			</view>

			<!-- 个人信息内容 -->
			<view class="profile-content">
				<!-- 已登录状态 -->
				<view v-if="isLoggedIn" class="logged-in-content">
					<view class="avatar-section">
						<image
							class="user-avatar-large"
							:src="currentAvatarSrc"
							mode="aspectFill"
							@error="onAvatarError"
							@load="onAvatarLoad"
							show-menu-by-longpress
						></image>
					</view>
					<view class="user-info-section">
						<view class="user-name-large">{{ userInfo.nickname || userInfo.username || '用户' }}</view>
						<view class="user-id-container">
							<text class="user-id-text">趣嗒ID：{{ userInfo.planet_id || '未设置' }}</text>
							<view class="copy-id-btn" @click="copyUserId">
								<image src="/static/icons/paperclip.png" class="icon-12" mode="aspectFit"></image>
							</view>
						</view>
						<view class="user-badges">
							<view class="badge-item" @click="goToVerification" :class="{ 'badge-inactive': !userInfo.is_verified }">
								<image src="/static/icons/person-filled.png" class="icon-16" mode="aspectFit"></image>
								<text class="badge-text">{{ userInfo.is_verified ? '已实名' : '未实名' }}</text>
							</view>
							<view class="badge-item" @click="goToVipCenter" :class="{ 'badge-inactive': !userInfo.is_vip }">
								<image src="/static/icons/vip-filled.png" class="icon-16" mode="aspectFit"></image>
								<text class="badge-text">{{ userInfo.is_vip ? '会员' : '开通会员' }}</text>
							</view>
							<view class="badge-item" @click="goToLevel">
								<image src="/static/icons/medal-filled.png" class="icon-16" mode="aspectFit"></image>
								<text class="badge-text">Lv.{{ userInfo.level || 1 }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 未登录状态 -->
				<view v-else class="not-logged-in-content" @click="goToAuth">
					<view class="avatar-section">
						<image class="user-avatar-large" src="/static/default-avatar.jpg" mode="aspectFill"></image>
					</view>
					<view class="user-info-section">
						<view class="user-name-large">请登录</view>
						<view class="user-subtitle">探索有趣的生活</view>
						<view class="login-hint">
							<text class="login-text">点击登录享受更多功能</text>
							<image src="/static/icons/right.png" class="login-arrow" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 数据统计区域 - 全屏显示 -->
		<view class="stats-section-fullwidth">
			<!-- 数据统计行 -->
			<view class="stats-row">
				<view class="stat-item" @click="goToFollowing">
					<text class="stat-number">{{ userStats.following }}</text>
					<text class="stat-label">关注</text>
				</view>
				<view class="stat-item" @click="goToFollowers">
					<text class="stat-number">{{ userStats.followers }}</text>
					<text class="stat-label">粉丝</text>
				</view>
				<view class="stat-item" @click="goToLikes">
					<text class="stat-number">{{ userStats.likes }}</text>
					<text class="stat-label">获赞</text>
				</view>
				<view class="stat-item" @click="goToPosts">
					<text class="stat-number">{{ userStats.posts }}</text>
					<text class="stat-label">作品</text>
				</view>
			</view>

			<!-- 个人简介行 -->
			<view class="user-bio-row">{{ userInfo.description || userInfo.bio || '这个人很懒，什么都没有留下~' }}</view>
		</view>

		<!-- 金刚功能区 -->
		<view class="diamond-section">
			<view class="diamond-row">
				<view class="diamond-item" @click="goToWallet">
					<view class="diamond-icon">
						<image src="/static/icons/wallet.png" class="icon-24" mode="aspectFit"></image>
					</view>
					<text class="diamond-text">钱包</text>
				</view>
				<view class="diamond-item" @click="goToOrders">
					<view class="diamond-icon">
						<image src="/static/icons/list.png" class="icon-24" mode="aspectFit"></image>
					</view>
					<text class="diamond-text">订单</text>
				</view>
				<view class="diamond-item" @click="goToCoupons">
					<view class="diamond-icon">
						<image src="/static/icons/gift.png" class="icon-24" mode="aspectFit"></image>
					</view>
					<text class="diamond-text">优惠券</text>
				</view>
				<view class="diamond-item" @click="goToMall">
					<view class="diamond-icon">
						<image src="/static/icons/shop.png" class="icon-24" mode="aspectFit"></image>
					</view>
					<text class="diamond-text">商城</text>
				</view>
			</view>
		</view>



		<!-- 切换菜单功能区 -->
		<view class="tab-section">
			<view class="tab-container">
				<view class="tab-item" :class="{ active: currentTab === 'works' }" @click="switchTab('works')">
					<text class="tab-text">作品</text>
				</view>
				<view class="tab-item" :class="{ active: currentTab === 'likes' }" @click="switchTab('likes')">
					<text class="tab-text">喜欢</text>
				</view>
				<view class="tab-item" :class="{ active: currentTab === 'favorites' }" @click="switchTab('favorites')">
					<text class="tab-text">收藏</text>
				</view>
				<view class="tab-item" :class="{ active: currentTab === 'activities' }" @click="switchTab('activities')">
					<text class="tab-text">活动</text>
				</view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content-section">
			<view v-if="currentTab === 'works'" class="content-container">
				<view class="empty-state">
					<image class="empty-illustration" src="/static/illustrations/empty-works.png" mode="aspectFit"></image>
					<text class="empty-text">还没有发布作品</text>
					<text class="empty-desc">分享你的精彩瞬间吧</text>
				</view>
			</view>
			<view v-if="currentTab === 'likes'" class="content-container">
				<view class="empty-state">
					<image class="empty-illustration" src="/static/illustrations/empty-works.png" mode="aspectFit"></image>
					<text class="empty-text">还没有喜欢的内容</text>
					<text class="empty-desc">去发现更多有趣的内容</text>
				</view>
			</view>
			<view v-if="currentTab === 'favorites'" class="content-container">
				<view class="empty-state">
					<image class="empty-illustration" src="/static/illustrations/empty-works.png" mode="aspectFit"></image>
					<text class="empty-text">还没有收藏内容</text>
					<text class="empty-desc">收藏喜欢的内容随时查看</text>
				</view>
			</view>
			<view v-if="currentTab === 'activities'" class="content-container">
				<view class="empty-state">
					<image class="empty-illustration" src="/static/illustrations/empty-works.png" mode="aspectFit"></image>
					<text class="empty-text">还没有参与活动</text>
					<text class="empty-desc">快去参加有趣的活动吧</text>
				</view>
			</view>
		</view>


	</view>

	<!-- 右侧半页更多功能弹窗 -->
	<view class="side-menu-overlay" v-if="showMoreMenuFlag" @click="hideMoreMenu">
		<view class="side-menu-panel" @click.stop :class="{ 'show': showMoreMenuFlag }">
			<!-- 顶部拖拽指示器 -->
			<view class="drag-indicator"></view>

			<!-- 头部区域 -->
			<view class="side-menu-header">
				<text class="menu-title">更多功能</text>
				<view class="close-btn" @click="hideMoreMenu">
					<image src="/static/icons/closeempty.png" class="icon-20" mode="aspectFit"></image>
				</view>
			</view>

			<!-- 功能列表 - 添加滚动容器 -->
			<scroll-view class="side-menu-scroll" scroll-y="true">
				<view class="side-menu-content">
				<view class="menu-item" @click="goToMyHistory">
					<view class="item-icon">
						<image src="/static/icons/location.png" class="icon-20" mode="aspectFit"></image>
					</view>
					<text class="item-title">我的足迹</text>
					<image src="/static/icons/right.png" class="icon-16" mode="aspectFit"></image>
				</view>

				<view class="menu-item" @click="goToCreatorCenter">
					<view class="item-icon">
						<image src="/static/icons/star.png" class="icon-20" mode="aspectFit"></image>
					</view>
					<text class="item-title">创作者中心</text>
					<image src="/static/icons/right.png" class="icon-16" mode="aspectFit"></image>
				</view>

				<view class="menu-item" @click="goToMyEvents">
					<view class="item-icon">
						<image src="/static/icons/calendar.png" class="icon-20" mode="aspectFit"></image>
					</view>
					<text class="item-title">我的活动</text>
					<image src="/static/icons/right.png" class="icon-16" mode="aspectFit"></image>
				</view>

				<view class="menu-item" @click="goToGuild">
					<view class="item-icon">
						<image src="/static/icons/person-filledd.png" class="icon-20" mode="aspectFit"></image>
					</view>
					<text class="item-title">我的公会</text>
					<image src="/static/icons/right.png" class="icon-16" mode="aspectFit"></image>
				</view>

				<view class="menu-item" @click="goToBecomeCompanion">
					<view class="item-icon">
						<image src="/static/icons/heart-filled.png" class="icon-20" mode="aspectFit"></image>
					</view>
					<text class="item-title">成为玩伴</text>
					<image src="/static/icons/right.png" class="icon-16" mode="aspectFit"></image>
				</view>

				<view class="menu-item" @click="goToRulesCenter">
					<view class="item-icon">
						<image src="/static/icons/listr.png" class="icon-20" mode="aspectFit"></image>
					</view>
					<text class="item-title">规则中心</text>
					<image src="/static/icons/right.png" class="icon-16" mode="aspectFit"></image>
				</view>

				<view class="menu-item" @click="goToOnlineService">
					<view class="item-icon">
						<image src="/static/icons/chatbubble.png" class="icon-20" mode="aspectFit"></image>
					</view>
					<text class="item-title">在线客服</text>
					<image src="/static/icons/right.png" class="icon-16" mode="aspectFit"></image>
				</view>

				<view class="menu-item" @click="goToSettings">
					<view class="item-icon">
						<image src="/static/icons/gear.png" class="icon-20" mode="aspectFit"></image>
					</view>
					<text class="item-title">设置</text>
					<image src="/static/icons/right.png" class="icon-16" mode="aspectFit"></image>
				</view>

				<view class="menu-item" @click="goToHelp">
					<view class="item-icon">
						<image src="/static/icons/help.png" class="icon-20" mode="aspectFit"></image>
					</view>
					<text class="item-title">帮助与反馈</text>
					<image src="/static/icons/right.png" class="icon-16" mode="aspectFit"></image>
				</view>

				<view class="menu-item" @click="goToAbout">
					<view class="item-icon">
						<image src="/static/icons/info.png" class="icon-20" mode="aspectFit"></image>
					</view>
					<text class="item-title">关于闲伴</text>
					<image src="/static/icons/right.png" class="icon-16" mode="aspectFit"></image>
				</view>

				<view class="menu-item" @click="scanCode">
					<view class="item-icon">
						<image src="/static/icons/scan.png" class="icon-20" mode="aspectFit"></image>
					</view>
					<text class="item-title">扫一扫</text>
					<image src="/static/icons/right.png" class="icon-16" mode="aspectFit"></image>
				</view>
				</view>

				<!-- 退出登录 -->
				<view class="logout-section">
					<view class="logout-btn-wrapper" @click.stop="handleLogoutClick">
						<button class="logout-btn">退出登录</button>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 年轻化退出登录弹窗 -->
		<view v-if="showLogoutModal" class="logout-modal-overlay" @click="cancelLogout">
			<view class="logout-modal-content" @click.stop>
				<!-- 大表情 -->
				<view class="logout-emoji">😢</view>

				<!-- 标题 -->
				<view class="logout-title">您确定要离开趣嗒嘛~</view>

				<!-- 副标题 -->
				<view class="logout-subtitle">离开后将无法收到最新的趣事推送哦</view>

				<!-- 按钮组 -->
				<view class="logout-buttons">
					<view class="logout-btn-custom cancel-btn" @click="cancelLogout">
						<text class="btn-text">再想想</text>
					</view>
					<view class="logout-btn-custom confirm-btn" @click="confirmLogout">
						<text class="btn-text">确定离开</text>
					</view>
				</view>
			</view>
		</view>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				showMoreMenuFlag: false,
				currentTab: 'works', // 当前选中的标签
				isLoggedIn: false,
				currentIPLocation: '', // 当前实时IP属地

				currentAvatarSrc: '/static/default-avatar.jpg', // 当前头像路径

				// 用户信息
				userInfo: {},

				// 用户统计
				userStats: {
					following: 0,
					followers: 0,
					likes: 0,
					posts: 0
				},

				// 钱包信息
				userWallet: {
					balance: 0
				},

				// 退出登录弹窗状态
				showLogoutModal: false
			}
		},

		onLoad() {
			// 获取系统信息
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});

			// 检查登录状态
			this.checkLoginStatus();

			// 自动获取当前IP属地
			this.autoGetCurrentIPLocation();
		},

		onShow() {
			console.log('🔍 我的页面显示 - onShow触发')

			// 立即重新初始化所有数据
			this.initializeUserData();

			// 检查是否需要刷新用户信息
			const needRefresh = uni.getStorageSync('needRefreshProfile')
			if (needRefresh) {
				console.log('检测到需要刷新用户信息')
				// 清除标记
				uni.removeStorageSync('needRefreshProfile')
				// 强制重新加载用户信息
				this.forceRefreshUserInfo()
			}
		},

		methods: {
			// 初始化用户数据
			initializeUserData() {
				console.log('🔄 初始化用户数据');

				// 获取存储的用户信息
				const userInfo = uni.getStorageSync('userInfo');
				const token = uni.getStorageSync('token');
				const isLoggedIn = uni.getStorageSync('isLoggedIn');

				console.log('🔄 存储的用户信息:', userInfo);
				console.log('🔄 存储的token:', token);
				console.log('🔄 存储的登录状态:', isLoggedIn);

				// 详细检查用户信息的关键字段
				if (userInfo) {
					console.log('🔍 用户信息详细检查:');
					console.log('  - nickname:', userInfo.nickname);
					console.log('  - username:', userInfo.username);
					console.log('  - planet_id:', userInfo.planet_id);
					console.log('  - avatar:', userInfo.avatar);
					console.log('  - _id:', userInfo._id);
				}

				if (userInfo && token && isLoggedIn) {
					// 设置登录状态
					this.isLoggedIn = true;
					this.userInfo = { ...userInfo };

					console.log('✅ 设置登录状态为true，用户信息已复制');
					console.log('📋 this.userInfo:', this.userInfo);

					// 设置头像 - 确保路径正确
					console.log('🔍 头像设置检查:');
					console.log('  - userInfo.avatar:', userInfo.avatar);
					console.log('  - avatar存在:', !!userInfo.avatar);
					console.log('  - avatar不为空:', userInfo.avatar !== '');
					console.log('  - 不包含default-avatar:', !userInfo.avatar?.includes('default-avatar'));

					if (userInfo.avatar && userInfo.avatar !== '' && !userInfo.avatar.includes('default-avatar')) {
						this.currentAvatarSrc = userInfo.avatar;
						console.log('🖼️ 使用用户头像:', this.currentAvatarSrc);
					} else {
						// 优先使用 .jpg 格式的默认头像（因为 .png 文件损坏）
						this.currentAvatarSrc = '/static/default-avatar.jpg';
						console.log('🖼️ 使用默认头像:', this.currentAvatarSrc);

						// 验证默认头像文件是否存在
						console.log('🔍 验证默认头像路径:', this.currentAvatarSrc);
					}

					// 加载用户统计
					this.loadUserStats();

					console.log('🔄 用户数据初始化完成');
				} else {
					// 未登录状态
					this.isLoggedIn = false;
					this.userInfo = {};
					this.currentAvatarSrc = '/static/default-avatar.jpg';
					console.log('🔄 未登录状态，使用默认头像');
				}

				// 强制更新视图
				this.$forceUpdate();
			},

			// 检查登录状态
			checkLoginStatus() {
				const userInfo = uni.getStorageSync('userInfo');
				const token = uni.getStorageSync('token');
				const isLoggedIn = uni.getStorageSync('isLoggedIn');

				console.log('检查登录状态 - userInfo:', userInfo);
				console.log('检查登录状态 - token:', token);
				console.log('检查登录状态 - isLoggedIn:', isLoggedIn);

				if (userInfo && token) {
					console.log('登录状态检查通过，设置为已登录');
					this.isLoggedIn = true;
					this.userInfo = { ...userInfo }; // 创建新对象确保响应式更新

					// 设置头像 - 确保路径正确
					if (userInfo.avatar && userInfo.avatar !== '' && !userInfo.avatar.includes('default-avatar')) {
						this.currentAvatarSrc = userInfo.avatar;
						console.log('🖼️ checkLoginStatus使用用户头像:', this.currentAvatarSrc);
					} else {
						this.currentAvatarSrc = '/static/default-avatar.jpg';
						console.log('🖼️ checkLoginStatus使用默认头像:', this.currentAvatarSrc);
					}

					this.loadUserStats();

					// 强制更新组件确保头像显示
					this.$nextTick(() => {
						this.$forceUpdate();
					});
				} else {
					console.log('登录状态检查失败，设置为未登录');
					this.isLoggedIn = false;
					this.userInfo = {};
					this.currentAvatarSrc = '/static/default-avatar.jpg';
				}
			},





			// 强制刷新用户信息
			forceRefreshUserInfo() {
				console.log('🔄 强制刷新用户信息')
				const userInfo = uni.getStorageSync('userInfo');
				console.log('🔄 获取到的用户信息:', userInfo);

				if (userInfo) {
					// 详细检查用户信息的关键字段
					console.log('🔍 强制刷新 - 用户信息详细检查:');
					console.log('  - nickname:', userInfo.nickname);
					console.log('  - username:', userInfo.username);
					console.log('  - planet_id:', userInfo.planet_id);
					console.log('  - avatar:', userInfo.avatar);
					console.log('  - _id:', userInfo._id);

					// 设置登录状态
					this.isLoggedIn = true;
					this.userInfo = { ...userInfo }; // 创建新对象触发响应式更新
					console.log('✅ 用户信息已更新:', this.userInfo)
					console.log('📋 头像路径:', this.userInfo.avatar)

					// 设置头像 - 确保路径正确
					if (userInfo.avatar && userInfo.avatar !== '' && !userInfo.avatar.includes('default-avatar')) {
						this.currentAvatarSrc = userInfo.avatar;
						console.log('🖼️ forceRefresh使用用户头像:', this.currentAvatarSrc);
					} else {
						this.currentAvatarSrc = '/static/default-avatar.jpg';
						console.log('🖼️ forceRefresh使用默认头像:', this.currentAvatarSrc);
					}

					// 强制触发页面重新渲染
					this.$forceUpdate()
				} else {
					console.log('❌ 强制刷新失败：没有找到用户信息');
				}
			},

			// 自动获取当前IP属地（无感知）
			async autoGetCurrentIPLocation() {
				console.log('开始自动获取IP属地...')
				try {
					// 检查缓存，如果缓存未过期则使用缓存
					const cachedLocation = this.getCachedIPLocation()
					if (cachedLocation) {
						this.currentIPLocation = cachedLocation.fullLocation
						console.log('使用缓存的IP属地:', this.currentIPLocation)
						return
					}

					console.log('缓存无效，开始获取新的IP信息...')

					// 使用多种方案获取IP属地
					const locationData = await this.getIPLocationWithMultipleMethods()
					if (locationData && locationData.fullLocation) {
						this.currentIPLocation = locationData.fullLocation

						// 缓存结果（30分钟）
						this.cacheIPLocation(locationData)

						console.log('获取到实时IP属地:', this.currentIPLocation)

						// 更新用户信息中的IP属地
						if (this.userInfo.id) {
							this.userInfo.ipLocation = this.currentIPLocation
							this.saveUserInfo()
						}
					} else {
						console.log('所有IP获取方法都失败，使用默认值')
						this.currentIPLocation = '上海'
					}
				} catch (error) {
					console.log('自动获取IP属地失败:', error.message)
					this.currentIPLocation = '上海'
				}
			},

			// 使用多种方法获取IP地区信息
			async getIPLocationWithMultipleMethods() {
				const methods = [
					() => this.getLocationByIPSilent(),
					() => this.getLocationByBaiduAPI(),
					() => this.getLocationByTencentAPI(),
					() => this.getLocationBySimpleAPI()
				]

				for (let i = 0; i < methods.length; i++) {
					try {
						console.log(`尝试方法 ${i + 1}...`)
						const result = await methods[i]()
						if (result && result.fullLocation) {
							console.log(`方法 ${i + 1} 成功:`, result.fullLocation)
							return result
						}
					} catch (error) {
						console.log(`方法 ${i + 1} 失败:`, error.message)
						continue
					}
				}

				return null
			},

			// 方法2：使用百度API
			async getLocationByBaiduAPI() {
				return new Promise((resolve, reject) => {
					uni.request({
						url: 'https://api.map.baidu.com/location/ip',
						data: {
							ak: 'your_baidu_ak', // 需要申请百度地图API密钥
							coor: 'bd09ll'
						},
						method: 'GET',
						timeout: 3000,
						success: (res) => {
							if (res.data && res.data.status === 0) {
								const location = {
									fullLocation: this.formatChineseLocationSilent(res.data.content.address_detail.province, res.data.content.address_detail.city),
									timestamp: Date.now()
								}
								resolve(location)
							} else {
								reject(new Error('百度API失败'))
							}
						},
						fail: () => reject(new Error('百度API请求失败'))
					})
				})
			},

			// 方法3：使用腾讯API
			async getLocationByTencentAPI() {
				return new Promise((resolve, reject) => {
					uni.request({
						url: 'https://apis.map.qq.com/ws/location/v1/ip',
						data: {
							key: 'your_tencent_key' // 需要申请腾讯地图API密钥
						},
						method: 'GET',
						timeout: 3000,
						success: (res) => {
							if (res.data && res.data.status === 0) {
								const location = {
									fullLocation: this.formatChineseLocationSilent(res.data.result.ad_info.province, res.data.result.ad_info.city),
									timestamp: Date.now()
								}
								resolve(location)
							} else {
								reject(new Error('腾讯API失败'))
							}
						},
						fail: () => reject(new Error('腾讯API请求失败'))
					})
				})
			},

			// 方法4：使用简单的IP查询API
			async getLocationBySimpleAPI() {
				return new Promise((resolve, reject) => {
					uni.request({
						url: 'https://whois.pconline.com.cn/ipJson.jsp',
						data: {
							json: true
						},
						method: 'GET',
						timeout: 3000,
						success: (res) => {
							console.log('简单API响应:', res.data)
							if (res.data && res.data.pro) {
								const location = {
									fullLocation: this.formatChineseLocationSilent(res.data.pro, res.data.city),
									timestamp: Date.now()
								}
								resolve(location)
							} else {
								reject(new Error('简单API失败'))
							}
						},
						fail: () => reject(new Error('简单API请求失败'))
					})
				})
			},

			// 静默获取IP地区信息
			async getCurrentIPLocationSilent() {
				try {
					const locationData = await this.getLocationByIPSilent()
					return locationData
				} catch (error) {
					console.log('静默获取IP地区失败:', error.message)
					return null
				}
			},

			// 静默IP地区查询（不显示错误）
			async getLocationByIPSilent() {
				try {
					// 获取IP
					const ip = await this.getUserIPSilent()
					if (!ip) return null

					// 获取地区信息
					const location = await this.getLocationFromAPISilent(ip)
					return location
				} catch (error) {
					return null
				}
			},

			// 静默获取用户IP
			async getUserIPSilent() {
				const apis = [
					'https://api.ipify.org?format=json',
					'https://ipapi.co/json/',
					'https://httpbin.org/ip'
				]

				for (const apiUrl of apis) {
					try {
						const ip = await this.tryGetIPFromAPISilent(apiUrl)
						if (ip) return ip
					} catch (error) {
						continue
					}
				}
				return null
			},

			// 静默尝试从API获取IP
			async tryGetIPFromAPISilent(url) {
				return new Promise((resolve, reject) => {
					uni.request({
						url: url,
						method: 'GET',
						timeout: 2000, // 更短的超时时间
						success: (res) => {
							if (res.data) {
								let ip = null
								if (res.data.ip) {
									ip = res.data.ip
								} else if (res.data.query) {
									ip = res.data.query
								} else if (typeof res.data === 'string') {
									ip = res.data.trim()
								}

								if (ip && this.isValidIP(ip)) {
									resolve(ip)
								} else {
									reject(new Error('无效IP'))
								}
							} else {
								reject(new Error('无数据'))
							}
						},
						fail: () => reject(new Error('请求失败'))
					})
				})
			},

			// 验证IP格式
			isValidIP(ip) {
				const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
				return ipv4Regex.test(ip)
			},

			// 静默获取地区信息
			async getLocationFromAPISilent(ip) {
				const url = `https://ip-api.com/json/${ip}?fields=status,country,regionName,city&lang=zh-CN`

				return new Promise((resolve, reject) => {
					uni.request({
						url: url,
						method: 'GET',
						timeout: 3000,
						success: (res) => {
							if (res.data && res.data.status === 'success') {
								const location = this.formatLocationDataSilent(res.data)
								resolve(location)
							} else {
								reject(new Error('API失败'))
							}
						},
						fail: () => reject(new Error('请求失败'))
					})
				})
			},

			// 静默格式化地区数据
			formatLocationDataSilent(data) {
				const location = {
					ip: data.query || '',
					country: data.country || '中国',
					province: data.regionName || '',
					city: data.city || '',
					fullLocation: '',
					timestamp: Date.now()
				}

				if (location.country === '中国' || location.country === 'China') {
					location.fullLocation = this.formatChineseLocationSilent(location.province, location.city)
				} else {
					location.fullLocation = location.country
				}

				return location.fullLocation ? location : null
			},

			// 静默格式化中国地区
			formatChineseLocationSilent(province, city) {
				if (!province && !city) return '上海'

				const municipalities = ['北京', '上海', '天津', '重庆']
				let targetLocation = province || city || ''

				targetLocation = targetLocation
					.replace(/市$/, '')
					.replace(/省$/, '')
					.replace(/自治区$/, '')
					.trim()

				const isDirectMunicipality = municipalities.some(m =>
					targetLocation.includes(m) || (city && city.includes(m))
				)

				if (isDirectMunicipality) {
					const municipality = municipalities.find(m =>
						targetLocation.includes(m) || (city && city.includes(m))
					)
					return municipality || targetLocation
				} else {
					return targetLocation || '上海'
				}
			},

			// 缓存IP地区信息（30分钟）
			cacheIPLocation(locationData) {
				try {
					const cacheData = {
						...locationData,
						timestamp: Date.now(),
						expireTime: Date.now() + (30 * 60 * 1000) // 30分钟过期
					}
					uni.setStorageSync('currentIPLocationCache', cacheData)
				} catch (error) {
					console.log('缓存IP地区信息失败:', error)
				}
			},

			// 获取缓存的IP地区信息
			getCachedIPLocation() {
				try {
					const cacheData = uni.getStorageSync('currentIPLocationCache')
					if (cacheData && cacheData.expireTime > Date.now()) {
						return cacheData
					}
					return null
				} catch (error) {
					return null
				}
			},

			// 加载用户统计数据
			loadUserStats() {
				// 这里可以调用云函数获取最新的用户统计数据
				this.userStats = {
					following: this.userInfo.following_count || 0,
					followers: this.userInfo.followers_count || 0,
					likes: this.userInfo.likes_count || 0,
					posts: this.userInfo.posts_count || 0
				};
			},

			// 跳转到登录页面
			goToAuth() {
				uni.navigateTo({
					url: '/pages/auth/auth'
				});
			},





			// 获取当前IP地区信息（复用auth页面的方法）
			async getCurrentIPLocation() {
				try {
					const locationData = await this.getLocationByIP()
					return locationData
				} catch (error) {
					console.error('获取IP地区失败:', error)
					return {
						ip: '',
						country: '中国',
						province: '上海市',
						city: '上海市',
						fullLocation: '上海',
						accuracy: 'low'
					}
				}
			},

			// IP地区查询
			async getLocationByIP(ip = null) {
				return new Promise(async (resolve, reject) => {
					try {
						let targetIP = ip

						if (!targetIP) {
							try {
								targetIP = await this.getUserIP()
							} catch (error) {
								resolve(this.getDefaultLocation())
								return
							}
						}

						this.getLocationFromAPI(targetIP)
							.then(resolve)
							.catch(() => {
								resolve(this.getDefaultLocation())
							})

					} catch (error) {
						resolve(this.getDefaultLocation())
					}
				})
			},

			// 获取用户IP
			async getUserIP() {
				return new Promise((resolve, reject) => {
					uni.request({
						url: 'https://ip-api.com/json/',
						method: 'GET',
						timeout: 5000,
						success: (res) => {
							if (res.data && res.data.status === 'success') {
								resolve(res.data.query)
							} else {
								reject(new Error('获取IP失败'))
							}
						},
						fail: reject
					})
				})
			},

			// 从API获取地区信息
			async getLocationFromAPI(ip) {
				return new Promise((resolve, reject) => {
					const url = `https://ip-api.com/json/${ip}?fields=status,country,regionName,city,district,isp,query&lang=zh-CN`

					uni.request({
						url: url,
						method: 'GET',
						timeout: 8000,
						success: (res) => {
							if (res.data && res.data.status === 'success') {
								const location = this.formatLocationData(res.data)
								resolve(location)
							} else {
								reject(new Error('API返回失败'))
							}
						},
						fail: reject
					})
				})
			},

			// 格式化地区数据
			formatLocationData(data) {
				let location = {
					ip: data.query || '',
					country: data.country || '中国',
					province: data.regionName || '',
					city: data.city || '',
					district: data.district || '',
					isp: data.isp || '',
					fullLocation: '',
					accuracy: 'high'
				}

				// 构建地址 - 只显示省级或直辖市
				if (location.country === '中国' || location.country === 'China') {
					location.fullLocation = this.formatChineseLocation(location.province, location.city)
				} else {
					location.fullLocation = `${location.country}`
				}

				if (!location.fullLocation.trim()) {
					location = this.getDefaultLocation()
					location.ip = data.query || ''
				}

				return location
			},

			// 格式化中国地区显示（只显示省级或直辖市）
			formatChineseLocation(province, city) {
				if (!province && !city) return '上海'

				// 直辖市列表
				const municipalities = ['北京', '上海', '天津', '重庆']

				// 特别行政区
				const specialRegions = ['香港', '澳门']

				// 处理省份信息
				let targetLocation = province || city || ''

				// 去除常见后缀
				targetLocation = targetLocation
					.replace(/市$/, '')
					.replace(/省$/, '')
					.replace(/自治区$/, '')
					.replace(/特别行政区$/, '')
					.replace(/维吾尔$/, '')
					.replace(/回族$/, '')
					.replace(/壮族$/, '')
					.trim()

				// 检查是否是直辖市
				const isDirectMunicipality = municipalities.some(m =>
					targetLocation.includes(m) || (city && city.includes(m))
				)

				// 检查是否是特别行政区
				const isSpecialRegion = specialRegions.some(s =>
					targetLocation.includes(s) || (city && city.includes(s))
				)

				if (isDirectMunicipality) {
					// 直辖市：返回直辖市名称
					const municipality = municipalities.find(m =>
						targetLocation.includes(m) || (city && city.includes(m))
					)
					return municipality || targetLocation
				} else if (isSpecialRegion) {
					// 特别行政区：返回特别行政区名称
					const specialRegion = specialRegions.find(s =>
						targetLocation.includes(s) || (city && city.includes(s))
					)
					return specialRegion || targetLocation
				} else {
					// 普通省份：返回省份名称
					return targetLocation || '上海'
				}
			},

			// 简化地区名称
			simplifyLocationName(location) {
				if (!location) return '未知'

				return location
					.replace(/省$/, '')
					.replace(/市$/, '')
					.replace(/自治区$/, '')
					.replace(/特别行政区$/, '')
					.replace(/维吾尔$/, '')
					.replace(/回族$/, '')
					.replace(/壮族$/, '')
					.trim()
			},

			// 获取默认地区
			getDefaultLocation() {
				return {
					ip: '',
					country: '中国',
					province: '上海',
					city: '上海',
					district: '',
					isp: '',
					fullLocation: '上海',
					accuracy: 'low'
				}
			},

			// 头像加载成功处理
			onAvatarLoad(e) {
				console.log('🖼️ 头像加载成功:', this.currentAvatarSrc);
				console.log('🖼️ 头像加载事件详情:', e);
			},

			// 头像加载失败处理
			onAvatarError(e) {
				console.log('🖼️ 头像加载失败:', this.currentAvatarSrc);
				console.log('🖼️ 头像加载失败事件详情:', e);
				console.log('🖼️ 尝试使用默认头像');

				// 尝试不同的默认头像路径
				if (this.currentAvatarSrc !== '/static/default-avatar.jpg') {
					this.currentAvatarSrc = '/static/default-avatar.jpg';
					console.log('🖼️ 切换到 default-avatar.jpg');
				} else if (this.currentAvatarSrc !== '/static/default-avatar.png') {
					this.currentAvatarSrc = '/static/default-avatar.png';
					console.log('🖼️ 切换到 default-avatar.png');
				} else {
					console.log('🖼️ 所有默认头像都加载失败');
					// 尝试使用在线占位图
					this.currentAvatarSrc = 'https://via.placeholder.com/160x160/cccccc/ffffff?text=头像';
				}
			},

			// 保存用户信息
			saveUserInfo() {
				try {
					uni.setStorageSync('userInfo', this.userInfo)

					// 更新注册用户列表
					const registeredUsers = uni.getStorageSync('registeredUsers') || []
					const userIndex = registeredUsers.findIndex(user => user.id === this.userInfo.id)
					if (userIndex !== -1) {
						registeredUsers[userIndex] = this.userInfo
						uni.setStorageSync('registeredUsers', registeredUsers)
					}
				} catch (error) {
					console.error('保存用户信息失败:', error)
				}
			},



			// 扫码功能
			scanCode() {
				uni.scanCode({
					success: (res) => {
						uni.showToast({
							title: '扫码成功',
							icon: 'success'
						});
						console.log('扫码结果:', res.result);
					},
					fail: () => {
						uni.showToast({
							title: '扫码失败',
							icon: 'none'
						});
					}
				});
			},

			// 编辑个人资料
			editProfile() {
				uni.showActionSheet({
					itemList: ['编辑个人信息', '更换头像'],
					success: (res) => {
						if (res.tapIndex === 0) {
							// 编辑个人信息
							uni.showToast({
								title: '编辑个人信息',
								icon: 'none'
							});
						} else if (res.tapIndex === 1) {
							// 更换头像
							this.changeAvatar();
						}
					}
				});
			},

			// 更换头像
			changeAvatar() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempPath = res.tempFilePaths[0];

						// 验证图片格式（不允许透明格式）
						const lowerPath = tempPath.toLowerCase();
						if (lowerPath.includes('.png') || lowerPath.includes('.gif')) {
							uni.showToast({
								title: '不支持透明格式图片，请选择JPG格式',
								icon: 'none',
								duration: 3000
							});
							return;
						}

						// 显示上传进度
						uni.showLoading({
							title: '上传中...'
						});

						// 上传到云存储
						this.uploadAvatarToCloud(tempPath);
					},
					fail: (error) => {
						console.error('选择头像失败:', error);
						uni.showToast({
							title: '选择头像失败，请重试',
							icon: 'none'
						});
					}
				});
			},

			// 上传头像到云存储
			async uploadAvatarToCloud(tempPath) {
				try {
					const result = await uniCloud.callFunction({
						name: 'upload-avatar',
						data: {
							action: 'uploadAvatar',
							filePath: tempPath,
							uid: this.userInfo.id
						}
					});

					if (result.result.code === 0) {
						const avatarUrl = result.result.data.fileID;

						// 更新数据库中的头像URL
						const updateResult = await uniCloud.callFunction({
							name: 'upload-avatar',
							data: {
								action: 'updateUserAvatar',
								uid: this.userInfo.id,
								avatarUrl: avatarUrl
							}
						});

						if (updateResult.result.code === 0) {
							// 更新本地用户信息
							this.userInfo.avatar = avatarUrl;
							this.currentAvatarSrc = avatarUrl;
							this.saveUserInfo();

							uni.hideLoading();
							uni.showToast({
								title: '头像更新成功',
								icon: 'success'
							});
						} else {
							throw new Error(updateResult.result.message || '更新失败');
						}
					} else {
						throw new Error(result.result.message || '上传失败');
					}
				} catch (error) {
					console.error('头像上传失败:', error);
					uni.hideLoading();
					uni.showToast({
						title: error.message || '头像上传失败，请重试',
						icon: 'none',
						duration: 3000
					});
				}
			},

			showMoreMenu() {
				this.showMoreMenuFlag = true;
			},

			hideMoreMenu() {
				this.showMoreMenuFlag = false;
			},

			// 切换标签
			switchTab(tab) {
				this.currentTab = tab;
			},

			goToSettings() {
				uni.navigateTo({
					url: '/pages/profile-features/settings/settings'
				});
			},

			// 复制用户ID
			copyUserId() {
				if (!this.isLoggedIn) {
					this.goToAuth();
					return;
				}

				const userId = this.userInfo.planet_id;
				uni.setClipboardData({
					data: userId,
					success: () => {
						uni.showToast({
							title: `您已复制趣嗒ID：${userId}`,
							icon: 'none',
							duration: 2000
						});
					},
					fail: () => {
						uni.showToast({
							title: '复制失败，请重试',
							icon: 'none',
							duration: 1500
						});
					}
				});
			},

			goToVipCenter() {
				uni.navigateTo({
					url: '/pages/profile-features/vip/vip'
				});
			},

			// 跳转到实名认证页面
			goToVerification() {
				console.log('点击实名认证徽章')
				uni.navigateTo({
					url: '/pages/profile-features/verification/verification',
					success: () => {
						console.log('跳转实名认证页面成功')
					},
					fail: (error) => {
						console.error('跳转实名认证页面失败:', error)
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						})
					}
				});
			},

			// 跳转到等级页面
			goToLevel() {
				console.log('点击等级徽章')
				uni.navigateTo({
					url: '/pages/profile-features/level/level',
					success: () => {
						console.log('跳转等级页面成功')
					},
					fail: (error) => {
						console.error('跳转等级页面失败:', error)
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						})
					}
				});
			},

			goToWallet() {
				uni.navigateTo({
					url: '/pages/profile-features/wallet/wallet'
				});
			},

			goToOrders() {
				uni.navigateTo({
					url: '/pages/profile-features/orders/orders'
				});
			},

			goToCoupons() {
				uni.navigateTo({
					url: '/pages/profile-features/coupons/coupons'
				});
			},

			goToMall() {
				uni.navigateTo({
					url: '/pages/profile-features/mall/mall'
				});
			},

			goToMyEvents() {
				uni.navigateTo({
					url: '/pages/profile-features/my-events/my-events'
				});
			},

			goToMyPosts() {
				uni.navigateTo({
					url: '/pages/profile-features/my-works/my-works'
				});
			},

			goToCollections() {
				uni.navigateTo({
					url: '/pages/profile-features/my-favorites/my-favorites'
				});
			},

			goToHelp() {
				uni.navigateTo({
					url: '/pages/profile-features/help/help'
				});
			},

			goToAbout() {
				uni.navigateTo({
					url: '/pages/profile-features/about/about'
				});
			},

			goToCreatorCenter() {
				this.hideMoreMenu();
				uni.navigateTo({
					url: '/pages/profile-features/creator-center/creator-center'
				});
			},

			goToFollowing() {
				uni.navigateTo({
					url: '/pages/profile-features/following/following'
				});
			},

			goToFollowers() {
				uni.navigateTo({
					url: '/pages/profile-features/followers/followers'
				});
			},

			goToLikes() {
				uni.navigateTo({
					url: '/pages/profile-features/likes/likes'
				});
			},

			goToPosts() {
				uni.navigateTo({
					url: '/pages/profile-features/works/works'
				});
			},

			goToMyFavorites() {
				console.log('跳转到我的喜欢');
			},

			goToMyHistory() {
				console.log('跳转到我的足迹');
			},

			goToMyWallet() {
				console.log('跳转到我的钱包');
				uni.navigateTo({
					url: '/pages/profile-features/wallet/wallet'
				});
			},

			goToOnlineService() {
				console.log('跳转到在线客服');
			},

			goToGuild() {
				console.log('跳转到我的公会');
			},

			goToBecomeCompanion() {
				console.log('跳转到成为玩伴');
			},

			goToRulesCenter() {
				console.log('跳转到规则中心');
			},

			// 处理退出登录点击
			handleLogoutClick() {
				console.log('🚪 处理退出登录点击');

				// 阻止事件冒泡
				event.stopPropagation();

				// 立即显示退出弹窗，同时关闭更多菜单
				this.showLogoutModal = true;
				this.showMoreMenuFlag = false;

				console.log('🚪 退出弹窗已显示，状态:', this.showLogoutModal);
				console.log('🚪 更多菜单已关闭，状态:', this.showMoreMenuFlag);
			},

			// 显示退出登录对话框
			showLogoutDialog() {
				this.handleLogoutClick();
			},

			// 确认退出登录
			confirmLogout() {
				console.log('✅ 确认退出登录');
				// 关闭弹窗
				this.showLogoutModal = false;

				// 清除所有登录相关的存储
				uni.removeStorageSync('userInfo');
				uni.removeStorageSync('token');
				uni.removeStorageSync('isLoggedIn');

				// 立即更新登录状态
				this.isLoggedIn = false;
				this.userInfo = {};
				this.currentAvatarSrc = '/static/default-avatar.jpg';

				// 强制更新视图
				this.$nextTick(() => {
					this.$forceUpdate();
				});

				// 显示退出成功提示
				uni.showToast({
					title: '已退出登录',
					icon: 'success'
				});
			},

			// 取消退出登录
			cancelLogout() {
				console.log('❌ 取消退出登录');
				this.showLogoutModal = false;
			},




		}
	}
</script>

<style lang="scss">
	.profile-container {
		min-height: 100vh;
		background: #F8F9FA;
		padding-bottom: env(safe-area-inset-bottom);
	}

	/* 个人信息背景区域 */
	.profile-header {
		background-image: url('/static/images/backgrounds/profile-bg.jpg');
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		position: relative;
		padding-top: env(safe-area-inset-top);
		padding-top: 80rpx;
		padding-bottom: 120rpx;

		/* 添加半透明遮罩确保文字可读性 */
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.3);
			z-index: 1;
		}
	}

	/* 位置信息显示 - 左上角 */
	.location-info {
		position: absolute;
		top: calc(env(safe-area-inset-top) + 20rpx);
		left: 30rpx;
		z-index: 10;
	}

	.location-row {
		display: flex;
		align-items: center;
	}

	.location-text {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.9);
		background: rgba(0, 0, 0, 0.3);
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		backdrop-filter: blur(10rpx);
	}

	/* 更多功能按钮 - 右上角 */
	.more-btn-header {
		position: absolute;
		top: calc(env(safe-area-inset-top) + 20rpx);
		right: 30rpx;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		z-index: 3;
	}

	/* 图标尺寸样式 */
	.icon-22 {
		width: 44rpx;
		height: 44rpx;
	}

	.icon-16 {
		width: 32rpx;
		height: 32rpx;
	}

	.icon-20 {
		width: 40rpx;
		height: 40rpx;
	}

	.icon-24 {
		width: 48rpx;
		height: 48rpx;
	}

	.icon-12 {
		width: 24rpx;
		height: 24rpx;
	}

	/* 个人信息内容 */
	.profile-content {
		display: flex;
		align-items: flex-start;
		padding: 80rpx 30rpx 0 30rpx;
		position: relative;
		z-index: 2;
	}

	/* 已登录内容 */
	.logged-in-content {
		display: flex;
		align-items: flex-start;
		width: 100%;
	}

	/* 未登录内容 */
	.not-logged-in-content {
		display: flex;
		align-items: flex-start;
		width: 100%;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.not-logged-in-content:active {
		transform: scale(0.98);
	}

	.avatar-section {
		margin-right: 30rpx;
		position: relative;
	}

	.user-avatar-large {
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
		border: 6rpx solid rgba(255, 255, 255, 0.8);
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
	}

	.backup-avatar {
		position: absolute;
		top: 0;
		left: 0;
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
		border: 6rpx solid rgba(255, 255, 255, 0.8);
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
		z-index: 2;
	}

	.user-info-section {
		flex: 1;
		padding-top: 20rpx;
	}

	.user-name-large {
		font-size: 44rpx;
		font-weight: 700;
		color: #fff;
		margin-bottom: 24rpx;
		text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
		display: block;
		width: 100%;
	}

	.user-id-container {
		display: flex;
		align-items: center;
		margin-bottom: 28rpx;
		gap: 8rpx;
	}

	.user-id-text {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.9);
	}

	.copy-id-btn {
		padding: 6rpx;
		border-radius: 8rpx;
		background: rgba(255, 255, 255, 0.15);
		display: flex;
		align-items: center;
		justify-content: center;
		min-width: 40rpx;
		min-height: 40rpx;
	}

	.user-bio-row {
		font-size: 28rpx;
		color: #333;
		line-height: 1.6;
		padding: 24rpx 40rpx;
		word-break: break-all;
		overflow-wrap: break-word;
	}

	/* 用户徽章区域 */
	.user-badges {
		display: flex;
		gap: 24rpx;
		flex-wrap: nowrap;
		align-items: center;
		margin-top: 16rpx;
	}

	.badge-item {
		display: flex;
		align-items: center;
		gap: 10rpx;
		padding: 12rpx 20rpx;
		background: rgba(255, 255, 255, 0.25);
		border-radius: 24rpx;
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.3);
		flex-shrink: 0;
		transition: all 0.2s;
		margin-right: 8rpx;
	}

	.badge-item:active {
		background: rgba(255, 255, 255, 0.35);
		transform: scale(0.95);
	}

	.badge-item.badge-inactive {
		opacity: 0.5;

		.icon-16 {
			filter: grayscale(100%);
		}

		.badge-text {
			color: rgba(255, 255, 255, 0.6);
		}
	}

	.badge-text {
		font-size: 22rpx;
		color: rgba(255, 255, 255, 0.95);
		font-weight: 500;
		white-space: nowrap;
	}

	/* 未登录状态样式 */
	.user-subtitle {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
		margin-bottom: 20rpx;
		display: block;
	}

	.login-hint {
		display: flex;
		align-items: center;
		gap: 12rpx;
		background: rgba(255, 255, 255, 0.15);
		padding: 16rpx 20rpx;
		border-radius: 20rpx;
		backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
	}

	.login-text {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.9);
		font-weight: 500;
	}

	.login-arrow {
		width: 24rpx;
		height: 24rpx;
		filter: brightness(0) invert(1);
		opacity: 0.8;
	}

	/* 数据统计区域 - 全屏显示 */
	.stats-section-fullwidth {
		background: rgba(255, 255, 255, 0.95);
		margin-top: -60rpx;
		padding: 40rpx 0 20rpx;
		border-radius: 32rpx 32rpx 0 0;
		position: relative;
		z-index: 2;
		box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
	}

	.stats-row {
		display: flex;
		justify-content: space-around;
		align-items: center;
		padding: 0 40rpx 32rpx;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
	}

	.stat-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		padding: 20rpx 10rpx;
		border-radius: 16rpx;
		transition: background-color 0.2s;
	}

	.stat-item:active {
		background-color: rgba(0, 0, 0, 0.05);
	}

	.stat-number {
		font-size: 36rpx;
		font-weight: 700;
		color: #333;
		margin-bottom: 8rpx;
	}

	.stat-label {
		font-size: 24rpx;
		color: #666;
		font-weight: 400;
	}

	/* 金刚功能区 */
	.diamond-section {
		background: #fff;
		padding: 30rpx;
		border-bottom: 20rpx solid #F8F9FA;
	}

	.diamond-row {
		display: flex;
		justify-content: space-around;
	}

	.diamond-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
	}

	.diamond-icon {
		width: 80rpx;
		height: 80rpx;
		background: #F8F9FA;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 16rpx;
	}

	.diamond-text {
		font-size: 24rpx;
		color: #666;
		font-weight: 400;
	}



	/* 切换菜单功能区 */
	.tab-section {
		background: #fff;
		border-bottom: 1rpx solid #F0F0F0;
	}

	.tab-container {
		display: flex;
		padding: 0 30rpx;
	}

	.tab-item {
		flex: 1;
		text-align: center;
		padding: 30rpx 0;
		position: relative;

		&.active {
			.tab-text {
				color: #333;
				font-weight: 600;
			}

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 40rpx;
				height: 4rpx;
				background: #333;
				border-radius: 2rpx;
			}
		}
	}

	.tab-text {
		font-size: 28rpx;
		color: #999;
		font-weight: 400;
		transition: all 0.3s ease;
	}

	/* 内容区域 */
	.content-section {
		background: #fff;
		min-height: 400rpx;
	}

	.content-container {
		padding: 60rpx 30rpx;
	}

	.empty-state {
		text-align: center;
		padding: 80rpx 0;
	}

	.empty-illustration {
		width: 200rpx;
		height: 200rpx;
		margin: 0 auto 30rpx auto;
		display: block;
	}

	.empty-text {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 12rpx;
		font-weight: 500;
		text-align: center;
	}

	.empty-desc {
		font-size: 24rpx;
		color: #999;
		text-align: center;
	}



	/* 右侧半页更多功能弹窗 */
	.side-menu-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		z-index: 1000;
		display: flex;
		justify-content: flex-end;
	}

	.side-menu-panel {
		width: 70%;
		height: 100%;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(20px);
		-webkit-backdrop-filter: blur(20px);
		border-top-left-radius: 30rpx;
		border-bottom-left-radius: 30rpx;
		box-shadow: -8rpx 0 32rpx rgba(0, 0, 0, 0.1);
		transform: translateX(100%);
		transition: transform 0.3s ease;

		&.show {
			transform: translateX(0);
		}
	}

	.drag-indicator {
		width: 60rpx;
		height: 8rpx;
		background: #E5E5E5;
		border-radius: 4rpx;
		margin: 20rpx auto;
	}

	.side-menu-header {
		background: #fff;
		padding: 30rpx;
		border-top-left-radius: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1rpx solid #F0F0F0;
	}

	.menu-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
	}

	.close-btn {
		width: 48rpx;
		height: 48rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		background: #F8F9FA;
	}

	.side-menu-scroll {
		height: calc(100vh - 200rpx);
		background: #fff;
	}

	.side-menu-content {
		padding: 0;
		background: #fff;
	}

	.menu-item {
		display: flex;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #F8F9FA;

		&:last-child {
			border-bottom: none;
		}

		&:active {
			background: #F8F9FA;
		}
	}

	.item-icon {
		margin-right: 24rpx;
	}

	.item-title {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		font-weight: 400;
	}

	/* 弹窗中的退出登录 */
	.logout-section {
		padding: 30rpx;
		background: #fff;
		border-top: 20rpx solid #F8F9FA;
	}

	.logout-btn-wrapper {
		width: 100%;
		cursor: pointer;
	}

	.logout-btn {
		width: 100%;
		background: #FF4757;
		color: #fff;
		border-radius: 16rpx;
		padding: 28rpx;
		font-size: 28rpx;
		font-weight: 500;
		border: none;
		pointer-events: none;
	}

	/* 年轻化退出登录弹窗样式 */
	.logout-modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		backdrop-filter: blur(10rpx);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10000; /* 确保在更多菜单之上 */
		animation: fadeIn 0.3s ease-out;
	}

	.logout-modal-content {
		background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
		border-radius: 40rpx;
		padding: 80rpx 60rpx 60rpx;
		margin: 0 60rpx;
		max-width: 600rpx;
		width: 100%;
		text-align: center;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
		animation: slideUp 0.3s ease-out;
		position: relative;
		overflow: hidden;
	}

	.logout-modal-content::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 8rpx;
		background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
	}

	.logout-emoji {
		font-size: 120rpx;
		line-height: 1;
		margin-bottom: 40rpx;
		animation: bounce 0.6s ease-out 0.2s both;
	}

	.logout-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #1a202c;
		margin-bottom: 20rpx;
		line-height: 1.4;
	}

	.logout-subtitle {
		font-size: 28rpx;
		color: #718096;
		margin-bottom: 60rpx;
		line-height: 1.5;
	}

	.logout-buttons {
		display: flex;
		gap: 24rpx;
	}

	.logout-btn-custom {
		flex: 1;
		height: 88rpx;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		font-weight: 600;
		transition: all 0.2s ease;
		position: relative;
		overflow: hidden;
	}

	.cancel-btn {
		background: #f7fafc;
		border: 2rpx solid #e2e8f0;
		color: #4a5568;
	}

	.cancel-btn:active {
		background: #edf2f7;
		transform: scale(0.98);
	}

	.confirm-btn {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	}

	.confirm-btn:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
	}

	.btn-text {
		position: relative;
		z-index: 1;
	}

	/* 动画效果 */
	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	@keyframes slideUp {
		from {
			opacity: 0;
			transform: translateY(60rpx) scale(0.95);
		}
		to {
			opacity: 1;
			transform: translateY(0) scale(1);
		}
	}

	@keyframes bounce {
		0% {
			opacity: 0;
			transform: scale(0.3) translateY(-20rpx);
		}
		50% {
			opacity: 1;
			transform: scale(1.1) translateY(-10rpx);
		}
		100% {
			opacity: 1;
			transform: scale(1) translateY(0);
		}
	}

</style>
