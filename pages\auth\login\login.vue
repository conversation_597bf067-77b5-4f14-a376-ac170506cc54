<template>
	<view class="container">
		<!-- 背景图片 -->
		<image src="/static/logoo.png" class="background-image" mode="aspectFill"></image>

		<!-- 状态栏占位 -->
		<view class="status-bar" :style="getStatusBarStyle()"></view>

		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="back-button" @click="goBack">
				<image src="/static/fanhuilogo.png" class="back-icon" mode="aspectFit"></image>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<!-- 标题 -->
			<view class="title-section">
				<text class="title">登录</text>
				<text class="subtitle">欢迎回到趣嗒同行</text>
			</view>

			<!-- 登录表单 -->
			<view class="form-section">
				<!-- 手机号输入 -->
				<view class="input-group">
					<text class="input-label">手机号</text>
					<input
						class="input-field"
						type="number"
						placeholder="请输入手机号"
						v-model="mobile"
						maxlength="11"
					/>
				</view>

				<!-- 密码输入 -->
				<view class="input-group">
					<text class="input-label">密码</text>
					<view class="password-container">
						<input
							class="input-field password-input"
							:type="showPassword ? 'text' : 'password'"
							placeholder="请输入密码"
							v-model="password"
							maxlength="20"
						/>
						<view class="password-toggle" @click="togglePassword">
							<image 
								:src="showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'" 
								class="eye-icon" 
								mode="aspectFit"
							></image>
						</view>
					</view>
				</view>

				<!-- 登录按钮 -->
				<view class="login-btn" @click="handleLogin" :class="{ disabled: !canLogin }">
					<text class="login-btn-text">登录</text>
				</view>

				<!-- 底部链接 -->
				<view class="bottom-links">
					<text class="link-text" @click="goToRegister">还没有账号？立即注册</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			mobile: '',
			password: '',
			showPassword: false,
			statusBarHeight: 0
		}
	},
	
	computed: {
		canLogin() {
			return this.mobile.length === 11 && this.password.length >= 6
		}
	},
	
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight || 0
	},
	
	methods: {
		getStatusBarStyle() {
			return {
				height: this.statusBarHeight + 'px'
			}
		},
		
		goBack() {
			uni.navigateBack()
		},
		
		togglePassword() {
			this.showPassword = !this.showPassword
		},
		
		async handleLogin() {
			if (!this.canLogin) {
				return
			}
			
			// 验证手机号格式
			if (!/^1[3-9]\d{9}$/.test(this.mobile)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				})
				return
			}
			
			try {
				uni.showLoading({
					title: '登录中...'
				})
				
				const result = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'login',
						mobile: this.mobile,
						password: this.password
					}
				})
				
				uni.hideLoading()
				
				if (result.result.code === 0) {
					console.log('🎉 登录成功，准备保存用户信息');
					console.log('📋 完整返回数据:', result.result.data);
					console.log('📋 返回的用户信息:', result.result.data.userInfo);
					console.log('🔑 返回的token:', result.result.data.token);

					// 智能处理不同的数据结构
					let userInfo, token;

					if (result.result.data.userInfo && result.result.data.token) {
						// 标准结构：data.userInfo 和 data.token
						userInfo = result.result.data.userInfo;
						token = result.result.data.token;
						console.log('✅ 使用标准数据结构');
					} else if (result.result.data._id) {
						// 直接结构：data 就是用户信息，token 在 result.result.token
						userInfo = result.result.data;
						token = result.result.token || result.result.data.token || 'temp_token_' + result.result.data._id;
						console.log('✅ 使用直接数据结构');
					} else {
						console.error('❌ 无法识别的数据结构!');
						uni.showToast({
							title: '登录数据异常',
							icon: 'none'
						})
						return
					}

					// 保存用户信息到本地
					uni.setStorageSync('userInfo', userInfo)
					uni.setStorageSync('token', token)
					uni.setStorageSync('isLoggedIn', true)

					// 验证保存是否成功
					const savedUserInfo = uni.getStorageSync('userInfo');
					console.log('✅ 验证保存的用户信息:', savedUserInfo);

					uni.showToast({
						title: '登录成功',
						icon: 'success'
					})

					// 跳转到首页
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/index/index'
						})
					}, 1500)
				} else {
					uni.showToast({
						title: result.result.message || '登录失败',
						icon: 'none'
					})
				}
			} catch (error) {
				uni.hideLoading()
				console.error('登录失败:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			}
		},
		
		goToRegister() {
			uni.navigateTo({
				url: '/pages/auth/phone-register/phone-register'
			})
		}
	}
}
</script>

<style scoped>
@import '@/styles/global.scss';

.container {
	width: 100%;
	min-height: 100vh;
	position: relative;
	overflow: hidden;
}

.background-image {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: -1;
}

.status-bar {
	width: 100%;
	background: transparent;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 32rpx;
	z-index: 100;
	padding-top: var(--status-bar-height);
}

.back-button {
	width: 64rpx;
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon {
	width: 48rpx;
	height: 48rpx;
}

.content {
	padding: 460rpx 40rpx 60rpx;
	min-height: 100vh;
	box-sizing: border-box;
}

.title-section {
	text-align: center;
	margin-bottom: 80rpx;
}

.title {
	font-size: 48rpx;
	font-weight: bold;
	color: #FFFFFF;
	display: block;
	margin-bottom: 20rpx;
}

.subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.form-section {
	width: 100%;
}

.input-group {
	margin-bottom: 40rpx;
}

.input-label {
	font-size: 32rpx;
	color: #FFFFFF;
	display: block;
	margin-bottom: 20rpx;
}

.input-field {
	width: 100%;
	height: 88rpx;
	background: rgba(255, 255, 255, 0.2);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 16rpx;
	padding: 0 32rpx;
	font-size: 32rpx;
	color: #FFFFFF;
}

.password-container {
	position: relative;
}

.password-input {
	padding-right: 80rpx;
}

.password-toggle {
	position: absolute;
	right: 32rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.eye-icon {
	width: 36rpx;
	height: 36rpx;
}

.login-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 60rpx;
	margin-bottom: 40rpx;
}

.login-btn.disabled {
	background: rgba(255, 255, 255, 0.3);
}

.login-btn-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #FFFFFF;
}

.bottom-links {
	text-align: center;
}

.link-text {
	font-size: 28rpx;
	color: #667eea;
	text-decoration: underline;
}
</style>
