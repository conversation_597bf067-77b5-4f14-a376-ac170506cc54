# 网络问题排查指南

## 问题描述
实机登录时提示"网络问题已为您切换到离线模式"，无法正常进行登录注册操作。

## 可能原因分析

### 1. uniCloud配置问题
- **问题**：manifest.json中的uniCloud配置不正确
- **检查方法**：确认spaceId和clientSecret是否正确
- **解决方案**：
  ```json
  "uniCloud": {
    "provider": "aliyun",
    "spaceId": "mp-4f58ec35-fd98-402d-99e4-2bb423eaf604",
    "clientSecret": "pZBK1la3eLXxOMrhsDatXg=="
  }
  ```

### 2. 云函数未部署
- **问题**：云函数没有正确部署到云端
- **检查方法**：在应用中运行"网络诊断"功能
- **解决方案**：
  1. 在HBuilderX中右键 `uniCloud-aliyun/cloudfunctions`
  2. 选择"上传所有云函数"
  3. 等待部署完成

### 3. 网络权限配置
- **问题**：Android应用缺少必要的网络权限
- **检查方法**：查看manifest.json中的permissions配置
- **解决方案**：已添加以下权限
  ```json
  "permissions": [
    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>"
  ]
  ```

### 4. 网络安全配置
- **问题**：Android 9.0+默认禁用HTTP明文传输
- **解决方案**：已添加 `"usesCleartextTraffic": true` 配置

### 5. 数据库未初始化
- **问题**：云数据库没有正确初始化
- **解决方案**：
  1. 在HBuilderX中右键 `uniCloud-aliyun/database`
  2. 选择"初始化云数据库"
  3. 等待初始化完成

## 修复步骤

### 第一步：检查基础配置
1. 确认manifest.json中的uniCloud配置正确
2. 确认网络权限已添加
3. 重新编译应用

### 第二步：部署云函数
1. 打开HBuilderX
2. 右键 `uniCloud-aliyun/cloudfunctions`
3. 选择"上传所有云函数"
4. 等待所有云函数部署完成

### 第三步：初始化数据库
1. 右键 `uniCloud-aliyun/database`
2. 选择"初始化云数据库"
3. 确认数据库表创建成功

### 第四步：测试连接
1. 运行应用
2. 在登录页面点击"网络连接诊断"
3. 查看诊断结果
4. 根据提示进行修复

## 诊断工具使用

### 网络诊断功能
应用内置了完整的网络诊断工具：
1. **基础网络检测**：检查设备网络连接状态
2. **uniCloud连接测试**：测试云服务连接
3. **云函数部署检查**：验证云函数是否正确部署
4. **数据库连接测试**：测试数据库访问

### 使用方法
1. 在登录页面点击"网络连接诊断"按钮
2. 等待诊断完成
3. 查看详细诊断报告
4. 根据建议进行修复

### 自动修复功能
1. 在登录页面点击"自动修复网络"按钮
2. 系统会自动清理缓存和重置配置
3. 建议修复后重启应用

## 常见错误代码

### FUNCTION_NOT_FOUND
- **含义**：云函数未找到
- **解决**：部署对应的云函数

### FUNCTION_EXECUTION_FAIL
- **含义**：云函数执行失败
- **解决**：检查云函数代码和数据库配置

### NETWORK_ERROR
- **含义**：网络连接超时
- **解决**：检查网络连接和防火墙设置

### INVALID_PARAM
- **含义**：参数错误
- **解决**：检查传递给云函数的参数

## 开发环境配置

### HBuilderX设置
1. 确保HBuilderX版本为最新
2. 登录DCloud账号
3. 绑定uniCloud服务空间

### 云服务空间配置
1. 登录uniCloud控制台
2. 确认服务空间状态正常
3. 检查云函数和数据库状态

## 生产环境注意事项

### 安全配置
1. 更换正式的clientSecret
2. 配置域名白名单
3. 启用访问控制

### 性能优化
1. 配置CDN加速
2. 优化云函数代码
3. 设置合理的超时时间

## 联系支持
如果以上步骤都无法解决问题，请：
1. 收集完整的错误日志
2. 记录网络诊断结果
3. 提供设备和网络环境信息
4. 联系技术支持

## 更新日志
- 2024-07-01：添加网络诊断工具
- 2024-07-01：优化错误处理机制
- 2024-07-01：增加自动修复功能
