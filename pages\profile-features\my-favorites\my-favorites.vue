<template>
	<view class="my-favorites-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<uni-icons type="left" size="24" color="#333"></uni-icons>
					<text class="back-text">返回</text>
				</view>
				<text class="nav-title">我的收藏</text>
				<view class="nav-right">
					<view class="search-btn" @tap="searchFavorites">
						<uni-icons type="search" size="20" color="#666"></uni-icons>
					</view>
					<view class="manage-btn" @tap="toggleManageMode">
						<uni-icons type="settings" size="20" color="#666"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 收藏统计 -->
		<view class="favorites-stats">
			<view class="stats-container">
				<view class="stat-item">
					<text class="stat-number">{{ totalFavorites }}</text>
					<text class="stat-label">全部收藏</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-number">{{ recentAdded }}</text>
					<text class="stat-label">本月新增</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-number">{{ favoriteCategories }}</text>
					<text class="stat-label">收藏夹</text>
				</view>
			</view>
		</view>

		<!-- 收藏分类 -->
		<view class="favorite-tabs">
			<scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false">
				<view class="tab-item" v-for="(tab, index) in favoriteTabs" :key="index" :class="{ active: currentTab === index }" @tap="switchTab(index)">
					<view class="tab-content">
						<uni-icons v-if="tab.icon" :type="tab.icon" size="16" :color="currentTab === index ? '#fff' : '#ff6b6b'"></uni-icons>
						<text class="tab-text">{{ tab.name }}</text>
					</view>
					<view class="tab-count" v-if="tab.count > 0">{{ tab.count }}</view>
				</view>
			</scroll-view>
		</view>

		<!-- 收藏列表 -->
		<scroll-view class="favorites-content" scroll-y="true" @scrolltolower="loadMore">
			<!-- 空状态 -->
			<view class="empty-state" v-if="currentFavorites.length === 0">
				<view class="empty-icon">
					<uni-icons type="heart" size="80" color="#ddd"></uni-icons>
				</view>
				<text class="empty-title">暂无收藏</text>
				<text class="empty-desc">{{ getEmptyDesc() }}</text>
				<view class="empty-action" @tap="exploreContent">
					<text class="action-text">去发现</text>
				</view>
			</view>

			<!-- 收藏网格 -->
			<view class="favorites-grid" v-else>
				<view class="favorite-item" v-for="favorite in currentFavorites" :key="favorite.id" @tap="viewFavorite(favorite)" :class="{ selected: manageMode && selectedItems.includes(favorite.id) }">
					
					<!-- 管理模式选择框 -->
					<view class="select-checkbox" v-if="manageMode" @tap.stop="toggleSelect(favorite.id)">
						<view class="checkbox" :class="{ checked: selectedItems.includes(favorite.id) }">
							<uni-icons v-if="selectedItems.includes(favorite.id)" type="checkmarkempty" size="16" color="#fff"></uni-icons>
						</view>
					</view>

					<view class="favorite-image-container">
						<image class="favorite-image" :src="favorite.cover" mode="aspectFill"></image>
						<view class="favorite-type" :class="favorite.typeClass">
							<uni-icons :type="favorite.typeIcon" size="16" color="#fff"></uni-icons>
						</view>
						<view class="favorite-duration" v-if="favorite.duration">
							<text class="duration-text">{{ favorite.duration }}</text>
						</view>
					</view>

					<view class="favorite-info">
						<text class="favorite-title">{{ favorite.title }}</text>
						<text class="favorite-author">{{ favorite.author }}</text>
						<view class="favorite-stats">
							<view class="stat-item">
								<uni-icons type="eye" size="12" color="#999"></uni-icons>
								<text class="stat-text">{{ favorite.views }}</text>
							</view>
							<view class="stat-item">
								<uni-icons type="heart" size="12" color="#999"></uni-icons>
								<text class="stat-text">{{ favorite.likes }}</text>
							</view>
						</view>
						<text class="favorite-time">{{ favorite.favoriteTime }}</text>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore">
				<text class="load-text">加载更多收藏...</text>
			</view>
		</scroll-view>

		<!-- 管理模式底部操作栏 -->
		<view class="manage-toolbar" v-if="manageMode">
			<view class="toolbar-left">
				<text class="selected-count">已选择 {{ selectedItems.length }} 项</text>
			</view>
			<view class="toolbar-right">
				<view class="toolbar-btn" @tap="moveToFolder">
					<uni-icons type="folder" size="20" color="#666"></uni-icons>
					<text class="btn-text">移动</text>
				</view>
				<view class="toolbar-btn danger" @tap="deleteFavorites">
					<uni-icons type="trash" size="20" color="#ff4757"></uni-icons>
					<text class="btn-text">删除</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				currentTab: 0,
				hasMore: true,
				manageMode: false,
				selectedItems: [],
				
				// 收藏分类标签
				favoriteTabs: [
					{ name: '全部', count: 0, icon: 'list' },
					{ name: '图文', count: 12, icon: 'image' },
					{ name: '视频', count: 8, icon: 'videocam' },
					{ name: '音频', count: 3, icon: 'sound' },
					{ name: '活动', count: 5, icon: 'calendar' }
				],

				// 所有收藏数据
				allFavorites: [
					{
						id: '001',
						title: '城市夜景摄影技巧分享',
						author: '摄影师小王',
						cover: 'https://s1.imagehub.cc/images/2025/05/26/night-photography.png',
						type: 'image',
						typeClass: 'image',
						typeIcon: 'image',
						views: 2345,
						likes: 189,
						favoriteTime: '2024-12-20',
						category: 'photography'
					},
					{
						id: '002',
						title: '美食制作教程',
						author: '美食达人',
						cover: 'https://s1.imagehub.cc/images/2025/05/26/cooking.png',
						type: 'video',
						typeClass: 'video',
						typeIcon: 'videocam',
						duration: '05:23',
						views: 1567,
						likes: 234,
						favoriteTime: '2024-12-18',
						category: 'food'
					},
					{
						id: '003',
						title: '放松冥想音乐',
						author: '音乐制作人',
						cover: 'https://s1.imagehub.cc/images/2025/05/26/meditation.png',
						type: 'audio',
						typeClass: 'audio',
						typeIcon: 'mic',
						duration: '15:00',
						views: 987,
						likes: 156,
						favoriteTime: '2024-12-15',
						category: 'music'
					},
					{
						id: '004',
						title: '周末户外徒步活动',
						author: '户外俱乐部',
						cover: 'https://s1.imagehub.cc/images/2025/05/26/hiking-event.png',
						type: 'event',
						typeClass: 'event',
						typeIcon: 'calendar',
						views: 456,
						likes: 78,
						favoriteTime: '2024-12-12',
						category: 'event'
					}
				]
			}
		},

		computed: {
			totalFavorites() {
				return this.allFavorites.length;
			},

			recentAdded() {
				const currentMonth = new Date().getMonth() + 1;
				return this.allFavorites.filter(favorite => {
					const favoriteMonth = new Date(favorite.favoriteTime).getMonth() + 1;
					return favoriteMonth === currentMonth;
				}).length;
			},

			favoriteCategories() {
				const categories = new Set(this.allFavorites.map(favorite => favorite.category));
				return categories.size;
			},

			currentFavorites() {
				if (this.currentTab === 0) {
					return this.allFavorites;
				}
				const typeMap = {
					1: 'image',
					2: 'video',
					3: 'audio',
					4: 'event'
				};
				return this.allFavorites.filter(favorite => favorite.type === typeMap[this.currentTab]);
			}
		},

		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},

		methods: {
			goBack() {
				uni.switchTab({
					url: '/pages/profile/profile'
				});
			},

			searchFavorites() {
				uni.showToast({
					title: '搜索功能开发中',
					icon: 'none'
				});
			},

			toggleManageMode() {
				this.manageMode = !this.manageMode;
				if (!this.manageMode) {
					this.selectedItems = [];
				}
			},

			switchTab(index) {
				this.currentTab = index;
				this.selectedItems = [];
			},

			getEmptyDesc() {
				const descs = [
					'还没有收藏任何内容哦',
					'暂无图文收藏',
					'暂无视频收藏',
					'暂无音频收藏',
					'暂无活动收藏'
				];
				return descs[this.currentTab];
			},

			exploreContent() {
				uni.switchTab({
					url: '/pages/discover/discover'
				});
			},

			viewFavorite(favorite) {
				if (this.manageMode) {
					this.toggleSelect(favorite.id);
					return;
				}

				uni.navigateTo({
					url: `/pages/content/content-detail?id=${favorite.id}&type=${favorite.type}`
				});
			},

			toggleSelect(id) {
				const index = this.selectedItems.indexOf(id);
				if (index > -1) {
					this.selectedItems.splice(index, 1);
				} else {
					this.selectedItems.push(id);
				}
			},

			moveToFolder() {
				if (this.selectedItems.length === 0) {
					uni.showToast({
						title: '请先选择要移动的项目',
						icon: 'none'
					});
					return;
				}

				uni.showToast({
					title: '移动功能开发中',
					icon: 'none'
				});
			},

			deleteFavorites() {
				if (this.selectedItems.length === 0) {
					uni.showToast({
						title: '请先选择要删除的项目',
						icon: 'none'
					});
					return;
				}

				uni.showModal({
					title: '删除收藏',
					content: `确定要删除选中的 ${this.selectedItems.length} 个收藏吗？`,
					success: (res) => {
						if (res.confirm) {
							this.selectedItems = [];
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},

			loadMore() {
				if (!this.hasMore) return;
				
				setTimeout(() => {
					this.hasMore = false;
				}, 1000);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.my-favorites-page {
		background: #f5f5f5;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.status-bar {
		background: #fff;
	}

	.header {
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;

		.nav-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;
			height: 88rpx;

			.nav-left {
				display: flex;
				align-items: center;
				gap: 8rpx;
				flex: 1;

				.back-text {
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				text-align: center;
				flex: 2;
			}

			.nav-right {
				flex: 1;
				display: flex;
				justify-content: flex-end;
				gap: 16rpx;

				.search-btn, .manage-btn {
					width: 48rpx;
					height: 48rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: #f8f9fa;
					border-radius: 24rpx;
				}
			}
		}
	}

	// 收藏统计
	.favorites-stats {
		background: #fff;
		margin: 16rpx 24rpx;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

		.stats-container {
			display: flex;
			align-items: center;
			padding: 32rpx 24rpx;

			.stat-item {
				flex: 1;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
				gap: 8rpx;
				padding: 8rpx 12rpx;
				border-radius: 12rpx;
				transition: all 0.3s ease;

				&:active {
					background: #fff5f5;
					transform: scale(0.98);
				}

				.stat-number {
					font-size: 48rpx;
					color: #ff6b6b;
					font-weight: 700;
				}

				.stat-label {
					font-size: 24rpx;
					color: #999;
					font-weight: 500;
				}
			}

			.stat-divider {
				width: 1rpx;
				height: 60rpx;
				background: #f0f0f0;
			}
		}
	}

	// 收藏分类
	.favorite-tabs {
		background: #fff;
		margin: 0 24rpx 16rpx;
		border-radius: 16rpx;
		padding: 8rpx;

		.tabs-scroll {
			white-space: nowrap;

			.tab-item {
				display: inline-block;
				position: relative;
				padding: 16rpx 24rpx;
				margin-right: 8rpx;
				border-radius: 12rpx;
				transition: all 0.3s ease;

				&.active {
					background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);

					.tab-text {
						color: #fff;
						font-weight: 600;
					}

					.tab-count {
						background: #fff;
						color: #ff6b6b;
					}
				}

				.tab-content {
					display: flex;
					align-items: center;
					gap: 6rpx;
				}

				.tab-text {
					font-size: 26rpx;
					color: #666;
					transition: all 0.3s ease;
				}

				.tab-count {
					position: absolute;
					top: 4rpx;
					right: 4rpx;
					background: #f0f0f0;
					color: #999;
					font-size: 18rpx;
					padding: 2rpx 6rpx;
					border-radius: 8rpx;
					min-width: 20rpx;
					text-align: center;
					line-height: 1;
				}
			}
		}
	}

	// 收藏内容区域
	.favorites-content {
		flex: 1;
		padding: 0 24rpx 120rpx;

		// 空状态
		.empty-state {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 120rpx 40rpx;
			text-align: center;

			.empty-icon {
				margin-bottom: 32rpx;
				opacity: 0.5;
			}

			.empty-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				margin-bottom: 16rpx;
			}

			.empty-desc {
				font-size: 26rpx;
				color: #999;
				margin-bottom: 48rpx;
				line-height: 1.5;
			}

			.empty-action {
				background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
				padding: 20rpx 48rpx;
				border-radius: 32rpx;
				box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);

				.action-text {
					font-size: 28rpx;
					color: #fff;
					font-weight: 600;
				}
			}
		}

		// 收藏网格
		.favorites-grid {
			display: flex;
			flex-wrap: wrap;
			gap: 16rpx;

			.favorite-item {
				width: calc(50% - 8rpx);
				background: #fff;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
				position: relative;
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
				}

				&.selected {
					border: 2rpx solid #ff6b6b;
					box-shadow: 0 4rpx 20rpx rgba(255, 107, 107, 0.3);
				}

				.select-checkbox {
					position: absolute;
					top: 12rpx;
					left: 12rpx;
					z-index: 10;

					.checkbox {
						width: 32rpx;
						height: 32rpx;
						border: 2rpx solid rgba(255, 255, 255, 0.8);
						border-radius: 16rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						background: rgba(0, 0, 0, 0.3);
						transition: all 0.3s ease;

						&.checked {
							background: #ff6b6b;
							border-color: #ff6b6b;
						}
					}
				}

				.favorite-image-container {
					position: relative;
					height: 240rpx;

					.favorite-image {
						width: 100%;
						height: 100%;
						background: #f5f5f5;
					}

					.favorite-type {
						position: absolute;
						top: 12rpx;
						right: 12rpx;
						width: 32rpx;
						height: 32rpx;
						border-radius: 16rpx;
						display: flex;
						align-items: center;
						justify-content: center;

						&.image {
							background: rgba(76, 205, 196, 0.9);
						}

						&.video {
							background: rgba(255, 107, 107, 0.9);
						}

						&.audio {
							background: rgba(255, 193, 7, 0.9);
						}

						&.event {
							background: rgba(102, 126, 234, 0.9);
						}
					}

					.favorite-duration {
						position: absolute;
						bottom: 12rpx;
						right: 12rpx;
						background: rgba(0, 0, 0, 0.7);
						color: #fff;
						font-size: 20rpx;
						padding: 4rpx 8rpx;
						border-radius: 8rpx;

						.duration-text {
							font-size: 20rpx;
						}
					}
				}

				.favorite-info {
					padding: 16rpx;

					.favorite-title {
						font-size: 26rpx;
						color: #333;
						font-weight: 600;
						display: block;
						margin-bottom: 8rpx;
						line-height: 1.3;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					.favorite-author {
						font-size: 22rpx;
						color: #999;
						display: block;
						margin-bottom: 12rpx;
					}

					.favorite-stats {
						display: flex;
						align-items: center;
						gap: 16rpx;
						margin-bottom: 8rpx;

						.stat-item {
							display: flex;
							align-items: center;
							gap: 4rpx;

							.stat-text {
								font-size: 20rpx;
								color: #999;
							}
						}
					}

					.favorite-time {
						font-size: 20rpx;
						color: #ccc;
					}
				}
			}
		}

		// 加载更多
		.load-more {
			text-align: center;
			padding: 32rpx;

			.load-text {
				font-size: 26rpx;
				color: #999;
			}
		}
	}

	// 管理模式工具栏
	.manage-toolbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #fff;
		border-top: 1rpx solid #f0f0f0;
		padding: 16rpx 24rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

		.toolbar-left {
			.selected-count {
				font-size: 26rpx;
				color: #666;
			}
		}

		.toolbar-right {
			display: flex;
			gap: 24rpx;

			.toolbar-btn {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 4rpx;

				&.danger {
					.btn-text {
						color: #ff4757;
					}
				}

				.btn-text {
					font-size: 22rpx;
					color: #666;
				}
			}
		}
	}
</style>
