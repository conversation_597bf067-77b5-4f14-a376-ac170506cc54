<template>
	<view class="likes-page">
		<!-- 状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left" @click="goBack">
				<view class="back-btn">
					<uni-icons type="left" size="18" color="#fff"></uni-icons>
				</view>
			</view>
			<view class="nav-center">
				<text class="nav-title">获赞记录</text>
				<text class="nav-subtitle">累计获得{{ totalLikes }}个赞</text>
			</view>
			<view class="nav-right">
				<view class="filter-btn" @click="showFilterMenu">
					<uni-icons type="tune" size="18" color="#fff"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 统计卡片 -->
		<view class="stats-section">
			<view class="stats-container">
				<view class="stat-card today">
					<view class="stat-icon">
						<uni-icons type="heart-filled" size="24" color="#FF6B6B"></uni-icons>
					</view>
					<view class="stat-info">
						<text class="stat-number">{{ todayLikes }}</text>
						<text class="stat-label">今日获赞</text>
					</view>
				</view>
				<view class="stat-card week">
					<view class="stat-icon">
						<uni-icons type="calendar" size="24" color="#4ECDC4"></uni-icons>
					</view>
					<view class="stat-info">
						<text class="stat-number">{{ weekLikes }}</text>
						<text class="stat-label">本周获赞</text>
					</view>
				</view>
				<view class="stat-card month">
					<view class="stat-icon">
						<uni-icons type="star-filled" size="24" color="#FFD700"></uni-icons>
					</view>
					<view class="stat-info">
						<text class="stat-number">{{ monthLikes }}</text>
						<text class="stat-label">本月获赞</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 时间筛选 -->
		<view class="time-filter">
			<scroll-view scroll-x class="filter-scroll" :show-scrollbar="false">
				<view class="filter-container">
					<view 
						class="filter-item" 
						:class="{ active: activeFilter === index }"
						v-for="(filter, index) in timeFilters" 
						:key="index"
						@click="switchFilter(index)"
					>
						<text class="filter-text">{{ filter.name }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 自定义下拉刷新 -->
		<view class="custom-refresh" v-if="refreshing">
			<view class="refresh-container">
				<view class="stars-animation">
					<view class="star-item star-1">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-2">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-3">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-4">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-5">
						<text class="star-text">✦</text>
					</view>
				</view>
				<text class="refresh-text">正在刷新...</text>
			</view>
		</view>

		<!-- 获赞列表 -->
		<scroll-view
			class="likes-list"
			scroll-y
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd"
			@scrolltolower="loadMore"
		>
			<view class="list-container">
				<view 
					class="like-item" 
					v-for="like in filteredLikes" 
					:key="like.id"
					@click="viewContent(like)"
				>
					<view class="item-content">
						<view class="user-section">
							<image :src="like.userAvatar" class="user-avatar"></image>
							<view class="user-info">
								<text class="user-name">{{ like.userName }}</text>
								<text class="like-time">{{ like.time }}</text>
							</view>
						</view>
						
						<view class="content-section">
							<view class="content-preview">
								<image v-if="like.contentType === 'image'" :src="like.contentThumb" class="content-thumb"></image>
								<view v-else-if="like.contentType === 'video'" class="video-thumb">
									<image :src="like.contentThumb" class="content-thumb"></image>
									<view class="play-icon">
										<uni-icons type="play-filled" size="20" color="#fff"></uni-icons>
									</view>
								</view>
								<view v-else class="text-content">
									<uni-icons type="compose" size="24" color="#999"></uni-icons>
								</view>
							</view>
							<view class="content-info">
								<text class="content-title">{{ like.contentTitle }}</text>
								<view class="content-meta">
									<view class="meta-item">
										<uni-icons type="heart-filled" size="12" color="#FF6B6B"></uni-icons>
										<text class="meta-text">{{ like.totalLikes }}</text>
									</view>
									<view class="meta-item">
										<uni-icons type="chat" size="12" color="#4ECDC4"></uni-icons>
										<text class="meta-text">{{ like.comments }}</text>
									</view>
								</view>
							</view>
						</view>
						
						<view class="like-icon">
							<view class="heart-animation">
								<uni-icons type="heart-filled" size="20" color="#FF6B6B"></uni-icons>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 加载更多 -->
				<view class="load-more" v-if="hasMore">
					<uni-icons type="spinner-cycle" size="20" color="#999"></uni-icons>
					<text class="load-text">加载更多...</text>
				</view>
				
				<!-- 空状态 -->
				<view class="empty-state" v-if="filteredLikes.length === 0 && !loading">
					<view class="empty-icon">
						<uni-icons type="heart" size="60" color="#ddd"></uni-icons>
					</view>
					<text class="empty-title">暂无获赞记录</text>
					<text class="empty-desc">发布精彩内容获得更多点赞吧~</text>
					<view class="create-btn" @click="goToCreate">
						<text class="create-text">去发布</text>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 筛选菜单弹窗 -->
		<view class="filter-popup" v-if="showFilter" @click="hideFilterMenu">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">筛选条件</text>
					<view class="close-btn" @click="hideFilterMenu">
						<uni-icons type="close" size="18" color="#999"></uni-icons>
					</view>
				</view>
				<view class="filter-options">
					<view class="option-group">
						<text class="group-title">内容类型</text>
						<view class="option-list">
							<view 
								class="option-item" 
								:class="{ active: contentTypeFilter === type.value }"
								v-for="type in contentTypes" 
								:key="type.value"
								@click="setContentTypeFilter(type.value)"
							>
								<uni-icons :type="type.icon" size="16" :color="contentTypeFilter === type.value ? '#fff' : '#666'"></uni-icons>
								<text class="option-text">{{ type.name }}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="popup-footer">
					<view class="reset-btn" @click="resetFilter">
						<text class="reset-text">重置</text>
					</view>
					<view class="confirm-btn" @click="applyFilter">
						<text class="confirm-text">确定</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				activeFilter: 0,
				refreshing: false,
				loading: false,
				hasMore: true,
				showFilter: false,
				contentTypeFilter: 'all',
				touchStartY: 0,
				scrollTop: 0,
				
				totalLikes: 1024,
				todayLikes: 23,
				weekLikes: 156,
				monthLikes: 567,
				
				timeFilters: [
					{ name: '全部', value: 'all' },
					{ name: '今天', value: 'today' },
					{ name: '本周', value: 'week' },
					{ name: '本月', value: 'month' }
				],
				
				contentTypes: [
					{ name: '全部', value: 'all', icon: 'list' },
					{ name: '图片', value: 'image', icon: 'image' },
					{ name: '视频', value: 'video', icon: 'videocam' },
					{ name: '文字', value: 'text', icon: 'compose' }
				],
				
				likes: [
					{
						id: 1,
						userName: '小雨',
						userAvatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						time: '2分钟前',
						contentType: 'image',
						contentTitle: '今天的夕阳真美',
						contentThumb: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						totalLikes: 89,
						comments: 23
					},
					{
						id: 2,
						userName: '阿杰',
						userAvatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						time: '1小时前',
						contentType: 'video',
						contentTitle: '王者荣耀五杀集锦',
						contentThumb: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						totalLikes: 234,
						comments: 67
					}
				]
			}
		},
		
		computed: {
			filteredLikes() {
				let result = this.likes;
				
				// 根据内容类型筛选
				if (this.contentTypeFilter !== 'all') {
					result = result.filter(like => like.contentType === this.contentTypeFilter);
				}
				
				// 根据时间筛选
				const filter = this.timeFilters[this.activeFilter];
				if (filter.value !== 'all') {
					// 这里可以添加时间筛选逻辑
				}
				
				return result;
			}
		},
		
		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},
		
		methods: {
			goBack() {
				uni.navigateBack();
			},
			
			showFilterMenu() {
				this.showFilter = true;
			},
			
			hideFilterMenu() {
				this.showFilter = false;
			},
			
			switchFilter(index) {
				this.activeFilter = index;
			},

			// 触摸开始
			onTouchStart(e) {
				this.touchStartY = e.touches[0].clientY;
				this.scrollTop = 0;
			},

			// 触摸移动
			onTouchMove(e) {
				const currentY = e.touches[0].clientY;
				const deltaY = currentY - this.touchStartY;

				// 只有在页面顶部且向下拉时才触发刷新
				if (this.scrollTop <= 0 && deltaY > 100 && !this.refreshing) {
					this.triggerRefresh();
				}
			},

			// 触摸结束
			onTouchEnd() {
				this.touchStartY = 0;
			},

			// 触发刷新
			triggerRefresh() {
				this.refreshing = true;
				setTimeout(() => {
					this.refreshing = false;
					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					});
				}, 2000);
			},
			
			loadMore() {
				if (this.hasMore && !this.loading) {
					this.loading = true;
					setTimeout(() => {
						this.loading = false;
					}, 1000);
				}
			},
			
			viewContent(like) {
				console.log('查看内容:', like.contentTitle);
			},
			
			setContentTypeFilter(type) {
				this.contentTypeFilter = type;
			},
			
			resetFilter() {
				this.contentTypeFilter = 'all';
				this.activeFilter = 0;
			},
			
			applyFilter() {
				this.hideFilterMenu();
			},
			
			goToCreate() {
				console.log('去发布内容');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.likes-page {
		background: #f5f6fa;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}
	
	.status-bar {
		background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
	}
	
	// 顶部导航
	.top-nav {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16rpx 24rpx;
		background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
		
		.nav-left, .nav-right {
			.back-btn, .filter-btn {
				width: 40rpx;
				height: 40rpx;
				border-radius: 20rpx;
				background: rgba(255, 255, 255, 0.2);
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				
				&:active {
					background: rgba(255, 255, 255, 0.3);
					transform: scale(0.95);
				}
			}
		}
		
		.nav-center {
			flex: 1;
			text-align: center;
			
			.nav-title {
				font-size: 34rpx;
				font-weight: 700;
				color: #fff;
				display: block;
				margin-bottom: 4rpx;
			}
			
			.nav-subtitle {
				font-size: 22rpx;
				color: rgba(255, 255, 255, 0.8);
			}
		}
	}

	// 自定义下拉刷新
	.custom-refresh {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10rpx);

		.refresh-container {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 40rpx 20rpx 20rpx;

			.stars-animation {
				position: relative;
				width: 80rpx;
				height: 80rpx;
				margin-bottom: 16rpx;

				.star-item {
					position: absolute;
					display: flex;
					align-items: center;
					justify-content: center;

					.star-text {
						font-size: 20rpx;
						color: #FF6B6B;
						font-weight: bold;
					}

					&.star-1 {
						top: 0;
						left: 50%;
						transform: translateX(-50%);
						animation: starPulse 1.5s ease-in-out infinite;
					}

					&.star-2 {
						top: 20rpx;
						right: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 0.3s;
					}

					&.star-3 {
						bottom: 20rpx;
						right: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 0.6s;
					}

					&.star-4 {
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						animation: starPulse 1.5s ease-in-out infinite 0.9s;
					}

					&.star-5 {
						top: 20rpx;
						left: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 1.2s;
					}
				}
			}

			.refresh-text {
				font-size: 24rpx;
				color: #666;
				font-weight: 500;
			}
		}
	}

	// 统计卡片
	.stats-section {
		padding: 16rpx 24rpx;

		.stats-container {
			display: flex;
			gap: 16rpx;

			.stat-card {
				flex: 1;
				background: #fff;
				border-radius: 16rpx;
				padding: 20rpx;
				display: flex;
				align-items: center;
				gap: 16rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

				&.today {
					border-left: 4rpx solid #FF6B6B;
				}

				&.week {
					border-left: 4rpx solid #4ECDC4;
				}

				&.month {
					border-left: 4rpx solid #FFD700;
				}

				.stat-icon {
					width: 48rpx;
					height: 48rpx;
					border-radius: 24rpx;
					background: rgba(255, 255, 255, 0.1);
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.stat-info {
					flex: 1;

					.stat-number {
						font-size: 32rpx;
						font-weight: 700;
						color: #333;
						display: block;
						margin-bottom: 4rpx;
					}

					.stat-label {
						font-size: 22rpx;
						color: #999;
					}
				}
			}
		}
	}

	// 时间筛选
	.time-filter {
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;

		.filter-scroll {
			white-space: nowrap;

			.filter-container {
				display: flex;
				padding: 0 24rpx;

				.filter-item {
					padding: 20rpx 24rpx;
					margin-right: 16rpx;
					border-radius: 24rpx;
					transition: all 0.3s ease;

					&.active {
						background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);

						.filter-text {
							color: #fff;
						}
					}

					.filter-text {
						font-size: 26rpx;
						color: #666;
						font-weight: 500;
					}
				}
			}
		}
	}

	// 获赞列表
	.likes-list {
		flex: 1;

		.list-container {
			padding: 16rpx 24rpx;

			.like-item {
				background: #fff;
				border-radius: 20rpx;
				margin-bottom: 16rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
				}

				.item-content {
					display: flex;
					align-items: center;
					padding: 24rpx;
					gap: 20rpx;

					.user-section {
						display: flex;
						align-items: center;
						gap: 16rpx;
						flex-shrink: 0;

						.user-avatar {
							width: 64rpx;
							height: 64rpx;
							border-radius: 32rpx;
							border: 2rpx solid #f0f0f0;
						}

						.user-info {
							.user-name {
								font-size: 26rpx;
								font-weight: 600;
								color: #333;
								display: block;
								margin-bottom: 4rpx;
							}

							.like-time {
								font-size: 22rpx;
								color: #999;
							}
						}
					}

					.content-section {
						flex: 1;
						display: flex;
						align-items: center;
						gap: 16rpx;
						min-width: 0;

						.content-preview {
							position: relative;
							flex-shrink: 0;

							.content-thumb {
								width: 80rpx;
								height: 80rpx;
								border-radius: 12rpx;
							}

							.video-thumb {
								position: relative;

								.play-icon {
									position: absolute;
									top: 50%;
									left: 50%;
									transform: translate(-50%, -50%);
									width: 32rpx;
									height: 32rpx;
									border-radius: 16rpx;
									background: rgba(0, 0, 0, 0.6);
									display: flex;
									align-items: center;
									justify-content: center;
								}
							}

							.text-content {
								width: 80rpx;
								height: 80rpx;
								border-radius: 12rpx;
								background: #f8f9fa;
								display: flex;
								align-items: center;
								justify-content: center;
							}
						}

						.content-info {
							flex: 1;
							min-width: 0;

							.content-title {
								font-size: 28rpx;
								color: #333;
								font-weight: 500;
								display: block;
								margin-bottom: 8rpx;
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;
							}

							.content-meta {
								display: flex;
								gap: 20rpx;

								.meta-item {
									display: flex;
									align-items: center;
									gap: 6rpx;

									.meta-text {
										font-size: 22rpx;
										color: #999;
									}
								}
							}
						}
					}

					.like-icon {
						flex-shrink: 0;

						.heart-animation {
							width: 40rpx;
							height: 40rpx;
							border-radius: 20rpx;
							background: rgba(255, 107, 107, 0.1);
							display: flex;
							align-items: center;
							justify-content: center;
							animation: heartBeat 1.5s ease-in-out infinite;
						}
					}
				}
			}

			// 加载更多
			.load-more {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 12rpx;
				padding: 32rpx;

				.load-text {
					font-size: 26rpx;
					color: #999;
				}
			}

			// 空状态
			.empty-state {
				text-align: center;
				padding: 120rpx 40rpx;

				.empty-icon {
					margin-bottom: 24rpx;
				}

				.empty-title {
					font-size: 32rpx;
					color: #666;
					font-weight: 600;
					display: block;
					margin-bottom: 12rpx;
				}

				.empty-desc {
					font-size: 26rpx;
					color: #999;
					line-height: 1.5;
					margin-bottom: 32rpx;
				}

				.create-btn {
					background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
					color: #fff;
					padding: 16rpx 32rpx;
					border-radius: 24rpx;
					display: inline-block;

					.create-text {
						font-size: 28rpx;
						font-weight: 600;
						color: #fff;
					}
				}
			}
		}
	}

	// 筛选弹窗
	.filter-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: flex-end;
		z-index: 1000;

		.popup-content {
			background: #fff;
			border-radius: 24rpx 24rpx 0 0;
			width: 100%;
			max-height: 60vh;
			overflow: hidden;
			animation: slideUp 0.3s ease-out;

			.popup-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 32rpx 24rpx 16rpx;
				border-bottom: 1rpx solid #f0f0f0;

				.popup-title {
					font-size: 32rpx;
					font-weight: 700;
					color: #333;
				}

				.close-btn {
					width: 40rpx;
					height: 40rpx;
					border-radius: 20rpx;
					background: #f8f9fa;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.filter-options {
				padding: 24rpx;

				.option-group {
					.group-title {
						font-size: 28rpx;
						color: #333;
						font-weight: 600;
						display: block;
						margin-bottom: 16rpx;
					}

					.option-list {
						display: flex;
						flex-wrap: wrap;
						gap: 12rpx;

						.option-item {
							display: flex;
							align-items: center;
							gap: 8rpx;
							padding: 12rpx 20rpx;
							border-radius: 20rpx;
							border: 1rpx solid #e0e0e0;
							transition: all 0.3s ease;

							&.active {
								background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
								border-color: #FF6B6B;

								.option-text {
									color: #fff;
								}
							}

							.option-text {
								font-size: 24rpx;
								color: #666;
								font-weight: 500;
							}
						}
					}
				}
			}

			.popup-footer {
				display: flex;
				gap: 16rpx;
				padding: 24rpx;
				border-top: 1rpx solid #f0f0f0;

				.reset-btn, .confirm-btn {
					flex: 1;
					padding: 16rpx;
					border-radius: 24rpx;
					text-align: center;
					transition: all 0.3s ease;

					&:active {
						transform: scale(0.98);
					}
				}

				.reset-btn {
					background: #f8f9fa;
					border: 1rpx solid #e0e0e0;

					.reset-text {
						font-size: 28rpx;
						color: #666;
						font-weight: 500;
					}
				}

				.confirm-btn {
					background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);

					.confirm-text {
						font-size: 28rpx;
						color: #fff;
						font-weight: 600;
					}
				}
			}
		}
	}

	// 星星脉冲动画
	@keyframes starPulse {
		0%, 100% {
			transform: scale(0.8);
			opacity: 0.5;
		}
		50% {
			transform: scale(1.2);
			opacity: 1;
		}
	}

	@keyframes heartBeat {
		0%, 100% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.1);
		}
	}

	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}
		to {
			transform: translateY(0);
		}
	}
</style>
