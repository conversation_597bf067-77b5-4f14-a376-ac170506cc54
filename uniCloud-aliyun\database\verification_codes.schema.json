{"bsonType": "object", "description": "验证码表", "required": ["code", "mobile", "type", "expires_at", "used", "created_at"], "properties": {"_id": {"description": "ID，系统自动生成"}, "code": {"bsonType": "string", "description": "验证码", "maxLength": 10}, "mobile": {"bsonType": "string", "description": "手机号", "pattern": "^1[3-9]\\d{9}$"}, "type": {"bsonType": "string", "description": "验证码类型", "enum": ["register", "login", "reset_password"]}, "expires_at": {"bsonType": "date", "description": "过期时间"}, "used": {"bsonType": "bool", "description": "是否已使用"}, "created_at": {"bsonType": "date", "description": "创建时间"}}}