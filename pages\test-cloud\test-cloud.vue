<template>
	<view class="test-page">
		<view class="header">
			<text class="title">云函数测试页面</text>
		</view>
		
		<view class="test-section">
			<button @click="initDatabase" class="test-btn init-btn">初始化数据库</button>
			<button @click="testDatabase" class="test-btn">测试数据库连接</button>
			<button @click="testAuth" class="test-btn">测试认证云函数</button>
			<button @click="testCheckUser" class="test-btn">测试用户检测</button>
		</view>
		
		<view class="result-section">
			<text class="result-title">测试结果：</text>
			<text class="result-text">{{ testResult }}</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			testResult: '等待测试...'
		}
	},
	
	methods: {
		async initDatabase() {
			this.testResult = '正在初始化数据库...'
			try {
				const result = await uniCloud.callFunction({
					name: 'init-db',
					data: {}
				})

				console.log('数据库初始化结果:', result)
				this.testResult = '数据库初始化结果:\n' + JSON.stringify(result, null, 2)

				if (result.result.code === 0) {
					uni.showToast({
						title: '数据库初始化成功',
						icon: 'success'
					})
				}
			} catch (error) {
				console.error('数据库初始化失败:', error)
				this.testResult = '数据库初始化失败: ' + error.message
				uni.showToast({
					title: '初始化失败',
					icon: 'none'
				})
			}
		},

		async testDatabase() {
			this.testResult = '测试中...'
			try {
				const result = await uniCloud.callFunction({
					name: 'test-db',
					data: {}
				})
				
				console.log('数据库测试结果:', result)
				this.testResult = JSON.stringify(result, null, 2)
			} catch (error) {
				console.error('数据库测试失败:', error)
				this.testResult = '数据库测试失败: ' + error.message
			}
		},
		
		async testAuth() {
			this.testResult = '测试中...'
			try {
				const result = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'checkUserExists',
						mobile: '13800138000'
					}
				})
				
				console.log('认证云函数测试结果:', result)
				this.testResult = JSON.stringify(result, null, 2)
			} catch (error) {
				console.error('认证云函数测试失败:', error)
				this.testResult = '认证云函数测试失败: ' + error.message
			}
		},
		
		async testCheckUser() {
			this.testResult = '测试中...'
			try {
				const result = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'checkUserExists',
						mobile: '13912345678'
					}
				})
				
				console.log('用户检测测试结果:', result)
				this.testResult = JSON.stringify(result, null, 2)
			} catch (error) {
				console.error('用户检测测试失败:', error)
				this.testResult = '用户检测测试失败: ' + error.message
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.test-page {
	padding: 40rpx;
	min-height: 100vh;
	background: #f5f5f5;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.test-section {
	margin-bottom: 60rpx;
}

.test-btn {
	width: 100%;
	height: 88rpx;
	background: #007AFF;
	color: white;
	border: none;
	border-radius: 16rpx;
	font-size: 32rpx;
	margin-bottom: 20rpx;
}

.test-btn:active {
	background: #0056CC;
}

.init-btn {
	background: #ff6b35 !important;
	font-weight: bold;
}

.init-btn:active {
	background: #e55a2b !important;
}

.result-section {
	background: white;
	padding: 40rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.result-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.result-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.6;
	word-break: break-all;
	white-space: pre-wrap;
}
</style>
