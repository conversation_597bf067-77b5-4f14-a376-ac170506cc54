<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲伴 - 黑金风格VIP会员中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 100%);
            min-height: 100vh;
            color: #fff;
        }
        
        .vip-page {
            background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 100%);
            min-height: 100vh;
            color: #fff;
        }
        
        .top-header {
            position: relative;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
        }
        
        .status-bar {
            height: 44px;
            background: transparent;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
        }
        
        .back-btn, .help-btn {
            width: 22px;
            height: 22px;
            border-radius: 11px;
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #FFD700;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .back-btn:active, .help-btn:active {
            background: rgba(255, 215, 0, 0.2);
            transform: scale(0.95);
        }
        
        .nav-title {
            font-size: 18px;
            font-weight: 700;
            color: #FFD700;
            text-shadow: 0 1px 4px rgba(255, 215, 0, 0.3);
        }
        
        .member-status-section {
            padding: 12px;
        }
        
        .status-card {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border: 1px solid rgba(255, 215, 0, 0.5);
            box-shadow: 0 6px 24px rgba(255, 215, 0, 0.2);
        }
        
        .card-decorations {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }
        
        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
        }
        
        .circle-1 {
            width: 100px;
            height: 100px;
            top: -50px;
            right: -25px;
            animation: float 8s ease-in-out infinite;
        }
        
        .circle-2 {
            width: 60px;
            height: 60px;
            bottom: -30px;
            left: -15px;
            animation: float 10s ease-in-out infinite reverse;
        }
        
        .circle-3 {
            width: 40px;
            height: 40px;
            top: 50%;
            left: 20%;
            animation: float 6s ease-in-out infinite;
        }
        
        .gold-particles {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
        
        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #FFD700;
            border-radius: 50%;
            animation: sparkle 3s ease-in-out infinite;
        }
        
        .particle:nth-child(1) { top: 20%; left: 10%; }
        .particle:nth-child(2) { top: 30%; right: 15%; }
        .particle:nth-child(3) { top: 60%; left: 20%; }
        .particle:nth-child(4) { bottom: 30%; right: 25%; }
        .particle:nth-child(5) { top: 40%; left: 60%; }
        .particle:nth-child(6) { bottom: 20%; left: 70%; }
        .particle:nth-child(7) { top: 70%; right: 30%; }
        .particle:nth-child(8) { bottom: 40%; left: 40%; }
        
        .card-content {
            position: relative;
            z-index: 2;
            padding: 16px;
        }
        
        .member-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .member-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid #FFD700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
            object-fit: cover;
        }
        
        .crown-badge {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 16px;
            height: 16px;
            background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 6px rgba(255, 215, 0, 0.4);
            font-size: 10px;
        }
        
        .member-info {
            flex: 1;
        }
        
        .member-name {
            font-size: 18px;
            font-weight: 700;
            color: #fff;
            margin-bottom: 6px;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
        }
        
        .member-status {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .status-text {
            font-size: 13px;
            color: #FFD700;
            font-weight: 600;
        }
        
        .vip-icon {
            background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
            border-radius: 4px;
            padding: 2px 4px;
        }
        
        .vip-text {
            font-size: 9px;
            color: #000;
            font-weight: 700;
        }
        
        .member-details {
            background: rgba(255, 215, 0, 0.1);
            border-radius: 8px;
            padding: 12px;
            margin-top: 16px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .detail-row:last-child {
            margin-bottom: 0;
        }
        
        .detail-item {
            flex: 1;
            text-align: center;
        }
        
        .detail-label {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
            display: block;
            margin-bottom: 4px;
        }
        
        .detail-value {
            font-size: 14px;
            color: #fff;
            font-weight: 600;
        }
        
        .detail-value.highlight {
            color: #FFD700;
        }
        
        .function-management {
            padding: 12px;
        }
        
        .management-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }
        
        .management-item {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .management-item:hover {
            border-color: rgba(255, 215, 0, 0.5);
            transform: translateY(-2px);
        }
        
        .item-icon {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: rgba(255, 215, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            color: #FFD700;
        }
        
        .item-title {
            font-size: 14px;
            color: #fff;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .item-desc {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }
        
        @keyframes sparkle {
            0%, 100% {
                opacity: 0;
                transform: scale(0);
            }
            50% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .demo-info {
            padding: 20px;
            text-align: center;
            background: rgba(255, 215, 0, 0.1);
            margin: 20px;
            border-radius: 12px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }
        
        .demo-title {
            font-size: 20px;
            color: #FFD700;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .demo-desc {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="vip-page">
        <div class="top-header">
            <div class="status-bar"></div>
            <div class="nav-content">
                <div class="back-btn">‹</div>
                <div class="nav-title">会员中心</div>
                <div class="help-btn">?</div>
            </div>
        </div>

        <div class="member-status-section">
            <div class="status-card premium">
                <div class="card-decorations">
                    <div class="decoration-circle circle-1"></div>
                    <div class="decoration-circle circle-2"></div>
                    <div class="decoration-circle circle-3"></div>
                    <div class="gold-particles">
                        <div class="particle" style="animation-delay: 0s;"></div>
                        <div class="particle" style="animation-delay: 0.3s;"></div>
                        <div class="particle" style="animation-delay: 0.6s;"></div>
                        <div class="particle" style="animation-delay: 0.9s;"></div>
                        <div class="particle" style="animation-delay: 1.2s;"></div>
                        <div class="particle" style="animation-delay: 1.5s;"></div>
                        <div class="particle" style="animation-delay: 1.8s;"></div>
                        <div class="particle" style="animation-delay: 2.1s;"></div>
                    </div>
                </div>
                
                <div class="card-content">
                    <div class="member-header">
                        <div class="avatar-section" style="position: relative;">
                            <img class="member-avatar" src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="用户头像">
                            <div class="crown-badge">⭐</div>
                        </div>
                        <div class="member-info">
                            <div class="member-name">闲伴用户</div>
                            <div class="member-status">
                                <span class="status-text">尊享会员 VIP2</span>
                                <div class="vip-icon">
                                    <span class="vip-text">VIP</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="member-details">
                        <div class="detail-row">
                            <div class="detail-item">
                                <span class="detail-label">会员等级</span>
                                <span class="detail-value">VIP2</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">到期时间</span>
                                <span class="detail-value">2025-03-15</span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-item">
                                <span class="detail-label">剩余天数</span>
                                <span class="detail-value highlight">79天</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">累计消费</span>
                                <span class="detail-value">¥168</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="function-management">
            <div class="management-grid">
                <div class="management-item">
                    <div class="item-icon">⭐</div>
                    <div class="item-title">权益中心</div>
                    <div class="item-desc">查看专属权益</div>
                </div>
                <div class="management-item">
                    <div class="item-icon">🔄</div>
                    <div class="item-title">续费规则</div>
                    <div class="item-desc">了解续费政策</div>
                </div>
                <div class="management-item">
                    <div class="item-icon">📋</div>
                    <div class="item-title">消费记录</div>
                    <div class="item-desc">查看账单明细</div>
                </div>
                <div class="management-item">
                    <div class="item-icon">❓</div>
                    <div class="item-title">会员说明</div>
                    <div class="item-desc">使用指南帮助</div>
                </div>
            </div>
        </div>

        <div class="demo-info">
            <div class="demo-title">🎨 黑金风格VIP会员中心</div>
            <div class="demo-desc">
                • 大厂级别的黑金配色设计<br>
                • 动态粒子效果和浮动装饰<br>
                • 完整的会员信息展示<br>
                • 实用的功能管理模块<br>
                • 现代化的交互体验
            </div>
        </div>
    </div>
</body>
</html>
