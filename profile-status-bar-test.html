<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>个人资料页面状态栏测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
        }
        
        .profile-container {
            min-height: 100vh;
            background: #F8F9FA;
        }
        
        .profile-header {
            background-image: url('/static/images/backgrounds/profile-bg.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
            padding-top: env(safe-area-inset-top);
            padding-bottom: 40px;
        }
        
        .ip-location {
            position: absolute;
            top: calc(env(safe-area-inset-top) + 10px);
            left: 15px;
            z-index: 10;
        }
        
        .ip-text {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
            background: rgba(0, 0, 0, 0.3);
            padding: 4px 8px;
            border-radius: 10px;
        }
        
        .more-btn-header {
            position: absolute;
            top: calc(env(safe-area-inset-top) + 10px);
            right: 15px;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            z-index: 3;
        }
        
        .profile-content {
            display: flex;
            align-items: flex-end;
            padding: 60px 20px 20px;
            gap: 20px;
        }
        
        .avatar-section {
            flex-shrink: 0;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .user-info-section {
            flex: 1;
            color: white;
        }
        
        .user-name-large {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .user-id-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .user-id-text {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .copy-id-btn {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
        }
        
        .user-badges {
            display: flex;
            gap: 12px;
            flex-wrap: nowrap;
            align-items: center;
            margin-top: 8px;
        }
        
        .badge-item {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 6px 10px;
            background: rgba(255, 255, 255, 0.25);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            flex-shrink: 0;
            margin-right: 4px;
        }
        
        .badge-text {
            font-size: 12px;
            color: white;
            font-weight: 500;
        }
        
        .icon-16 {
            width: 16px;
            height: 16px;
        }
        
        .icon-22 {
            width: 22px;
            height: 22px;
        }
        
        .test-info {
            background: white;
            margin: 20px;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .test-item {
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }
        
        .status-success {
            color: #4CAF50;
            font-weight: 500;
        }
        
        .status-warning {
            color: #FF9800;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="profile-container">
        <div class="profile-header">
            <!-- IP属地显示 -->
            <div class="ip-location">
                <span class="ip-text">🌐 IP属地：上海</span>
            </div>
            
            <!-- 更多功能按钮 -->
            <div class="more-btn-header">
                <img src="/static/icons/more-filled.png" class="icon-22" style="filter: brightness(0) invert(1);">
            </div>
            
            <!-- 个人信息内容 -->
            <div class="profile-content">
                <div class="avatar-section">
                    <img src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" 
                         class="user-avatar" alt="用户头像">
                </div>
                <div class="user-info-section">
                    <div class="user-name-large">闲伴用户</div>
                    <div class="user-id-container">
                        <span class="user-id-text">乐鱼ID：8264751</span>
                        <div class="copy-id-btn">
                            <img src="/static/icons/paperclip.png" class="icon-12" style="filter: brightness(0) invert(1);">
                        </div>
                    </div>
                    <div class="user-badges">
                        <div class="badge-item">
                            <img src="/static/icons/person-filled.png" class="icon-16" style="filter: brightness(0) invert(1);">
                            <span class="badge-text">实名认证</span>
                        </div>
                        <div class="badge-item">
                            <img src="/static/icons/vip-filled.png" class="icon-16" style="filter: brightness(0) invert(1);">
                            <span class="badge-text">会员</span>
                        </div>
                        <div class="badge-item">
                            <img src="/static/icons/medal-filled.png" class="icon-16" style="filter: brightness(0) invert(1);">
                            <span class="badge-text">Lv.5</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-info">
            <div class="test-title">状态栏适配测试结果</div>
            <div class="test-item">✅ <span class="status-success">背景图延伸到顶部</span> - 移除了状态栏占位</div>
            <div class="test-item">✅ <span class="status-success">IP属地位置调整</span> - 使用 calc(env(safe-area-inset-top) + 20rpx)</div>
            <div class="test-item">✅ <span class="status-success">更多按钮位置调整</span> - 使用 calc(env(safe-area-inset-top) + 20rpx)</div>
            <div class="test-item">✅ <span class="status-success">徽章间距增加</span> - gap: 24rpx + margin-right: 8rpx</div>
            <div class="test-item">✅ <span class="status-success">徽章内边距增加</span> - padding: 12rpx 20rpx</div>
            <div class="test-item">✅ <span class="status-success">页面配置正确</span> - navigationStyle: "custom"</div>
        </div>
    </div>
</body>
</html>
