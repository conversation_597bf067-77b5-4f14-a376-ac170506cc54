# 部署指南

## 快速部署步骤

### 1. 环境准备
确保您已经安装并配置了以下环境：
- HBuilderX 最新版本
- DCloud 开发者账号
- uniCloud 服务空间

### 2. 项目配置检查

#### 检查 manifest.json 配置
确认以下配置正确：
```json
{
  "uniCloud": {
    "provider": "aliyun",
    "spaceId": "mp-4f58ec35-fd98-402d-99e4-2bb423eaf604",
    "clientSecret": "pZBK1la3eLXxOMrhsDatXg=="
  }
}
```

#### 检查网络权限
确认 Android 权限配置包含：
```json
"permissions": [
  "<uses-permission android:name=\"android.permission.INTERNET\"/>",
  "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
  "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
  "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
  "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
  "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>"
]
```

### 3. 云函数部署

#### 方法一：批量部署（推荐）
1. 在 HBuilderX 中右键 `uniCloud-aliyun/cloudfunctions` 目录
2. 选择 "上传所有云函数"
3. 等待部署完成（可能需要几分钟）

#### 方法二：单独部署
依次部署以下云函数：
1. 右键 `uniCloud-aliyun/cloudfunctions/simple-test`，选择 "上传部署"
2. 右键 `uniCloud-aliyun/cloudfunctions/auth`，选择 "上传部署"
3. 右键 `uniCloud-aliyun/cloudfunctions/test-db`，选择 "上传部署"
4. 右键 `uniCloud-aliyun/cloudfunctions/user-center`，选择 "上传部署"

### 4. 数据库初始化

#### 初始化数据库
1. 右键 `uniCloud-aliyun/database` 目录
2. 选择 "初始化云数据库"
3. 等待初始化完成

#### 验证数据库表
确认以下数据表已创建：
- `uni-id-users` - 用户表
- `verification_codes` - 验证码表
- `customer_service_sessions` - 客服会话表
- `messages` - 消息表
- `admins` - 管理员表

### 5. 部署验证

#### 使用应用内诊断工具
1. 编译并运行应用
2. 进入登录页面
3. 点击 "网络连接诊断" 按钮
4. 查看诊断结果

#### 预期诊断结果
正常情况下应该显示：
- ✓ 网络连接正常
- ✓ uniCloud 连接正常
- ✓ 云函数部署正常
- ✓ 数据库连接正常

### 6. 常见部署问题

#### 问题1：云函数上传失败
**症状**：提示 "上传失败" 或 "网络错误"
**解决方案**：
1. 检查网络连接
2. 确认 DCloud 账号登录状态
3. 重试上传

#### 问题2：数据库初始化失败
**症状**：提示 "初始化失败"
**解决方案**：
1. 检查服务空间状态
2. 确认账号权限
3. 重新初始化

#### 问题3：云函数调用失败
**症状**：应用中提示 "FUNCTION_NOT_FOUND"
**解决方案**：
1. 确认云函数已正确部署
2. 检查函数名称是否正确
3. 重新部署对应云函数

### 7. 测试流程

#### 基础功能测试
1. **网络诊断测试**
   - 点击 "网络连接诊断"
   - 确认所有项目显示正常

2. **用户注册测试**
   - 输入手机号
   - 获取验证码
   - 完成注册流程

3. **用户登录测试**
   - 使用已注册账号登录
   - 确认登录成功

#### 高级功能测试
1. **IP定位测试**
   - 检查用户IP地址获取
   - 验证地区信息显示

2. **数据同步测试**
   - 修改用户信息
   - 确认数据正确保存

### 8. 生产环境配置

#### 安全配置
1. 更换生产环境的 clientSecret
2. 配置访问域名白名单
3. 启用 API 访问控制

#### 性能优化
1. 配置 CDN 加速
2. 优化云函数执行效率
3. 设置合理的缓存策略

### 9. 监控和维护

#### 日志监控
1. 在 uniCloud 控制台查看云函数日志
2. 监控错误率和响应时间
3. 设置告警规则

#### 定期维护
1. 定期备份数据库
2. 更新云函数代码
3. 清理过期数据

### 10. 故障排除

#### 快速诊断命令
在应用中使用以下功能进行快速诊断：
- 网络连接诊断
- 自动修复网络
- 查看诊断历史

#### 联系支持
如果遇到无法解决的问题：
1. 收集完整的错误日志
2. 记录详细的操作步骤
3. 提供设备和环境信息
4. 联系技术支持团队

## 部署检查清单

- [ ] HBuilderX 环境配置完成
- [ ] DCloud 账号登录成功
- [ ] uniCloud 服务空间绑定
- [ ] manifest.json 配置正确
- [ ] 网络权限配置完整
- [ ] 所有云函数部署成功
- [ ] 数据库初始化完成
- [ ] 应用内诊断测试通过
- [ ] 基础功能测试正常
- [ ] 生产环境配置完成

完成以上所有步骤后，您的应用应该可以正常进行网络连接和用户认证操作。
