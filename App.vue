<template>
	<view>
		<!-- App根组件 -->
	</view>
</template>

<script>
	import { performStartupCheck, showStartupResult } from '@/utils/startupCheck.js'

	let firstBackTime = 0
	export default {
		onLaunch: function () {
			console.log('App Launch - 趣嗒同行启动')
			this.initApp()
		},
		onShow: function () {
			// 应用显示
		},
		onHide: function () {
			// 应用隐藏
		},
		// #ifdef APP-ANDROID
		onLastPageBackPress: function () {
			if (firstBackTime == 0) {
				uni.showToast({
					title: '再按一次退出应用',
					position: 'bottom',
				})
				firstBackTime = Date.now()
				setTimeout(() => {
					firstBackTime = 0
				}, 2000)
			} else if (Date.now() - firstBackTime < 2000) {
				firstBackTime = Date.now()
				uni.exit()
			}
		},
		// #endif
		onExit: function () {
			// 应用退出
		},

		methods: {
			// 初始化应用
			async initApp() {
				try {
					console.log('开始应用初始化...')

					// 执行启动检测
					const checkResult = await performStartupCheck()

					if (!checkResult.success) {
						console.warn('启动检测发现问题:', checkResult)
						// 延迟显示结果，避免与启动画面冲突
						setTimeout(() => {
							showStartupResult(checkResult)
						}, 2000)
					} else {
						console.log('应用启动检测通过')
					}

					// 保存启动检测结果
					uni.setStorageSync('lastStartupCheck', {
						...checkResult,
						timestamp: new Date().toISOString()
					})

				} catch (error) {
					console.error('应用初始化失败:', error)

					// 如果启动检测失败，显示错误信息
					setTimeout(() => {
						uni.showModal({
							title: '应用初始化失败',
							content: '应用启动时遇到问题，可能影响正常使用。\n\n建议：\n1. 检查网络连接\n2. 重启应用\n3. 清理应用缓存',
							showCancel: false,
							confirmText: '知道了'
						})
					}, 2000)
				}
			}
		}
	}
</script>

<style lang="scss">
	/* 引入全局样式 */
	@import '@/styles/global.scss';

	/*每个页面公共css */
	.uni-row {
		flex-direction: row;
	}

	.uni-column {
		flex-direction: column;
	}

	/* 图标样式 - 使用PNG图片 */
	.icon {
		display: inline-block;
		width: 20px;
		height: 20px;
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}

	.icon-large {
		width: 24px;
		height: 24px;
	}

	.icon-small {
		width: 16px;
		height: 16px;
	}
</style>