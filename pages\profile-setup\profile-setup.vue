<template>
	<view class="profile-setup-page">
		<!-- 返回按钮 -->
		<view class="back-button" :style="{ top: (statusBarHeight + 20) + 'px' }" @click="goBack">
			<image src="/static/fanhuilogo.png" class="back-icon" mode="aspectFit"></image>
		</view>

		<!-- 主要内容 -->
		<view class="setup-content">

			<!-- 头像上传 -->
			<view class="form-section avatar-section">
				<view class="avatar-upload" @click="chooseAvatar">
					<image
						v-if="userInfo.avatar"
						:src="userInfo.avatar"
						class="avatar-preview"
						mode="aspectFill"
						@error="onAvatarError"
					></image>
					<view v-else class="avatar-placeholder">
						<image src="/static/icons/camera.png" class="camera-icon" mode="aspectFit"></image>
						<text class="upload-text">点击上传头像</text>
					</view>
				</view>
			</view>

			<!-- 用户名输入 -->
			<view class="form-section">
				<text class="section-title">用户名 *</text>
				<input
					class="form-input"
					type="text"
					placeholder="请输入用户名"
					placeholder-style="color: #999999"
					v-model="userInfo.username"
					maxlength="20"
				/>
			</view>

			<!-- 邮箱输入 -->
			<view class="form-section">
				<text class="section-title">邮箱 *</text>
				<input
					class="form-input"
					type="text"
					placeholder="请输入邮箱地址"
					placeholder-style="color: #999999"
					v-model="userInfo.email"
					maxlength="50"
				/>
			</view>

			<!-- 密码输入 -->
			<view class="form-section">
				<text class="section-title">密码 *</text>
				<view class="password-container">
					<input
						class="form-input password-input"
						:type="showPassword ? 'text' : 'password'"
						placeholder="请输入6-20位密码"
						placeholder-style="color: #999999"
						v-model="userInfo.password"
						maxlength="20"
					/>
					<view class="password-toggle" @click="togglePassword">
						<image
							:src="showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'"
							class="eye-icon"
							mode="aspectFit"
						></image>
					</view>
				</view>
			</view>

			<!-- 性别选择 -->
			<view class="form-section">
				<text class="section-title">性别 *</text>
				<view class="gender-selector">
					<view
						class="gender-option"
						:class="{ active: userInfo.gender === 'male' }"
						@click="selectGender('male')"
					>
						<text class="gender-text">男</text>
					</view>
					<view
						class="gender-option"
						:class="{ active: userInfo.gender === 'female' }"
						@click="selectGender('female')"
					>
						<text class="gender-text">女</text>
					</view>
				</view>
			</view>

			<!-- 生日选择 -->
			<view class="form-section">
				<text class="section-title">生日 *</text>
				<picker mode="date" :value="userInfo.birthday" @change="onBirthdayChange">
					<view class="date-picker">
						<text class="date-text" :class="{ placeholder: !userInfo.birthday }">
							{{ userInfo.birthday || '请选择生日' }}
						</text>
						<image src="/static/icons/right.png" class="arrow-icon" mode="aspectFit"></image>
					</view>
				</picker>
			</view>

			<!-- 地区选择 -->
			<view class="form-section">
				<text class="section-title">所在地区 *</text>
				<view class="region-selector" @click="selectRegion">
					<text class="region-text" :class="{ placeholder: !userInfo.region }">
						{{ userInfo.region || '请选择所在地区' }}
					</text>
					<image src="/static/icons/right.png" class="arrow-icon" mode="aspectFit"></image>
				</view>
			</view>

			<!-- 个人简介 -->
			<view class="form-section">
				<text class="section-title">个人简介</text>
				<textarea
					class="bio-input"
					v-model="userInfo.description"
					placeholder="趣嗒同行，探索有趣的生活~"
					:maxlength="100"
					auto-height
				></textarea>
				<view class="char-count">{{ userInfo.description.length }}/100</view>
			</view>

			<!-- 完成按钮 -->
			<view class="submit-section">
				<view class="submit-btn" :class="{ disabled: !canSubmit }" @click="completeSetup">
					<text class="submit-text">完成注册</text>
				</view>
			</view>
		</view>
	</view>

	<!-- 退出确认弹窗 -->
	<view class="exit-confirm-overlay" v-if="showExitConfirm" @click="hideExitConfirm">
		<view class="exit-confirm-modal" @click.stop>
			<view class="modal-header">
				<!-- 不规则圆形装饰 -->
				<view class="header-decoration">
					<view class="circle circle-1"></view>
					<view class="circle circle-2"></view>
					<view class="circle circle-3"></view>
					<view class="circle circle-4"></view>
					<view class="circle circle-5"></view>
					<view class="circle circle-6"></view>
				</view>
				<text class="modal-title">确认退出</text>
				<view class="modal-close" @click="hideExitConfirm">
					<text class="close-text">×</text>
				</view>
			</view>
			<view class="modal-content">
				<text class="modal-message">资料尚未完善，确定要退出吗？</text>
				<text class="modal-submessage">退出后当前填写的信息将会丢失</text>
			</view>
			<view class="modal-actions">
				<view class="action-button cancel-button" @click="hideExitConfirm">
					<text class="button-text">继续完善</text>
				</view>
				<view class="action-button confirm-button" @click="confirmExit">
					<text class="button-text">确认退出</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0,
			tempAvatarPath: '', // 临时头像路径
			showPassword: false,
			showExitConfirm: false,
			mobile: '',
			code: '',
			userInfo: {
				username: '',
				email: '',
				password: '',
				avatar: '',
				gender: '',
				birthday: '',
				region: '',
				description: '趣嗒同行，探索有趣的生活~'
			}
		}
	},
	
	computed: {
		progressPercent() {
			let completedFields = 0
			const totalRequiredFields = 7 // 用户名、邮箱、密码、头像、性别、生日、地区（个人简介非必填）

			if (this.userInfo.username && this.userInfo.username.trim()) completedFields++
			if (this.userInfo.email && this.userInfo.email.trim()) completedFields++
			if (this.userInfo.password && this.userInfo.password.length >= 6) completedFields++
			if (this.userInfo.avatar) completedFields++
			if (this.userInfo.gender) completedFields++
			if (this.userInfo.birthday) completedFields++
			if (this.userInfo.region) completedFields++

			return Math.round((completedFields / totalRequiredFields) * 100)
		},
		
		canSubmit() {
			const emailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.userInfo.email);

			return this.userInfo.username && this.userInfo.username.trim().length >= 2 &&
				   this.userInfo.email && emailValid &&
				   this.userInfo.password && this.userInfo.password.length >= 6 &&
				   this.userInfo.avatar &&
				   this.userInfo.gender &&
				   this.userInfo.birthday &&
				   this.userInfo.region
			// 个人简介为非必填项，不影响提交
		}
	},

	onLoad(options) {
		// 获取从验证码页面传递的参数
		this.mobile = options.mobile || ''
		this.code = options.code || ''
		console.log('接收到的参数:', { mobile: this.mobile, code: this.code })
		this.getSystemInfo()

		// 获取传递的用户信息
		if (options.userInfo) {
			try {
				const userInfo = JSON.parse(decodeURIComponent(options.userInfo))
				this.userInfo = { ...this.userInfo, ...userInfo }

				// 如果有默认头像，清空它以便用户重新选择
				if (this.userInfo.avatar === '/static/default-avatar.png') {
					this.userInfo.avatar = ''
				}
			} catch (error) {
				console.error('解析用户信息失败:', error)
			}
		}
	},
	
	methods: {
		getSystemInfo() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight || 0
		},

		// 切换密码显示
		togglePassword() {
			this.showPassword = !this.showPassword
		},

		// 选择性别
		selectGender(gender) {
			this.userInfo.gender = gender
		},

		// 生日选择
		onBirthdayChange(e) {
			this.userInfo.birthday = e.detail.value
		},
		
		// 选择头像
		chooseAvatar() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					const tempPath = res.tempFilePaths[0];

					// 验证图片格式（不允许透明格式）
					const lowerPath = tempPath.toLowerCase();
					if (lowerPath.includes('.png') || lowerPath.includes('.gif')) {
						uni.showToast({
							title: '不支持透明格式图片，请选择JPG格式',
							icon: 'none',
							duration: 3000
						});
						return;
					}

					// 暂时显示本地图片，等保存时再上传
					this.userInfo.avatar = tempPath;
					this.tempAvatarPath = tempPath;

					uni.showToast({
						title: '头像选择成功！',
						icon: 'success',
						duration: 2000
					});
				},
				fail: (error) => {
					console.error('选择头像失败:', error)
					uni.showToast({
						title: '选择头像失败，请重试',
						icon: 'none'
					})
				}
			})
		},
		
		// 选择地区
		selectRegion() {
			uni.navigateTo({
				url: '/pages/city-select/city-select?from=profile-setup'
			})
		},
		
		// 完成设置
		async completeSetup() {
			console.log('点击完成注册按钮')
			if (!this.canSubmit) {
				console.log('资料未完善，无法提交')
				uni.showToast({
					title: '请完善所有信息',
					icon: 'none'
				})
				return
			}

			console.log('开始注册流程')

			try {
				uni.showLoading({
					title: '注册中...'
				})

				// 调用注册云函数
				const result = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'register',
						username: this.userInfo.username.trim(),
						email: this.userInfo.email.trim(),
						password: this.userInfo.password,
						mobile: this.mobile,
						code: this.code,
						avatar: this.userInfo.avatar,
						gender: this.userInfo.gender,
						birthday: this.userInfo.birthday,
						region: this.userInfo.region,
						bio: this.userInfo.description.trim()
					}
				})

				uni.hideLoading()

				console.log('注册结果:', result)

				if (result.result.code === 0) {
					const userInfo = result.result.data.userInfo
					const token = result.result.data.token

					console.log('🎉 注册成功，准备保存用户信息');
					console.log('📋 返回的用户信息:', userInfo);
					console.log('🔑 返回的token:', token);

					// 保存用户信息到本地
					uni.setStorageSync('userInfo', userInfo)
					uni.setStorageSync('isLoggedIn', true)
					uni.setStorageSync('token', token)

					// 验证保存是否成功
					const savedUserInfo = uni.getStorageSync('userInfo');
					console.log('✅ 验证保存的用户信息:', savedUserInfo);

					uni.showToast({
						title: '注册成功',
						icon: 'success'
					})

					// 注册成功后跳转到首页
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/index/index'
						})
					}, 1500)
				} else {
					uni.showToast({
						title: result.result.message || '注册失败',
						icon: 'none'
					})
				}
				
			} catch (error) {
				uni.hideLoading()
				console.error('保存失败:', error)
				uni.showToast({
					title: '保存失败，请重试',
					icon: 'none'
				})
			}
		},
		
		// 返回
		goBack() {
			this.showExitConfirm = true
		},

		// 隐藏退出确认弹窗
		hideExitConfirm() {
			this.showExitConfirm = false
		},

		// 确认退出
		confirmExit() {
			this.showExitConfirm = false
			uni.navigateBack()
		},

		// 头像加载失败处理
		onAvatarError(e) {
			console.log('头像加载失败，清空头像')
			this.userInfo.avatar = ''
			uni.showToast({
				title: '头像加载失败，请重新选择',
				icon: 'none'
			})
		}
	},
	
	// 监听页面显示，处理地区选择返回
	onShow() {
		console.log('完善资料页面显示')
		const selectedCity = uni.getStorageSync('selectedCity')
		if (selectedCity) {
			console.log('选择的城市:', selectedCity)
			this.userInfo.region = selectedCity
			uni.removeStorageSync('selectedCity') // 清除临时存储
			console.log('当前资料完成状态:', this.canSubmit)
		}
	}
}
</script>

<style lang="scss" scoped>
.profile-setup-page {
	min-height: 100vh;
	background-image: url('/static/ziliao.png');
	background-size: 100% auto;
	background-position: top center;
	background-repeat: no-repeat;
	position: relative;
	background-color: #f5f5f5;
}

/* 返回按钮 */
.back-button {
	position: fixed;
	left: 32rpx;
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
	z-index: 100;
}

.back-button:active {
	background: rgba(255, 255, 255, 0.3);
}

.back-icon {
	width: 32rpx;
	height: 32rpx;
}

.setup-content {
	padding: 800rpx 48rpx 120rpx;
	min-height: 100vh;
	position: relative;
	z-index: 1;
}



.form-section {
	margin-bottom: 64rpx;
}

/* 头像区域特殊样式 */
.avatar-section {
	margin-bottom: 80rpx;
	text-align: center;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 32rpx;
	display: block;
}

/* 表单输入框样式 */
.form-input {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 24rpx 32rpx;
	border: 2rpx solid #e0e0e0;
	font-size: 32rpx;
	color: #333333;
	width: 100%;
	box-sizing: border-box;
	height: 96rpx;
	line-height: 48rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.form-input::placeholder {
	color: #999999;
}

/* 密码容器 */
.password-container {
	position: relative;
}

.password-input {
	padding-right: 80rpx;
}

.password-toggle {
	position: absolute;
	right: 32rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.eye-icon {
	width: 36rpx;
	height: 36rpx;
	opacity: 0.6;
}

/* 性别选择器 */
.gender-selector {
	display: flex;
	gap: 32rpx;
}

.gender-option {
	flex: 1;
	background: #ffffff;
	border-radius: 24rpx;
	padding: 24rpx 32rpx;
	border: 2rpx solid #e0e0e0;
	text-align: center;
	transition: all 0.3s ease;
	min-height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.gender-option.active {
	background: #66D4C8;
	border-color: #66D4C8;
}

.gender-text {
	font-size: 32rpx;
	color: #333333;
}

.gender-option.active .gender-text {
	color: #ffffff;
}

/* 日期选择器 */
.date-picker {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 24rpx 32rpx;
	border: 2rpx solid #e0e0e0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	min-height: 48rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.date-text {
	font-size: 32rpx;
	color: #333333;
	flex: 1;
}

.date-text.placeholder {
	color: #999999;
}

.avatar-upload {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 200rpx;
	height: 200rpx;
	margin: 0 auto;
	border-radius: 50%;
	background: #ffffff;
	border: 4rpx solid #66D4C8;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(102, 212, 200, 0.2);
	transition: all 0.3s ease;
}

.avatar-upload:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(102, 212, 200, 0.3);
}

.avatar-preview {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.avatar-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
}

.camera-icon {
	width: 48rpx;
	height: 48rpx;
	opacity: 0.8;
}

.upload-text {
	font-size: 24rpx;
	color: #999999;
}

.region-selector {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 24rpx 32rpx;
	border: 2rpx solid #e0e0e0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	min-height: 48rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.region-text {
	font-size: 32rpx;
	color: #333333;
	flex: 1;
}

.region-text.placeholder {
	color: #999999;
}

.arrow-icon {
	width: 32rpx;
	height: 32rpx;
	opacity: 0.6;
}

.bio-input {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 24rpx 32rpx;
	border: 2rpx solid #e0e0e0;
	font-size: 32rpx;
	color: #333333;
	min-height: 120rpx;
	width: 100%;
	box-sizing: border-box;
	line-height: 1.5;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.bio-input::placeholder {
	color: #999999;
}

.char-count {
	text-align: right;
	font-size: 24rpx;
	color: #999999;
	margin-top: 16rpx;
}

.submit-section {
	margin-top: 80rpx;
}

.submit-btn {
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 100%);
	border-radius: 24rpx;
	height: 96rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 32rpx rgba(102, 212, 200, 0.3);
	transition: all 0.3s ease;
}

.submit-btn:active {
	transform: scale(0.98);
}

.submit-btn.disabled {
	opacity: 0.5;
	background: #cccccc;
	box-shadow: none;
}

.submit-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #ffffff;
}

/* 退出确认弹窗样式 */
.exit-confirm-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.exit-confirm-modal {
	width: 600rpx;
	background-color: #FFFFFF;
	border-radius: 40rpx;
	overflow: hidden;
	margin: 0 40rpx;
}

.modal-header {
	padding: 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: relative;
	height: 120rpx;
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 50%, #5DD5C9 100%);
	overflow: hidden;
}

.header-decoration {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.circle {
	position: absolute;
	border-radius: 50%;
	opacity: 0.6;
}

.circle-1 {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(45deg, #66D4C8, #5DD5C9);
	top: -20rpx;
	left: 50rpx;
	transform: rotate(15deg);
}

.circle-2 {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(45deg, #4ECDC4, #66D4C8);
	top: 20rpx;
	right: 80rpx;
	transform: rotate(-25deg);
}

.circle-3 {
	width: 100rpx;
	height: 100rpx;
	background: linear-gradient(45deg, #5DD5C9, #4ECDC4);
	top: -30rpx;
	right: -20rpx;
	transform: rotate(35deg);
}

.circle-4 {
	width: 70rpx;
	height: 70rpx;
	background: linear-gradient(45deg, #FFD700, #FFA500);
	bottom: -15rpx;
	left: 30%;
	transform: rotate(-15deg);
}

.circle-5 {
	width: 50rpx;
	height: 50rpx;
	background: linear-gradient(45deg, #FF6B9D, #F06292);
	top: 60rpx;
	left: 20rpx;
	transform: rotate(45deg);
}

.circle-6 {
	width: 90rpx;
	height: 90rpx;
	background: linear-gradient(45deg, #9C27B0, #E91E63);
	bottom: -25rpx;
	right: 40%;
	transform: rotate(25deg);
}

.modal-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #FFFFFF;
	z-index: 2;
	position: relative;
}

.modal-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2;
	position: relative;
}

.close-text {
	font-size: 48rpx;
	color: #FFFFFF;
	font-weight: bold;
}

.modal-content {
	padding: 60rpx 40rpx 40rpx;
	text-align: center;
}

.modal-message {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
	display: block;
	margin-bottom: 20rpx;
}

.modal-submessage {
	font-size: 28rpx;
	color: #999999;
	display: block;
}

.modal-actions {
	padding: 0 40rpx 40rpx;
	display: flex;
	gap: 20rpx;
}

.action-button {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.cancel-button {
	background: #F5F5F5;
	border: 2rpx solid #E0E0E0;
}

.cancel-button .button-text {
	color: #666666;
	font-size: 32rpx;
	font-weight: 500;
}

.confirm-button {
	background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
	box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
}

.confirm-button .button-text {
	color: #FFFFFF;
	font-size: 32rpx;
	font-weight: 500;
}

.action-button:active {
	transform: scale(0.98);
}
</style>
