<template>
	<view class="home-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<!-- 自定义下拉刷新 -->
		<view class="custom-refresh" v-if="refreshing">
			<view class="refresh-container">
				<view class="stars-animation">
					<view class="star-item star-1">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-2">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-3">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-4">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-5">
						<text class="star-text">✦</text>
					</view>
				</view>
				<text class="refresh-text">正在刷新...</text>
			</view>
		</view>

		<!-- 主内容滚动区域 -->
		<scroll-view class="main-scroll" scroll-y @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
			<!-- 顶部导航区 -->
			<view class="top-nav">
			<view class="location-area" @click="onLocationClick">
				<text class="city-text">{{ currentCity }}</text>
			</view>

			<view class="search-container" @click="goToSearch">
				<text class="search-placeholder">搜索活动、搭子、地点...</text>
			</view>

			<view class="avatar-container" @click="onAvatarClick">
				<image :src="currentAvatarSrc" class="avatar-img" @error="onAvatarError" @load="onAvatarLoad"></image>
			</view>
		</view>

		<!-- 轮播横幅 -->
		<view class="banner-section">
			<swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500" indicator-color="rgba(255,255,255,0.4)" indicator-active-color="#FFD700">
				<swiper-item v-for="(banner, index) in banners" :key="index">
					<view class="banner-item">
						<image :src="banner.image" class="banner-bg-image" mode="aspectFill"></image>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 年轻化滚动公告栏 -->
		<view class="notice-section" v-if="showNotice">
			<view class="notice-container">
				<view class="notice-icon">
					<text class="notice-emoji">📢</text>
				</view>
				<view class="notice-content">
					<view class="notice-scroll-wrapper">
						<text class="notice-text-scroll">{{ allNoticesText || '🎉 欢迎来到趣嗒同行！一起发现有趣的人和事 ✨ 安全交友，快乐社交 🌟 发现身边的精彩活动' }}</text>
					</view>
				</view>
				<view class="notice-close" @click="hideNotice">
					<text class="close-icon">×</text>
				</view>
			</view>
		</view>

		<!-- 快捷功能网格 -->
		<view class="quick-grid">
			<view class="grid-container">
				<!-- 左侧竖向长方形：组局约伴 -->
				<view class="grid-item vertical-item organize-card" @click="onQuickAction(quickActions[0])">
					<view class="card-layout">
						<!-- 图标区域 -->
						<view class="icon-area">
							<uni-icons type="home" size="48" color="rgba(255,255,255,0.9)"></uni-icons>
						</view>
						<!-- 文字区域 -->
						<view class="text-area">
							<text class="item-title">组局约伴</text>
							<text class="item-desc">发起活动</text>
						</view>
					</view>
				</view>

				<!-- 右侧横向正方形容器 -->
				<view class="horizontal-items-container">
					<!-- 游戏玩伴 -->
					<view class="grid-item horizontal-item game-card" @click="onQuickAction(quickActions[1])">
						<view class="horizontal-layout">
							<view class="text-section">
								<text class="item-title">游戏玩伴</text>
								<text class="item-desc">开黑陪玩</text>
							</view>
							<view class="icon-section">
								<uni-icons type="videocam" size="32" color="rgba(255,255,255,0.9)"></uni-icons>
							</view>
						</view>
					</view>

					<!-- 城市玩伴 -->
					<view class="grid-item horizontal-item city-card" @click="onQuickAction(quickActions[2])">
						<view class="horizontal-layout">
							<view class="text-section">
								<text class="item-title">城市玩伴</text>
								<text class="item-desc">同城交友</text>
							</view>
							<view class="icon-section">
								<uni-icons type="location" size="32" color="rgba(255,255,255,0.9)"></uni-icons>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 热门活动 -->
		<view class="hot-section">
			<view class="section-header">
				<view class="section-title-container">
					<uni-icons type="fire" size="20" color="#FF6B6B"></uni-icons>
					<text class="section-title">热门活动</text>
				</view>
				<view class="more-btn" @click="viewMore">
					<text class="more-text">更多</text>
					<text class="more-arrow">›</text>
				</view>
			</view>
			<scroll-view scroll-x class="hot-scroll" :show-scrollbar="false">
				<view class="hot-container">
					<view class="hot-card" v-for="activity in hotActivities" :key="activity.id" @click="viewActivity(activity)">
						<view class="card-image" :style="{ background: activity.gradient }">
							<uni-icons :type="activity.icon" size="36" color="#FFFFFF"></uni-icons>
							<view class="card-tag">{{ activity.tag }}</view>
						</view>
						<view class="card-info">
							<text class="card-title">{{ activity.title }}</text>
							<view class="card-meta">
								<view class="meta-item">
									<uni-icons type="location" size="12" color="#999"></uni-icons>
									<text class="meta-text">{{ activity.location }}</text>
								</view>
								<view class="meta-item">
									<uni-icons type="calendar" size="12" color="#999"></uni-icons>
									<text class="meta-text">{{ activity.time }}</text>
								</view>
							</view>
							<view class="card-footer">
								<view class="participants">
									<text class="participant-count">{{ activity.participants }}人参与</text>
								</view>
								<text class="card-price">{{ activity.price }}</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 推荐搭子 -->
		<view class="recommend-section">
			<view class="section-header">
				<view class="section-title-container">
					<uni-icons type="star" size="20" color="#FFD700"></uni-icons>
					<text class="section-title">推荐搭子</text>
				</view>
				<view class="more-btn" @click="viewMorePartners">
					<text class="more-text">更多</text>
					<text class="more-arrow">›</text>
				</view>
			</view>
			<view class="partner-grid">
				<view class="partner-card" v-for="partner in recommendPartners" :key="partner.id" @click="viewPartner(partner)">
					<view class="partner-avatar">
						<image :src="partner.avatar" class="partner-img"></image>
						<view class="online-dot" v-if="partner.online"></view>
					</view>
					<text class="partner-name">{{ partner.name }}</text>
					<text class="partner-tags">{{ partner.tags.join(' · ') }}</text>
					<view class="partner-stats">
						<view class="stat-item">
							<uni-icons type="star-filled" size="12" color="#FFD700"></uni-icons>
							<text class="stat-text">{{ partner.rating }}</text>
						</view>
						<view class="stat-item">
							<uni-icons type="checkmarkempty" size="12" color="#4ECDC4"></uni-icons>
							<text class="stat-text">{{ partner.matches }}次</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 横条广告 -->
		<view class="ad-section">
			<view class="ad-banner" :style="{ background: advertisement.background }" @click="onAdClick">
				<view class="ad-content">
					<view class="ad-icon">
						<uni-icons :type="advertisement.icon" size="24" color="#FFFFFF"></uni-icons>
					</view>
					<view class="ad-text">
						<text class="ad-title">{{ advertisement.title }}</text>
						<text class="ad-subtitle">{{ advertisement.subtitle }}</text>
					</view>
				</view>
				<view class="ad-button">
					<text class="button-text">{{ advertisement.buttonText }}</text>
					<uni-icons type="right" size="16" color="#333"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 附近组局 -->
		<view class="nearby-section">
			<view class="section-header">
				<text class="section-title">附近组局</text>
				<view class="more-btn" @click="viewMoreNearby">
					<text class="more-text">更多</text>
					<uni-icons type="right" size="16" color="#FFD700"></uni-icons>
				</view>
			</view>

			<view class="nearby-list">
				<view class="nearby-item" v-for="group in nearbyGroups" :key="group.id" @click="viewNearbyGroup(group)">
					<view class="item-left">
						<image class="organizer-avatar" :src="group.avatar" mode="aspectFill"></image>
						<view class="item-info">
							<text class="item-title">{{ group.title }}</text>
							<view class="item-meta">
								<text class="meta-category">{{ group.category }}</text>
								<text class="meta-distance">{{ group.distance }}</text>
								<text class="meta-time">{{ group.time }}</text>
							</view>
							<text class="organizer-name">组织者：{{ group.organizer }}</text>
						</view>
					</view>
					<view class="item-right">
						<view class="participants-info">
							<text class="participants-count">{{ group.participants }}/{{ group.maxParticipants }}</text>
							<text class="participants-label">人</text>
						</view>
						<view class="join-btn">
							<text class="join-text">加入</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 热门话题 -->
		<view class="topics-section">
			<view class="section-header">
				<text class="section-title">热门话题</text>
				<view class="more-btn" @click="viewMoreTopics">
					<text class="more-text">更多</text>
					<uni-icons type="right" size="16" color="#FFD700"></uni-icons>
				</view>
			</view>

			<view class="topics-list">
				<view class="topic-item" v-for="topic in hotTopics" :key="topic.id" @click="viewTopic(topic)">
					<view class="topic-header">
						<view class="topic-category" :class="{ hot: topic.isHot }">
							{{ topic.category }}
						</view>
						<view class="hot-tag" v-if="topic.isHot">
							<uni-icons type="fire" size="14" color="#FF6B6B"></uni-icons>
						</view>
					</view>
					<text class="topic-title">{{ topic.title }}</text>
					<view class="topic-footer">
						<view class="topic-meta">
							<text class="author">{{ topic.author }}</text>
							<text class="time">{{ topic.time }}</text>
						</view>
						<view class="topic-stats">
							<view class="stat-item">
								<uni-icons type="chat" size="14" color="#999"></uni-icons>
								<text class="stat-text">{{ topic.replies }}</text>
							</view>
							<view class="stat-item">
								<uni-icons type="heart" size="14" color="#999"></uni-icons>
								<text class="stat-text">{{ topic.likes }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

			<!-- 底部安全距离 -->
			<view class="safe-bottom"></view>
		</scroll-view>

		<!-- 弹窗保持不变 -->
		<view class="device-info-popup-overlay" v-if="showDeviceInfoPopup" @click="closeDeviceInfoPopup">
			<view class="device-info-popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">设备信息与权限</text>
					<text class="close-btn" @click="closeDeviceInfoPopup">✕</text>
				</view>
				<view class="popup-body">
					<view class="info-item">
						<text class="label">设备型号:</text>
						<text class="value">未知</text>
					</view>
					<view class="info-item">
						<text class="label">操作系统:</text>
						<text class="value">未知</text>
					</view>
					<view class="permission-section">
						<text class="permission-title">定位权限</text>
						<switch :checked="locationPermissionGranted" @change="toggleLocationPermission" color="#FFD700"></switch>
					</view>
				</view>
				<view class="popup-footer">
					<button class="confirm-button" @click="closeDeviceInfoPopup">确认</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'IndexPage',
		data() {
			return {
				currentCity: '北海',
				statusBarHeight: 0,
				searchKeyword: '',
				refreshing: false,
				touchStartY: 0,
				scrollTop: 0,
				locationPermissionGranted: false,
				showDeviceInfoPopup: false,
				allNoticesText: '',
				showNotice: true, // 控制公告栏显示

				// 用户头像相关
				currentAvatarSrc: '/static/default-avatar.png',
				isLoggedIn: false,
				userInfo: {},

				// 轮播横幅数据
				banners: [
					{
						image: '/static/images/banners/banner1.jpg'
					},
					{
						image: '/static/images/banners/banner2.jpg'
					},
					{
						image: '/static/images/banners/banner3.jpg'
					},
					{
						image: '/static/images/banners/banner4.jpg'
					}
				],

				// 年轻化滚动公告数据
				notices: [
					'🎉 欢迎来到趣嗒同行！一起发现有趣的人和事',
					'✨ 安全交友，快乐社交，遇见更好的自己',
					'🌟 发现身边的精彩活动，找到志同道合的搭子',
					'🎈 年轻就要敢于尝试，勇敢表达真实的自己',
					'🌈 每一次相遇都是缘分，珍惜每一个美好瞬间'
				],

				// 快捷功能数据 - 三大功能
				quickActions: [
					{
						id: 1,
						title: '组局约伴',
						desc: '发起活动，寻找伙伴',
						subCategories: ['露营', 'BBQ', '钓鱼', '徒步', '聚餐', '桌游']
					},
					{
						id: 2,
						title: '游戏玩伴',
						desc: '游戏开黑，陪玩服务',
						subCategories: ['王者荣耀', '和平精英', 'LOL', '原神', '陪玩服务']
					},
					{
						id: 3,
						title: '城市玩伴',
						desc: '同城交友，探索城市',
						subCategories: ['看电影', '逛街', '展览', '音乐会', '咖啡厅', '夜市']
					}
				],

				// 热门活动数据
				hotActivities: [
					{
						id: 1,
						title: '周末海边BBQ',
						location: '金沙滩',
						time: '周六 14:00',
						participants: 12,
						price: '￥68',
						tag: '热门',
						icon: 'navigate',
						gradient: 'linear-gradient(135deg, #FFD700 0%, #FFC107 100%)'
					},
					{
						id: 2,
						title: '王者荣耀五排',
						location: '线上',
						time: '今晚 20:00',
						participants: 4,
						price: '免费',
						tag: '火爆',
						icon: 'medal',
						gradient: 'linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%)'
					}
				],

				// 推荐搭子数据
				recommendPartners: [
					{
						id: 1,
						name: '小雨',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						tags: ['摄影', '旅行', '美食'],
						rating: 4.9,
						matches: 23,
						online: true
					},
					{
						id: 2,
						name: '阿杰',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						tags: ['游戏', '电竞', '音乐'],
						rating: 4.8,
						matches: 31,
						online: false
					}
				],

				// 广告数据
				advertisement: {
					id: 1,
					title: '新用户专享福利',
					subtitle: '注册即送100积分，邀请好友再送50积分',
					buttonText: '立即领取',
					background: 'linear-gradient(135deg, #FF8A80 0%, #FFAB91 100%)',
					icon: 'gift'
				},

				// 附近组局数据
				nearbyGroups: [
					{
						id: 1,
						title: '今晚王者荣耀五排',
						category: '游戏',
						distance: '500m',
						participants: 3,
						maxParticipants: 5,
						time: '20:00',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						organizer: '游戏达人'
					},
					{
						id: 2,
						title: '周末朝阳公园跑步',
						category: '运动',
						distance: '1.2km',
						participants: 6,
						maxParticipants: 10,
						time: '明天 07:00',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						organizer: '跑步小队长'
					},
					{
						id: 3,
						title: '三里屯酒吧聚会',
						category: '社交',
						distance: '2.1km',
						participants: 4,
						maxParticipants: 8,
						time: '今晚 21:30',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						organizer: '夜生活达人'
					}
				],

				// 热门话题数据
				hotTopics: [
					{
						id: 1,
						title: '北京最适合约会的咖啡厅推荐',
						replies: 128,
						likes: 256,
						category: '美食',
						isHot: true,
						author: '咖啡控小姐姐',
						time: '2小时前'
					},
					{
						id: 2,
						title: '周末户外BBQ需要准备什么？',
						replies: 89,
						likes: 167,
						category: '户外',
						isHot: false,
						author: 'BBQ专家',
						time: '5小时前'
					},
					{
						id: 3,
						title: '新手健身房训练计划分享',
						replies: 203,
						likes: 445,
						category: '运动',
						isHot: true,
						author: '健身教练Leo',
						time: '1天前'
					}
				]
			}
		},

		onLoad() {
			// 获取系统信息，计算状态栏高度
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.statusBarHeight);
				}
			});

			// 延迟初始化滚动公告，确保页面渲染完成
			setTimeout(() => {
				this.initNoticeScroll(); // 初始化滚动公告
			}, 1000);

			console.log('首页加载完成');
		},

		onShow() {
			const selectedCity = uni.getStorageSync('selectedCity');
			if (selectedCity) {
				this.currentCity = selectedCity;
			}
			console.log('首页显示，当前城市:', this.currentCity);

			// 初始化用户头像
			this.initUserAvatar();
		},

		methods: {
			// 触摸开始
			onTouchStart(e) {
				this.touchStartY = e.touches[0].clientY;
				this.scrollTop = 0;
			},

			// 触摸移动
			onTouchMove(e) {
				const currentY = e.touches[0].clientY;
				const deltaY = currentY - this.touchStartY;

				// 只有在页面顶部且向下拉时才触发刷新
				if (this.scrollTop <= 0 && deltaY > 0) {
					if (deltaY > 100 && !this.refreshing) {
						this.triggerRefresh();
					}
				}
			},

			// 触摸结束
			onTouchEnd() {
				this.touchStartY = 0;
			},

			// 触发刷新
			triggerRefresh() {
				this.refreshing = true;
				// 模拟刷新数据
				setTimeout(() => {
					this.refreshing = false;
					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					});
				}, 2000);
			},

			initKingkongSubCategories() {
				// 这个方法暂时不需要，因为我们使用的是quickActions
				console.log('初始化快捷功能分类');
			},

			requestLocationPermission() {
				uni.showModal({
					title: '定位权限申请',
					content: '为了为您推荐附近的玩伴，需要获取您的位置信息',
					confirmText: '同意',
					cancelText: '拒绝',
					success: (res) => {
						if (res.confirm) {
							this.getCurrentLocation();
						} else {
							this.currentCity = '北京'; // 默认城市
							uni.showToast({
								title: '已设置默认城市：北京',
								icon: 'none',
								duration: 2000
							});
						}
					}
				});
			},

			getCurrentLocation() {
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						this.getCityByCoordinates(res.latitude, res.longitude);
					},
					fail: (err) => {
						console.log('定位失败：', err);
						this.currentCity = '北京'; // 定位失败时的默认城市
						uni.showToast({
							title: '定位失败，已设置默认城市',
							icon: 'none',
							duration: 3000
						});
					}
				});
			},

			getCityByCoordinates(latitude, longitude) {
				const cities = ['北京', '上海', '广州', '深圳', '杭州', '成都'];
				const randomCity = cities[Math.floor(Math.random() * cities.length)];

				this.currentCity = randomCity;
				this.locationPermissionGranted = true;

				uni.showToast({
					title: `定位成功：${randomCity}`,
					icon: 'success',
					duration: 3000
				});
			},

			onPullDownRefresh() {
				this.refreshing = true;
				setTimeout(() => {
					this.refreshing = false;
					uni.stopPullDownRefresh();
					uni.showToast({
						title: '刷新成功',
						icon: 'success'
					});
				}, 2000);
			},

			onLocationClick() {
				uni.navigateTo({
					url: '/pages/city-select/city-select'
				});
			},

			showCityPicker() {
				const cities = ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安'];
				uni.showActionSheet({
					itemList: cities,
					success: (res) => {
						this.currentCity = cities[res.tapIndex];
						uni.showToast({
							title: `已切换到${this.currentCity}`,
							icon: 'success'
						});
					}
				});
			},

			onSearch() {
				if (!this.searchKeyword.trim()) {
					uni.showToast({
						title: '请输入搜索内容',
						icon: 'none'
					});
				}
			},
			onAvatarClick() {
				this.showDeviceInfoPopup = true; // Show the popup when avatar is clicked
			},
			closeDeviceInfoPopup() {
				this.showDeviceInfoPopup = false;
			},
			toggleLocationPermission(e) {
				this.locationPermissionGranted = e.detail.value;
				if (this.locationPermissionGranted) {
					this.getCurrentLocation();
				} else {
					uni.showToast({
						title: '定位权限已关闭',
						icon: 'none',
						duration: 2000
					});
				}
			},
			onKingkongItemClick(item) {
				console.log('点击快捷功能:', item.title);
				// 这个方法暂时不需要，因为我们使用onQuickAction
			},
			switchCategory(categoryId) {
				console.log('切换到分类:', categoryId);
				// 这个方法暂时不需要
			},

			// 新增方法
			goToSearch() {
				console.log('跳转到搜索页面');
				uni.navigateTo({
					url: '/pages/search/search'
				});
			},

			// 重置页面样式
			resetPageStyles() {
				// 在uni-app中不需要直接操作DOM，样式通过CSS控制
				console.log('重置页面样式');
			},
			onQuickAction(item) {
				console.log('点击快捷功能:', item.title);
				// 跳转到分类页面，传递分类信息
				uni.navigateTo({
					url: `/pages/category/category?id=${item.id}&title=${item.title}&subCategories=${JSON.stringify(item.subCategories)}`
				});
			},
			viewMore() {
				console.log('查看更多热门活动');
				// 跳转到活动列表页
			},
			viewActivity(activity) {
				console.log('查看活动详情:', activity.title);
				// 跳转到活动详情页
			},
			viewMorePartners() {
				console.log('查看更多推荐搭子');
				// 跳转到搭子列表页
			},
			viewPartner(partner) {
				console.log('查看搭子详情:', partner.name);
				// 跳转到搭子详情页
			},

			// 新增模块方法
			onAdClick() {
				console.log('点击广告');
				// 处理广告点击
			},

			viewMoreNearby() {
				console.log('查看更多附近组局');
				// 跳转到附近组局列表页
			},

			viewNearbyGroup(group) {
				console.log('查看组局详情:', group.title);
				// 跳转到组局详情页
			},

			viewMoreTopics() {
				console.log('查看更多热门话题');
				// 跳转到话题列表页
			},

			viewTopic(topic) {
				console.log('查看话题详情:', topic.title);
				// 跳转到话题详情页
			},

			// 初始化用户头像
			initUserAvatar() {
				console.log('🖼️ 首页初始化用户头像');

				// 获取存储的用户信息
				const userInfo = uni.getStorageSync('userInfo');
				const token = uni.getStorageSync('token');
				const isLoggedIn = uni.getStorageSync('isLoggedIn');

				if (userInfo && token && isLoggedIn) {
					this.isLoggedIn = true;
					this.userInfo = { ...userInfo };

					// 设置头像 - 确保路径正确
					if (userInfo.avatar && userInfo.avatar !== '' && !userInfo.avatar.includes('default-avatar')) {
						this.currentAvatarSrc = userInfo.avatar;
						console.log('🖼️ 首页使用用户头像:', this.currentAvatarSrc);
					} else {
						this.currentAvatarSrc = '/static/default-avatar.png';
						console.log('🖼️ 首页使用默认头像:', this.currentAvatarSrc);
					}
				} else {
					this.isLoggedIn = false;
					this.userInfo = {};
					this.currentAvatarSrc = '/static/default-avatar.png';
					console.log('🖼️ 首页未登录，使用默认头像');
				}
			},

			// 头像加载错误处理
			onAvatarError(e) {
				console.log('🖼️ 首页头像加载失败:', this.currentAvatarSrc);
				this.currentAvatarSrc = '/static/default-avatar.png';
			},

			// 头像加载成功处理
			onAvatarLoad(e) {
				console.log('🖼️ 首页头像加载成功:', this.currentAvatarSrc);
			},

			// 隐藏公告栏
			hideNotice() {
				this.showNotice = false;
			},

			// 滚动公告相关方法
			initNoticeScroll() {
				console.log('初始化滚动公告, notices长度:', this.notices.length);
				if (this.notices.length > 0) {
					// 将所有公告合并成一条长文本，用分隔符连接
					this.allNoticesText = this.notices.join('   ✨   ');
					console.log('合并公告文本长度:', this.allNoticesText.length);
					console.log('合并公告内容预览:', this.allNoticesText.substring(0, 100) + '...');
				}
			}
		},

		// 页面隐藏时清除定时器
		onHide() {
			if (this.noticeTimer) {
				clearInterval(this.noticeTimer);
				this.noticeTimer = null;
				console.log('页面隐藏，清除公告定时器');
			}
		},

		// 页面销毁时清除定时器
		onUnload() {
			if (this.noticeTimer) {
				clearInterval(this.noticeTimer);
				this.noticeTimer = null;
				console.log('页面销毁，清除公告定时器');
			}
		}
	}
</script>

<style lang="scss">
	// 年轻化主题样式
	.home-container {
		background: linear-gradient(180deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
		min-height: 100vh;
		display: flex;
		flex-direction: column;

		// 重置所有可能被影响的样式
		* {
			box-sizing: border-box !important;
		}

		// 确保页面布局正常
		display: block !important;
		width: 100% !important;
		overflow-x: hidden !important;
		position: relative !important;
	}

	.status-bar {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	// 自定义下拉刷新
	.custom-refresh {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10rpx);

		.refresh-container {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 40rpx 20rpx 20rpx;

			.stars-animation {
				position: relative;
				width: 80rpx;
				height: 80rpx;
				margin-bottom: 16rpx;

				.star-item {
					position: absolute;
					display: flex;
					align-items: center;
					justify-content: center;

					.star-text {
						font-size: 20rpx;
						color: #FFD700;
						font-weight: bold;
					}

					&.star-1 {
						top: 0;
						left: 50%;
						transform: translateX(-50%);
						animation: starPulse 1.5s ease-in-out infinite;
					}

					&.star-2 {
						top: 20rpx;
						right: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 0.3s;
					}

					&.star-3 {
						bottom: 20rpx;
						right: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 0.6s;
					}

					&.star-4 {
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						animation: starPulse 1.5s ease-in-out infinite 0.9s;
					}

					&.star-5 {
						top: 20rpx;
						left: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 1.2s;
					}
				}
			}

			.refresh-text {
				font-size: 24rpx;
				color: #666;
				font-weight: 500;
			}
		}
	}

	// 主滚动区域
	.main-scroll {
		flex: 1;
		height: 100vh;
	}

	// 顶部导航
	.top-nav {
		display: flex !important;
		align-items: center !important;
		justify-content: space-between !important;
		padding: 16rpx 24rpx !important;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
		width: 100% !important;
		box-sizing: border-box !important;

		.location-area {
			display: flex !important;
			align-items: center !important;
			background: rgba(255, 255, 255, 0.25) !important;
			border-radius: 20rpx !important;
			padding: 8rpx 16rpx !important;
			gap: 8rpx !important;
			flex-shrink: 0 !important;
			min-width: auto !important;
			max-width: none !important;
			backdrop-filter: blur(10rpx) !important;

			.city-text {
				font-size: 28rpx !important;
				font-weight: 600 !important;
				color: #fff !important;
				white-space: nowrap !important;
				text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.1) !important;
			}
		}

		.search-container {
			flex: 1 !important;
			display: flex !important;
			align-items: center !important;
			background: rgba(255, 255, 255, 0.9) !important;
			border-radius: 24rpx !important;
			padding: 12rpx 20rpx !important;
			margin: 0 16rpx !important;
			gap: 12rpx !important;
			backdrop-filter: blur(10rpx) !important;
			min-width: 0 !important;
			max-width: none !important;
			height: auto !important;

			.search-placeholder {
				font-size: 28rpx !important;
				color: #999 !important;
				flex: 1 !important;
				white-space: nowrap !important;
				overflow: hidden !important;
				text-overflow: ellipsis !important;
			}
		}

		.avatar-container {
			position: relative !important;
			flex-shrink: 0 !important;
			width: 64rpx !important;
			height: 64rpx !important;

			.avatar-img {
				width: 64rpx !important;
				height: 64rpx !important;
				border-radius: 32rpx !important;
				border: 3rpx solid rgba(255, 255, 255, 0.8) !important;
				display: block !important;
			}

			.avatar-badge {
				position: absolute !important;
				top: -4rpx !important;
				right: -4rpx !important;
				width: 20rpx !important;
				height: 20rpx !important;
				background: #ff4757 !important;
				border-radius: 50% !important;
				border: 2rpx solid #fff !important;
			}
		}
	}

	// 轮播横幅
	.banner-section {
		padding: 24rpx;

		.banner-swiper {
			height: 320rpx;
			border-radius: 24rpx;
			overflow: hidden;
			position: relative;

			.banner-item {
				width: 100%;
				height: 100%;
				position: relative;

				.banner-bg-image {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}
		}
	}

	// 全局样式覆盖轮播图指示器位置
	.banner-section {
		::v-deep swiper {
			.uni-swiper-dots {
				position: absolute !important;
				bottom: 20rpx !important;
				right: 24rpx !important;
				left: auto !important;
				width: auto !important;
				text-align: right !important;
			}

			.uni-swiper-dot {
				background: rgba(255, 255, 255, 0.4) !important;
				width: 16rpx !important;
				height: 16rpx !important;
				margin: 0 6rpx !important;
			}

			.uni-swiper-dot-active {
				background: #FFD700 !important;
			}
		}
	}

	// 年轻化滚动公告栏
	.notice-section {
		padding: 16rpx 24rpx;
		background: transparent;

		.notice-container {
			background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
			border-radius: 25rpx;
			padding: 16rpx 20rpx;
			display: flex;
			align-items: center;
			box-shadow: 0 8rpx 25rpx rgba(255, 154, 158, 0.3);
			min-height: 70rpx;
			border: 2rpx solid rgba(255, 255, 255, 0.3);
			backdrop-filter: blur(10rpx);
			position: relative;
			overflow: hidden;

			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
				animation: shimmer 3s infinite;
			}

			.notice-icon {
				margin-right: 16rpx;
				animation: bounce 2s infinite;
				flex-shrink: 0;
				z-index: 1;

				.notice-emoji {
					font-size: 32rpx;
					filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.1));
				}
			}

			.notice-content {
				flex: 1;
				overflow: hidden;
				height: 40rpx;
				display: flex;
				align-items: center;
				z-index: 1;

				.notice-scroll-wrapper {
					width: 100%;
					height: 100%;
					overflow: hidden;
					position: relative;

					.notice-text-scroll {
						font-size: 26rpx;
						color: #fff;
						font-weight: 600;
						line-height: 40rpx;
						white-space: nowrap;
						text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.1);
						display: block;
						animation: marqueeAll 60s linear infinite;
						text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
					}
				}
			}

			.notice-close {
				margin-left: 16rpx;
				flex-shrink: 0;
				z-index: 1;
				padding: 8rpx;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.2);
				backdrop-filter: blur(5rpx);

				.close-icon {
					font-size: 32rpx;
					color: #fff;
					font-weight: bold;
					line-height: 1;
					text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.1);
				}
			}
		}
	}

	// 新的动画效果
	@keyframes bounce {
		0%, 20%, 50%, 80%, 100% {
			transform: translateY(0);
		}
		40% {
			transform: translateY(-6rpx);
		}
		60% {
			transform: translateY(-3rpx);
		}
	}

	@keyframes shimmer {
		0% {
			transform: translateX(-100%);
		}
		100% {
			transform: translateX(100%);
		}
	}

	// 滚动文字动画 - 所有公告连续滚动
	@keyframes marqueeAll {
		0% {
			transform: translateX(100%);
		}
		100% {
			transform: translateX(-100%);
		}
	}

	// 快捷功能网格
	.quick-grid {
		padding: 0 24rpx 24rpx;

		.grid-container {
			display: flex;
			gap: 16rpx;
			height: 320rpx;

			// 左侧竖向长方形 (1/2宽度) - 组局约伴
			.vertical-item {
				flex: 1;
				border-radius: 24rpx;
				box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
				transition: all 0.3s ease;
				position: relative;
				overflow: hidden;

				&:active {
					transform: scale(0.96);
				}

				// 组局约伴渐变背景
				&.organize-card {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				}

				.card-layout {
					height: 100%;
					display: flex;
					flex-direction: column;
					padding: 32rpx 24rpx;

					.icon-area {
						flex: 1;
						display: flex;
						justify-content: center;
						align-items: center;
						margin-bottom: 24rpx;
					}

					.text-area {
						text-align: center;

						.item-title {
							font-size: 32rpx;
							font-weight: 700;
							color: #FFFFFF;
							display: block;
							margin-bottom: 8rpx;
							text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
						}

						.item-desc {
							font-size: 24rpx;
							color: rgba(255, 255, 255, 0.9);
							text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.3);
						}
					}
				}
			}

			// 右侧横向正方形容器 (1/2宽度)
			.horizontal-items-container {
				flex: 1;
				display: flex;
				flex-direction: column;
				gap: 16rpx;

				.horizontal-item {
					flex: 1;
					border-radius: 20rpx;
					box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
					transition: all 0.3s ease;
					position: relative;
					overflow: hidden;

					&:active {
						transform: scale(0.96);
					}

					// 游戏玩伴渐变背景
					&.game-card {
						background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
					}

					// 城市玩伴渐变背景
					&.city-card {
						background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
					}

					.horizontal-layout {
						height: 100%;
						display: flex;
						align-items: center;
						padding: 20rpx 24rpx;

						.text-section {
							flex: 1;

							.item-title {
								font-size: 26rpx;
								font-weight: 700;
								color: #333;
								display: block;
								margin-bottom: 6rpx;
								text-shadow: 0 1rpx 3rpx rgba(255, 255, 255, 0.8);
							}

							.item-desc {
								font-size: 20rpx;
								color: #666;
								text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
							}
						}

						.icon-section {
							flex-shrink: 0;
							margin-left: 16rpx;
							display: flex;
							align-items: center;
							justify-content: center;
						}
					}
				}
			}
		}
	}

	// 热门活动
	.hot-section {
		padding: 0 24rpx 24rpx;

		.section-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 24rpx;

			.section-title-container {
				display: flex;
				align-items: center;
				gap: 8rpx;
			}

			.section-title {
				font-size: 36rpx;
				font-weight: 700;
				color: #333;
			}

			.more-btn {
				display: flex;
				align-items: center;

				.more-text {
					font-size: 28rpx;
					color: #999;
					margin-right: 8rpx;
				}

				.more-arrow {
					font-size: 24rpx;
					color: #FFD700;
				}
			}
		}

		.hot-scroll {
			white-space: nowrap;

			.hot-container {
				display: flex;
				gap: 20rpx;

				.hot-card {
					width: 280rpx;
					background: #fff;
					border-radius: 20rpx;
					overflow: hidden;
					box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
					flex-shrink: 0;

					.card-image {
						height: 160rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						position: relative;

						.card-icon {
							display: flex;
							align-items: center;
							justify-content: center;
						}

						.card-tag {
							position: absolute;
							top: 12rpx;
							right: 12rpx;
							background: rgba(255, 255, 255, 0.9);
							color: #ff4757;
							font-size: 20rpx;
							font-weight: 600;
							padding: 4rpx 12rpx;
							border-radius: 12rpx;
						}
					}

					.card-info {
						padding: 20rpx;

						.card-title {
							font-size: 28rpx;
							font-weight: 600;
							color: #333;
							margin-bottom: 12rpx;
							display: block;
						}

						.card-meta {
							margin-bottom: 16rpx;

							.meta-item {
								display: flex;
								align-items: center;
								gap: 6rpx;
								margin-bottom: 4rpx;

								.meta-text {
									font-size: 24rpx;
									color: #999;
								}
							}
						}

						.card-footer {
							display: flex;
							align-items: center;
							justify-content: space-between;

							.participants {
								.participant-count {
									font-size: 24rpx;
									color: #666;
								}
							}

							.card-price {
								font-size: 28rpx;
								font-weight: 600;
								color: #FFD700;
							}
						}
					}
				}
			}
		}
	}

	// 推荐搭子
	.recommend-section {
		padding: 0 24rpx 24rpx;

		.section-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 24rpx;

			.section-title-container {
				display: flex;
				align-items: center;
				gap: 8rpx;
			}

			.section-title {
				font-size: 36rpx;
				font-weight: 700;
				color: #333;
			}

			.more-btn {
				display: flex;
				align-items: center;

				.more-text {
					font-size: 28rpx;
					color: #999;
					margin-right: 8rpx;
				}

				.more-arrow {
					font-size: 24rpx;
					color: #FFD700;
				}
			}
		}

		.partner-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 20rpx;

			.partner-card {
				background: #fff;
				border-radius: 20rpx;
				padding: 24rpx;
				text-align: center;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.95);
				}

				.partner-avatar {
					position: relative;
					margin: 0 auto 16rpx;
					width: 80rpx;
					height: 80rpx;

					.partner-img {
						width: 80rpx;
						height: 80rpx;
						border-radius: 40rpx;
						border: 3rpx solid #f0f0f0;
					}

					.online-dot {
						position: absolute;
						bottom: 4rpx;
						right: 4rpx;
						width: 20rpx;
						height: 20rpx;
						background: #2ed573;
						border-radius: 50%;
						border: 3rpx solid #fff;
					}
				}

				.partner-name {
					font-size: 28rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 8rpx;
					display: block;
				}

				.partner-tags {
					font-size: 24rpx;
					color: #999;
					margin-bottom: 12rpx;
					display: block;
				}

				.partner-stats {
					display: flex;
					justify-content: center;
					gap: 16rpx;

					.stat-item {
						display: flex;
						align-items: center;
						gap: 4rpx;

						.stat-text {
							font-size: 22rpx;
							color: #666;
						}
					}
				}
			}
		}
	}

	// 横条广告
	.ad-section {
		padding: 0 24rpx 24rpx;

		.ad-banner {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx;
			border-radius: 20rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

			.ad-content {
				display: flex;
				align-items: center;
				flex: 1;

				.ad-icon {
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 16rpx;
				}

				.ad-text {
					flex: 1;

					.ad-title {
						font-size: 30rpx;
						font-weight: 600;
						color: #333;
						display: block;
						margin-bottom: 4rpx;
					}

					.ad-subtitle {
						font-size: 24rpx;
						color: #666;
					}
				}
			}

			.ad-button {
				display: flex;
				align-items: center;
				background: rgba(255, 255, 255, 0.9);
				border-radius: 20rpx;
				padding: 12rpx 16rpx;
				gap: 8rpx;

				.button-text {
					font-size: 26rpx;
					font-weight: 600;
					color: #333;
				}
			}
		}
	}

	// 附近组局
	.nearby-section {
		padding: 0 24rpx 24rpx;

		.section-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 24rpx;

			.section-title {
				font-size: 36rpx;
				font-weight: 700;
				color: #333;
			}

			.more-btn {
				display: flex;
				align-items: center;
				gap: 8rpx;

				.more-text {
					font-size: 28rpx;
					color: #999;
				}
			}
		}

		.nearby-list {
			display: flex;
			flex-direction: column;
			gap: 16rpx;

			.nearby-item {
				background: #fff;
				border-radius: 20rpx;
				padding: 24rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
				display: flex;
				align-items: center;
				justify-content: space-between;

				.item-left {
					display: flex;
					align-items: center;
					flex: 1;

					.organizer-avatar {
						width: 80rpx;
						height: 80rpx;
						border-radius: 40rpx;
						margin-right: 16rpx;
					}

					.item-info {
						flex: 1;

						.item-title {
							font-size: 30rpx;
							font-weight: 600;
							color: #333;
							display: block;
							margin-bottom: 8rpx;
						}

						.item-meta {
							display: flex;
							gap: 16rpx;
							margin-bottom: 8rpx;

							.meta-category,
							.meta-distance,
							.meta-time {
								font-size: 24rpx;
								color: #999;
							}

							.meta-category {
								background: #f0f0f0;
								padding: 4rpx 12rpx;
								border-radius: 12rpx;
								color: #666;
							}
						}

						.organizer-name {
							font-size: 24rpx;
							color: #999;
						}
					}
				}

				.item-right {
					display: flex;
					flex-direction: column;
					align-items: center;
					gap: 12rpx;

					.participants-info {
						text-align: center;

						.participants-count {
							font-size: 28rpx;
							font-weight: 600;
							color: #333;
						}

						.participants-label {
							font-size: 24rpx;
							color: #999;
						}
					}

					.join-btn {
						background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
						border-radius: 16rpx;
						padding: 8rpx 20rpx;

						.join-text {
							font-size: 24rpx;
							font-weight: 600;
							color: #333;
						}
					}
				}
			}
		}
	}

	// 热门话题
	.topics-section {
		padding: 0 24rpx 24rpx;

		.section-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 24rpx;

			.section-title {
				font-size: 36rpx;
				font-weight: 700;
				color: #333;
			}

			.more-btn {
				display: flex;
				align-items: center;
				gap: 8rpx;

				.more-text {
					font-size: 28rpx;
					color: #999;
				}
			}
		}

		.topics-list {
			display: flex;
			flex-direction: column;
			gap: 16rpx;

			.topic-item {
				background: #fff;
				border-radius: 20rpx;
				padding: 24rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

				.topic-header {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 12rpx;

					.topic-category {
						background: #f0f0f0;
						color: #666;
						font-size: 22rpx;
						padding: 6rpx 12rpx;
						border-radius: 12rpx;

						&.hot {
							background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
							color: #333;
						}
					}

					.hot-tag {
						font-size: 24rpx;
					}
				}

				.topic-title {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
					line-height: 1.4;
					margin-bottom: 16rpx;
					display: block;
				}

				.topic-footer {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.topic-meta {
						display: flex;
						gap: 16rpx;

						.author,
						.time {
							font-size: 24rpx;
							color: #999;
						}
					}

					.topic-stats {
						display: flex;
						gap: 16rpx;

						.stat-item {
							display: flex;
							align-items: center;
							gap: 4rpx;

							.stat-text {
								font-size: 24rpx;
								color: #999;
							}
						}
					}
				}
			}
		}
	}

	// 底部安全距离
	.safe-bottom {
		height: 120rpx;
	}

	// 弹窗样式
	.device-info-popup-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;

		.device-info-popup-content {
			background: #fff;
			border-radius: 24rpx;
			width: 600rpx;
			max-height: 80vh;
			overflow: hidden;

			.popup-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 32rpx;
				border-bottom: 1rpx solid #f0f0f0;

				.popup-title {
					font-size: 32rpx;
					font-weight: 600;
					color: #333;
				}

				.close-btn {
					font-size: 32rpx;
					color: #999;
					width: 48rpx;
					height: 48rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.popup-body {
				padding: 32rpx;

				.info-item {
					display: flex;
					justify-content: space-between;
					margin-bottom: 24rpx;

					.label {
						font-size: 28rpx;
						color: #666;
					}

					.value {
						font-size: 28rpx;
						color: #333;
					}
				}

				.permission-section {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 24rpx 0;
					border-top: 1rpx solid #f0f0f0;

					.permission-title {
						font-size: 28rpx;
						color: #333;
					}
				}
			}

			.popup-footer {
				padding: 32rpx;
				border-top: 1rpx solid #f0f0f0;

				.confirm-button {
					width: 100%;
					background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
					color: #333;
					font-size: 32rpx;
					font-weight: 600;
					border-radius: 24rpx;
					padding: 24rpx 0;
					border: none;

					&:active {
						background: linear-gradient(135deg, #FFC107 0%, #FFB300 100%);
					}
				}
			}
		}
	}

	// 星星脉冲动画
	@keyframes starPulse {
		0%, 100% {
			transform: scale(0.8);
			opacity: 0.5;
		}
		50% {
			transform: scale(1.2);
			opacity: 1;
		}
	}




</style>
