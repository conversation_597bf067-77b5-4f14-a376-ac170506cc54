<template>
	<view class="page-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="header-content">
				<view class="back-button" @click="goBack">
					<text class="back-icon">‹</text>
				</view>
				<text class="header-title">广告推广服务</text>
				<view class="header-placeholder"></view>
			</view>
		</view>

		<!-- 主要内容 -->
		<view class="main-content">
			<scroll-view scroll-y="true" class="scroll-container">
				<view class="document-content">
					<!-- 文档标题 -->
					<view class="document-header">
						<text class="document-title">闲伴广告推广服务说明</text>
						<text class="document-subtitle">专业广告服务，精准触达用户</text>
						<view class="document-info">
							<text class="info-item">版本：v1.6</text>
							<text class="info-item">发布日期：2025年6月4日</text>
							<text class="info-item">生效日期：2025年6月4日</text>
						</view>
					</view>

					<!-- 第1章 -->
					<view class="content-section">
						<text class="section-title">1. 服务概述</text>
						<view class="section-item">
							<text class="item-number">1.1</text>
							<view class="item-content">
								<text class="item-title">服务定位</text>
								<text class="item-text">闲伴广告推广服务致力于为企业和个人提供专业的数字营销解决方案，帮助客户在闲伴平台上精准触达目标用户。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.2</text>
							<view class="item-content">
								<text class="item-title">服务优势</text>
								<text class="item-text">基于大数据分析和智能算法，提供精准的用户画像和投放策略，确保广告效果最大化。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">1.3</text>
							<view class="item-content">
								<text class="item-title">服务理念</text>
								<text class="item-text">坚持用户体验优先，确保广告内容与平台调性相符，为用户提供有价值的信息。</text>
							</view>
						</view>
					</view>
					
					<!-- 第2章 -->
					<view class="content-section">
						<text class="section-title">2. 广告形式</text>
						<view class="section-item">
							<text class="item-number">2.1</text>
							<view class="item-content">
								<text class="item-title">信息流广告</text>
								<text class="item-text">融入用户浏览流中的原生广告，形式自然，用户接受度高。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.2</text>
							<view class="item-content">
								<text class="item-title">横幅广告</text>
								<text class="item-text">位于页面顶部或底部的展示广告，曝光量大，品牌展示效果佳。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.3</text>
							<view class="item-content">
								<text class="item-title">活动推广</text>
								<text class="item-text">针对特定活动或产品的专题推广，支持多种创意形式。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">2.4</text>
							<view class="item-content">
								<text class="item-title">KOL合作</text>
								<text class="item-text">与平台优质创作者合作，通过内容营销实现品牌传播。</text>
							</view>
						</view>
					</view>
					
					<!-- 第3章 -->
					<view class="content-section">
						<text class="section-title">3. 投放策略</text>
						<view class="section-item">
							<text class="item-number">3.1</text>
							<view class="item-content">
								<text class="item-title">精准定向</text>
								<text class="item-text">基于用户的年龄、性别、地域、兴趣等多维度数据进行精准投放。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.2</text>
							<view class="item-content">
								<text class="item-title">时段优化</text>
								<text class="item-text">根据目标用户的活跃时间，优化广告投放时段，提升转化效果。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">3.3</text>
							<view class="item-content">
								<text class="item-title">频次控制</text>
								<text class="item-text">合理控制广告展示频次，避免用户疲劳，保持良好的用户体验。</text>
							</view>
						</view>
					</view>
					
					<!-- 第4章 -->
					<view class="content-section">
						<text class="section-title">4. 服务流程</text>
						<view class="section-item">
							<text class="item-number">4.1</text>
							<view class="item-content">
								<text class="item-title">需求沟通</text>
								<text class="item-text">深入了解客户的推广目标、预算范围和目标用户群体。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.2</text>
							<view class="item-content">
								<text class="item-title">方案制定</text>
								<text class="item-text">基于客户需求制定个性化的广告投放方案和创意策略。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.3</text>
							<view class="item-content">
								<text class="item-title">执行监控</text>
								<text class="item-text">实时监控广告投放效果，及时调整优化策略。</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">4.4</text>
							<view class="item-content">
								<text class="item-title">效果报告</text>
								<text class="item-text">提供详细的投放数据报告和效果分析，助力客户决策。</text>
							</view>
						</view>
					</view>
					
					<!-- 第5章 -->
					<view class="content-section">
						<text class="section-title">5. 联系方式</text>
						<view class="section-item">
							<text class="item-number">5.1</text>
							<view class="item-content">
								<text class="item-text">商务合作邮箱：<EMAIL></text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">5.2</text>
							<view class="item-content">
								<text class="item-text">客服热线：400-888-0000</text>
							</view>
						</view>
						<view class="section-item">
							<text class="item-number">5.3</text>
							<view class="item-content">
								<text class="item-text">公司地址：上海市崇明区北沿公路2111号3幢</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'AdvertisingService',
	data() {
		return {
			statusBarHeight: 0
		};
	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 0;
	},
	methods: {
		goBack() {
			// 尝试返回上一页，如果没有上一页则跳转到社区公约页面
			const pages = getCurrentPages();
			if (pages.length > 1) {
				uni.navigateBack({
					delta: 1
				});
			} else {
				uni.redirectTo({
					url: '/pages/community-rules/community-rules'
				});
			}
		}
	}
};
</script>

<style lang="scss">
.page-container {
	min-height: 100vh;
	background: #f8f9fa;
	display: flex;
	flex-direction: column;
}

.status-bar {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
}

.header {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
	padding: 0 32rpx 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(255, 215, 0, 0.2);

	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;

		.back-button {
			width: 64rpx;
			height: 64rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50%;
			backdrop-filter: blur(10rpx);

			.back-icon {
				font-size: 36rpx;
				color: #333;
				font-weight: bold;
				line-height: 1;
			}
		}

		.header-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			flex: 1;
			text-align: center;
		}

		.header-placeholder {
			width: 64rpx;
		}
	}
}

.main-content {
	flex: 1;
	overflow: hidden;

	.scroll-container {
		height: 100%;
	}
}

.document-content {
	padding: 32rpx;

	.document-header {
		text-align: center;
		margin-bottom: 48rpx;
		padding: 48rpx 32rpx;
		background: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

		.document-title {
			font-size: 48rpx;
			font-weight: 700;
			color: #333;
			display: block;
			margin-bottom: 16rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}

		.document-subtitle {
			font-size: 28rpx;
			color: #999;
			display: block;
			margin-bottom: 32rpx;
			font-style: italic;
		}

		.document-info {
			display: flex;
			justify-content: center;
			flex-wrap: wrap;
			gap: 24rpx;

			.info-item {
				font-size: 24rpx;
				color: #666;
				padding: 8rpx 16rpx;
				background: #f8f9fa;
				border-radius: 8rpx;
			}
		}
	}

	.content-section {
		margin-bottom: 48rpx;
		background: #fff;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

		.section-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			display: block;
			margin-bottom: 32rpx;
			padding: 24rpx 32rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
			border-radius: 12rpx;
			text-align: center;
			box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.2);
		}

		.section-item {
			display: flex;
			margin-bottom: 24rpx;
			align-items: flex-start;

			&:last-child {
				margin-bottom: 0;
			}

			.item-number {
				font-size: 28rpx;
				color: #FFD700;
				font-weight: 600;
				margin-right: 20rpx;
				flex-shrink: 0;
				margin-top: 4rpx;
				min-width: 60rpx;
			}

			.item-content {
				flex: 1;
				line-height: 1.6;

				.item-title {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
					display: block;
					margin-bottom: 12rpx;
				}

				.item-text {
					font-size: 28rpx;
					color: #666;
					line-height: 1.8;
					text-align: justify;
				}
			}
		}
	}
}
</style>
