<template>
	<view class="rules-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 顶部导航栏 -->
		<view class="top-header">
			<view class="header-left" @click="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
				<text class="back-text">返回</text>
			</view>
			<text class="header-title">社区公约</text>
			<view class="header-right"></view>
		</view>
		
		<!-- 主要内容区域 -->
		<scroll-view class="main-content" scroll-y>
			<view class="content-wrapper">
				<!-- 页面介绍 -->
				<view class="page-intro">
					<text class="intro-title">闲伴社区规范</text>
					<text class="intro-subtitle">共建和谐、友善、积极的社区环境</text>
				</view>

				<!-- 文章列表 -->
				<view class="article-list">
					<!-- 社区规则文档 -->
					<view
						class="article-item"
						v-for="category in ruleCategories"
						:key="category.id"
						@click="viewDocument(category.id)"
					>
						<view class="article-content">
							<text class="article-title">{{ getDocumentInfo(category.id).title }}</text>
							<text class="article-subtitle">{{ getDocumentInfo(category.id).subtitle }}</text>
							<view class="article-meta">
								<text class="meta-tag">{{ getDocumentInfo(category.id).version }}</text>
								<text class="meta-date">{{ getDocumentInfo(category.id).updateTime }}</text>
							</view>
						</view>
						<view class="article-arrow">
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</view>

					<!-- 相关政策文档 -->
					<view class="policy-section">
						<text class="section-title">相关政策</text>

						<!-- 隐私权政策 -->
						<view class="article-item policy-item" @click="goToPrivacyPolicy">
							<view class="article-content">
								<text class="article-title">隐私权政策</text>
								<text class="article-subtitle">了解我们如何保护您的个人信息</text>
								<view class="article-meta">
									<text class="meta-tag">v2.1.0</text>
									<text class="meta-date">2025年6月3日</text>
								</view>
							</view>
							<view class="article-arrow">
								<uni-icons type="right" size="16" color="#999"></uni-icons>
							</view>
						</view>

						<!-- 用户协议 -->
						<view class="article-item policy-item" @click="goToUserAgreement">
							<view class="article-content">
								<text class="article-title">闲伴用户协议</text>
								<text class="article-subtitle">使用闲伴服务的法律协议</text>
								<view class="article-meta">
									<text class="meta-tag">v3.0.0</text>
									<text class="meta-date">2025年6月3日</text>
								</view>
							</view>
							<view class="article-arrow">
								<uni-icons type="right" size="16" color="#999"></uni-icons>
							</view>
						</view>
					</view>
				</view>

				<!-- 联系方式 -->
				<view class="contact-info">
					<text class="contact-title">联系我们</text>
					<text class="contact-text">如有疑问或建议，请通过以下方式联系我们</text>
					<text class="contact-text">邮箱：<EMAIL></text>
					<text class="contact-text">地址：上海市崇明区北沿公路2111号3幢</text>
				</view>
			</view>
		</scroll-view>


	</view>
</template>

<script>
export default {
	name: 'CommunityRules',
	data() {
		return {
			statusBarHeight: 0,
			currentCategory: 'community',

			// 规则分类
			ruleCategories: [
				{ id: 'community', name: '社区公约条例' },
				{ id: 'punishment', name: '社区处罚条例' },
				{ id: 'content', name: '发布内容规范' },
				{ id: 'recommendation', name: '推荐规则' },
				{ id: 'review', name: '审核规则' },
				{ id: 'unban', name: '解封规则' },
				{ id: 'creator', name: '创作者协议' },
				{ id: 'antifraud', name: '反作弊规则' },
				{ id: 'youth', name: '青少年模式' },
				{ id: 'dataprotection', name: '数据保护规则' },
				{ id: 'groupguide', name: '组局完全指南' },
				{ id: 'advertising', name: '广告推广服务' }
			],
			
			// 规则文档数据
			ruleDocuments: {
				community: {
					title: '闲伴社区公约条例',
					subtitle: '共建和谐、友善、积极的社区环境',
					version: 'v1.0.0',
					publishDate: '2025年6月3日',
					effectiveDate: '2025年6月3日',
					updateTime: '2025年6月3日',
					content: [
						{
							title: '第一章 总则',
							numbering: '1.',
							items: [
								{
									title: '目的和宗旨',
									content: '为了维护闲伴社区的良好秩序，保护用户合法权益，营造健康、和谐、积极向上的社区环境，特制定本公约。'
								},
								{
									title: '适用范围',
									content: '本公约适用于所有使用闲伴平台的用户，包括但不限于注册用户、游客用户等。'
								},
								{
									title: '基本原则',
									content: '用户在使用闲伴平台时，应当遵守国家法律法规，尊重社会公德，维护网络安全，不得利用平台从事危害国家安全、泄露国家秘密，颠覆国家政权，破坏国家统一的活动。',
									subItems: [
										'遵守法律法规：严格遵守《中华人民共和国网络安全法》、《中华人民共和国数据安全法》、《中华人民共和国个人信息保护法》等相关法律法规',
										'尊重他人权益：不侵犯他人肖像权、隐私权、名誉权、知识产权等合法权益',
										'维护社区秩序：不发布垃圾信息、不恶意刷屏、不进行网络暴力、不传播谣言',
										'诚实守信：发布真实信息，不虚构事实，不误导他人，建立良好的信用记录',
										'积极向上：传播正能量内容，弘扬社会主义核心价值观，促进社会和谐发展',
										'保护未成年人：不向未成年人传播不良信息，为未成年人营造健康的网络环境',
										'环保理念：倡导绿色生活方式，在户外活动中保护环境，不破坏生态'
									]
								},
								{
									title: '平台定位',
									content: '闲伴致力于打造一个专注于户外活动、生活分享的社交平台，主要服务于露营、钓鱼、BBQ、徒步、夜跑、聚会等活动的爱好者。'
								},
								{
									title: '用户权利',
									content: '用户在遵守本公约的前提下，享有以下权利：',
									subItems: [
										'自由发布符合规范的内容',
										'参与社区互动和讨论',
										'举报违规行为',
										'申请账号注销',
										'查看和修改个人信息',
										'对平台处罚决定提出申诉'
									]
								},
								{
									title: '用户义务',
									content: '用户应当履行以下义务：',
									subItems: [
										'遵守本公约及相关规则',
										'配合平台的安全检查',
										'及时更新个人信息',
										'保护账号安全',
										'维护社区环境',
										'承担相应的法律责任'
									]
								}
							]
						},
						{
							title: '第二章 用户行为规范',
							numbering: '2.',
							items: [
								{
									title: '内容发布规范',
									content: '用户发布的内容应当真实、合法、健康，不得包含以下内容：',
									subItems: [
										'违反国家法律法规的内容',
										'危害国家安全、泄露国家秘密的内容',
										'颠覆国家政权、破坏国家统一的内容',
										'损害国家荣誉和利益的内容',
										'煽动民族仇恨、民族歧视的内容',
										'破坏民族团结的内容',
										'破坏国家宗教政策的内容',
										'宣扬邪教和封建迷信的内容',
										'散布谣言、扰乱社会秩序的内容',
										'侮辱或者诽谤他人的内容',
										'侵害他人合法权益的内容'
									]
								},
								{
									title: '互动行为规范',
									content: '用户在社区互动时应当文明礼貌，理性表达，建立良好的社交环境，不得进行以下行为：',
									subItems: [
										'人身攻击：恶意攻击、谩骂、侮辱他人，包括但不限于种族歧视、性别歧视、地域歧视等',
										'恶意刷屏：重复发布相同或类似内容，恶意灌水，发布无意义的垃圾信息',
										'身份冒充：冒充他人身份、虚构个人信息、盗用他人头像或资料',
										'恶意举报：无理由恶意举报他人，滥用举报功能扰乱平台秩序',
										'谣言传播：故意传播虚假信息、未经证实的消息、恶意造谣',
										'非法营销：未经授权进行商业广告、传销、诈骗等营销活动',
										'骚扰行为：持续骚扰他人、恶意私信、跟踪他人动态',
										'引战行为：故意挑起争端、煽动对立情绪、破坏和谐氛围'
									]
								},
								{
									title: '账号使用规范',
									content: '用户应当规范使用账号，维护账号安全：',
									subItems: [
										'一人一号：每个用户只能注册一个主账号，不得恶意注册多个账号',
										'真实信息：提供真实有效的注册信息，及时更新个人资料',
										'账号安全：妥善保管账号密码，不得出售、转让、共享账号',
										'合规昵称：使用健康向上的昵称，不得使用违法违规、低俗不雅的名称',
										'头像规范：使用合适的头像，不得使用违法、暴力、色情等不当图片'
									]
								},
								{
									title: '隐私保护规范',
									content: '用户应当尊重他人隐私，保护个人信息安全：',
									subItems: [
										'不得未经同意公开他人个人信息',
										'不得恶意收集他人隐私数据',
										'不得利用技术手段窃取他人信息',
										'妥善保护自己的个人隐私',
										'举报侵犯隐私的违法行为'
									]
								}
							]
						}
					]
				}
			}
		}
	},
	
	onLoad() {
		// 获取系统信息
		uni.getSystemInfo({
			success: (res) => {
				this.statusBarHeight = res.statusBarHeight;
			}
		});
		
		// 初始化所有规则文档
		this.initializeRuleDocuments();
	},
	
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 查看文档详情
		viewDocument(categoryId) {
			console.log('点击了文档:', categoryId);

			const routeMap = {
				'community': '/pages/community-rules/community-covenant',
				'punishment': '/pages/community-rules/punishment-rules',
				'content': '/pages/community-rules/content-standards',
				'recommendation': '/pages/community-rules/recommendation-rules',
				'review': '/pages/community-rules/review-rules',
				'unban': '/pages/community-rules/unban-rules',
				'creator': '/pages/community-rules/creator-agreement',
				'antifraud': '/pages/community-rules/anti-fraud-rules',
				'youth': '/pages/community-rules/youth-mode-rules',
				'dataprotection': '/pages/community-rules/data-protection-rules',
				'groupguide': '/pages/community-rules/group-activity-rules',
				'advertising': '/pages/community-rules/advertising-service'
			};

			const route = routeMap[categoryId];
			console.log('路由路径:', route);

			if (route) {
				console.log('准备跳转到:', route);
				uni.navigateTo({
					url: route,
					success: function(res) {
						console.log('跳转成功:', res);
					},
					fail: function(err) {
						console.log('跳转失败:', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			} else {
				console.log('未找到对应路由');
				uni.showToast({
					title: '页面不存在',
					icon: 'none'
				});
			}
		},

		// 获取文档信息
		getDocumentInfo(categoryId) {
			const doc = this.ruleDocuments[categoryId];
			return {
				title: doc?.title || '',
				subtitle: doc?.subtitle || '',
				version: doc?.version || '',
				updateTime: doc?.updateTime || ''
			};
		},

		// 跳转到隐私权政策
		goToPrivacyPolicy() {
			uni.navigateTo({
				url: '/pages/privacy-policy/privacy-policy'
			});
		},

		// 跳转到用户协议
		goToUserAgreement() {
			uni.navigateTo({
				url: '/pages/user-agreement/user-agreement'
			});
		},
		
		// 初始化规则文档
		initializeRuleDocuments() {
			// 这里会添加其他规则文档的内容
			this.initializePunishmentRules();
			this.initializeRecommendationRules();
			this.initializeContentRules();
			this.initializeAdvertisingRules();
			this.initializeReviewRules();
			this.initializeUnbanRules();
			this.initializeCreatorAgreement();
			this.initializeAntiFraudRules();
			this.initializeYouthModeRules();
			this.initializeDataProtectionRules();
			this.initializeGroupGuide();
		},
		
		// 初始化处罚条例
		initializePunishmentRules() {
			this.ruleDocuments.punishment = {
				title: '闲伴社区处罚条例',
				subtitle: '维护社区秩序，保障用户权益',
				version: 'v1.0.0',
				publishDate: '2025年6月3日',
				effectiveDate: '2025年6月3日',
				updateTime: '2025年6月3日',
				content: [
					{
						title: '第一章 处罚原则',
						numbering: '1.',
						items: [
							{
								title: '处罚依据',
								content: '对违反社区公约的用户，平台将根据违规行为的性质、情节轻重、影响范围等因素，依据本条例给予相应处罚。'
							},
							{
								title: '处罚原则',
								content: '处罚应当遵循公平、公正、公开的原则，做到事实清楚、证据确凿、程序合法、处罚适当。',
								subItems: [
									'教育与处罚相结合',
									'过罚相当，宽严适度',
									'一事不再罚',
									'保障用户申诉权利'
								]
							},
							{
								title: '处罚等级说明',
								content: '根据违规行为的严重程度，处罚分为四个等级：',
								subItems: [
									'轻微违规：警告、内容删除',
									'一般违规：限制功能1-7天',
									'严重违规：限制功能7-30天或临时封禁',
									'特别严重违规：长期封禁或永久封禁'
								]
							}
						]
					},
					{
						title: '第二章 违规行为及处罚标准',
						numbering: '2.',
						items: [
							{
								title: '一、危害国家安全类违规内容',
								content: '发布危害国家安全、政治敏感等严重违法内容处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['煽动民族仇恨、民族歧视、破坏民族团结', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['煽动民族对立、地域歧视、分裂国家统一', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['宣扬法轮功等邪教组织、封建迷信内容', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['发布涉及国家时政敏感话题、重大突发事件不实信息', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['宣扬其他政党、反对现行政治制度', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['侮辱、诽谤民族英雄、革命先烈', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['煽动对抗国家政策、恶意传播负面政治言论', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['泄露国家军事机密、涉密信息', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['发布间谍活动、危害国家安全相关内容', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['颠覆国家政权、推翻社会主义制度', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理']
								]
							},
							{
								title: '二、违法犯罪类内容',
								content: '发布涉及违法犯罪活动的内容处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['发布不当内容、低俗擦边、令人不适的图片/视频/语音', '删除内容 + 警告 + 限制发布7天', '限制功能15天 + 降低账号权重', '永久封禁账号'],
									['发布暴力血腥、极端内容', '删除内容 + 限制功能15天', '临时封禁30天', '永久封禁账号'],
									['发布涉及未成年人不当内容', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['宣扬极端主义思想', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['发布违禁物品相关教程或信息', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['发布危险品制作方法', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['发布严重犯罪相关信息', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['发布金融犯罪内容', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['发布管制物品买卖信息（除政府认证账号外）', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['发布违禁药品信息', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['发布自我伤害相关方法、教唆内容', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['教唆未成年人违法行为', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['发布假币制作、买卖相关内容', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['发布违法犯罪教程、灰色产业链信息', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['发布诈骗相关工作、话题内容', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['发布不当服务相关信息', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理']
								]
							},
							{
								title: '三、社会秩序类违规内容',
								content: '扰乱社会秩序、传播有害信息的内容处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['传播重大灾难、事故的虚假信息', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号'],
									['发布涉及疫情、公共卫生的不实信息', '删除内容 + 限制功能7天', '限制功能30天', '永久封禁账号'],
									['煽动群体性事件、影响社会稳定', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['发布扰乱经济秩序、金融市场的虚假信息', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号'],
									['传播迷信、占卜、风水等封建糟粕', '删除内容 + 警告', '限制功能7天', '限制功能30天'],
									['发布违背科学常识、伪科学内容', '删除内容 + 警告', '限制功能7天', '限制功能15天'],
									['煽动医闹、校闹等违法聚集活动', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号']
								]
							},
							{
								title: '四、平台引流和内容规范类违规',
								content: '违反平台内容规范、恶意引流等行为处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['发布作品包含其他平台水印、标识', '删除内容 + 警告', '限制发布3天', '限制功能7天'],
									['恶意引流至其他平台', '删除内容 + 限制功能7天', '限制功能15天', '限制功能30天'],
									['发布大尺度内容、不当着装展示', '删除内容 + 警告 + 限制发布7天', '限制功能15天', '限制功能30天'],
									['发布不雅动作、不当肢体行为', '删除内容 + 限制功能7天', '限制功能15天', '限制功能30天'],
									['发布有害健康行为教学内容', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号'],
									['恶意模仿、恶搞他人', '删除内容 + 警告', '限制功能7天', '限制功能15天'],
									['侵犯他人肖像权、隐私权', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号']
								]
							},
							{
								title: '五、个人信息泄露类违规内容',
								content: '发布个人联系方式、账号信息等违规内容处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['发布个人微信号、QQ号等即时通讯账号', '删除内容 + 警告', '限制发布3天', '限制功能7天'],
									['发布个人手机号码、座机号码', '删除内容 + 警告', '限制发布3天', '限制功能7天'],
									['发布个人邮箱地址、社交媒体账号', '删除内容 + 警告', '限制发布3天', '限制功能7天'],
									['发布他人隐私信息、个人资料', '删除内容 + 限制功能7天', '限制功能15天', '限制功能30天'],
									['恶意泄露他人联系方式、住址信息', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号'],
									['发布身份证号、银行卡号等敏感信息', '删除内容 + 限制功能7天', '限制功能30天', '永久封禁账号'],
									['人肉搜索、恶意传播他人个人信息', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理']
								]
							},
							{
								title: '六、群体行为和社会秩序类违规',
								content: '组织或参与破坏社会秩序的群体行为处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['组织或煽动群体聚集闹事', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['拉帮结派、组织网络对抗', '限制功能15天', '限制功能30天', '永久封禁账号'],
									['煽动约架、线下冲突', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号'],
									['发布暴力冲突、打斗内容', '删除内容 + 限制功能15天', '临时封禁30天', '永久封禁账号'],
									['组织水军、恶意刷量团伙', '清除虚假数据 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['煽动对抗情绪、制造社会对立', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号']
								]
							},
							{
								title: '七、身份冒充和权威机构类违规',
								content: '冒充权威机构、公职人员等行为处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['冒充党政机关工作人员', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['冒充国家机关、事业单位人员', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['冒充国家领导人、公众人物', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['利用公职身份谋取不当利益', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['发布宗教极端内容、煽动宗教对立', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理']
								]
							},
							{
								title: '八、恶意攻击和骚扰行为',
								content: '对其他用户进行恶意攻击、骚扰、威胁等行为处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['恶意谩骂、人身攻击、侮辱他人', '警告 + 限制评论3天', '限制功能7天', '限制功能30天'],
									['恶意骚扰、跟踪、威胁他人', '限制功能7天 + 限制私信', '限制功能30天', '永久封禁账号'],
									['组织网络暴力、恶意举报', '限制功能15天', '临时封禁60天', '永久封禁账号'],
									['发布仇恨言论、煽动对立情绪', '删除内容 + 限制功能7天', '限制功能30天', '永久封禁账号']
								]
							},
							{
								title: '九、虚假信息和欺诈行为',
								content: '发布虚假信息、进行欺诈等行为处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['发布虚假活动信息、恶意欺骗他人', '删除内容 + 警告 + 限制发布7天', '限制功能15天', '限制功能30天'],
									['冒充他人身份、虚构个人信息', '强制修改资料 + 限制功能7天', '限制功能30天', '永久封禁账号'],
									['发布虚假商品信息、进行诈骗活动', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['传播谣言、恶意造谣', '删除内容 + 限制功能7天', '限制功能30天', '永久封禁账号'],
									['虚假宣传、夸大产品功效', '删除内容 + 警告', '限制功能7天', '限制功能15天']
								]
							},
							{
								title: '十、技术违规和破坏行为',
								content: '使用技术手段破坏平台秩序的行为处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['使用外挂软件、恶意脚本', '限制功能15天', '临时封禁30天', '永久封禁账号'],
									['使用爬虫程序、数据抓取工具', '限制功能7天', '限制功能30天', '永久封禁账号'],
									['发布破解软件、盗版程序', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号'],
									['恶意攻击平台服务器、网络安全漏洞利用', '永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['传播计算机病毒、恶意代码', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理']
								]
							},
							{
								title: '十一、恶意营销和刷量行为',
								content: '进行恶意营销、刷量等破坏平台秩序的行为处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['恶意刷赞、刷评论、刷关注', '清除虚假数据 + 限制功能7天', '限制功能30天 + 降低权重', '永久封禁账号'],
									['未经授权发布广告、进行商业推广', '删除内容 + 警告', '限制功能7天', '限制功能30天'],
									['恶意刷屏、发布垃圾信息', '删除内容 + 限制发布3天', '限制功能7天', '限制功能30天'],
									['建立营销群组、拉人头推广', '删除内容 + 限制功能7天', '限制功能30天', '永久封禁账号'],
									['发布刷单兼职、网络兼职等诈骗信息', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号'],
									['发布虚假福利、诱导性福利信息', '删除内容 + 限制功能7天', '限制功能15天', '限制功能30天']
								]
							},
							{
								title: '十二、平台冒充和欺诈行为',
								content: '冒充平台官方、客服等欺诈行为处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['冒充官方客服、平台工作人员', '删除内容 + 限制功能30天', '永久封禁账号', '移交相关部门处理'],
									['发布非官方客服信息、虚假客服联系方式', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号'],
									['冒充平台官方账号、发布虚假官方信息', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理'],
									['利用平台名义进行诈骗、非法牟利', '删除内容 + 永久封禁账号', '移交相关部门处理', '移交相关部门处理']
								]
							},
							{
								title: '十三、特殊敏感内容',
								content: '涉及特殊敏感话题的内容处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['发布同性恋相关内容但未标注个人观点声明', '要求添加声明 + 警告', '删除内容 + 限制发布3天', '限制功能7天'],
									['发布同性恋相关令人反感、极端内容', '删除内容 + 限制功能7天', '限制功能15天', '限制功能30天'],
									['发布宗教极端主义、宗教仇恨内容', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号'],
									['发布种族歧视、性别歧视等歧视性内容', '删除内容 + 限制功能7天', '限制功能30天', '永久封禁账号']
								]
							},
							{
								title: '十四、侵犯知识产权行为',
								content: '侵犯他人知识产权的行为处罚标准：',
								isTable: true,
								tableHeaders: ['违规行为', '首次处罚', '再犯处罚', '终极处罚'],
								tableData: [
									['盗用他人原创内容、恶意抄袭', '删除内容 + 警告 + 限制发布3天', '限制功能7天', '限制功能30天'],
									['未经授权使用他人作品、侵犯版权', '删除内容 + 限制发布7天', '限制功能15天', '限制功能30天'],
									['恶意盗用他人头像、昵称等个人标识', '强制修改 + 警告', '限制功能7天', '限制功能30天'],
									['商标侵权、冒用品牌标识', '删除内容 + 限制功能15天', '限制功能30天', '永久封禁账号']
								]
							}
						]
					},
					{
						title: '第三章 处罚执行程序',
						numbering: '3.',
						items: [
							{
								title: '违规认定',
								content: '平台通过以下方式认定违规行为：',
								subItems: [
									'用户举报：其他用户举报违规内容',
									'系统检测：AI智能识别违规内容',
									'人工审核：专业审核团队人工审查',
									'主动巡查：平台主动发现违规行为'
								]
							},
							{
								title: '处罚通知',
								content: '对违规用户的处罚通知包含以下信息：',
								subItems: [
									'违规行为具体描述',
									'违反的具体条款',
									'处罚措施和期限',
									'申诉渠道和时限',
									'处罚生效时间'
								]
							},
							{
								title: '申诉机制',
								content: '用户对处罚决定不服的，可在收到通知后7日内提出申诉：',
								subItems: [
									'申诉渠道：通过平台申诉功能或邮件申诉',
									'申诉材料：提供相关证据和说明',
									'处理时限：平台在3个工作日内处理申诉',
									'申诉结果：维持原处罚或撤销/减轻处罚'
								]
							},
							{
								title: '特殊内容处理说明',
								content: '对于特殊敏感内容的处理原则：',
								subItems: [
									'同性恋相关内容：允许发布但需标注"以上内容仅代表个人观点"声明',
									'宗教内容：允许正常宗教讨论，禁止极端主义和仇恨言论',
									'政治敏感内容：严格按照国家法律法规执行，零容忍政策',
									'未成年人保护：涉及未成年人的违规内容从严处理',
									'技术违规：使用技术手段破坏平台的行为将被严厉打击'
								]
							}
						]
					}
				]
			};
		},

		// 初始化推荐规则
		initializeRecommendationRules() {
			this.ruleDocuments.recommendation = {
				title: '闲伴内容推荐规则',
				subtitle: '优质内容优先，用户体验至上',
				version: 'v1.0.0',
				publishDate: '2025年6月3日',
				effectiveDate: '2025年6月3日',
				updateTime: '2025年6月3日',
				content: [
					{
						title: '第一章 推荐原则',
						numbering: '1.',
						items: [
							{
								title: '内容质量优先',
								content: '优先推荐高质量、原创性强、用户互动度高的内容。',
								subItems: [
									'原创内容优于转载内容',
									'高清图片/视频优于模糊内容',
									'完整描述优于简单标题',
									'积极正面优于消极负面'
								]
							},
							{
								title: '用户兴趣匹配',
								content: '基于用户的浏览历史、点赞收藏、关注偏好等数据，推荐符合用户兴趣的内容。'
							},
							{
								title: '多样性保障',
								content: '确保推荐内容的多样性，避免信息茧房，为用户提供丰富的内容体验。'
							}
						]
					},
					{
						title: '第二章 推荐算法',
						numbering: '2.',
						items: [
							{
								title: '内容评分机制',
								content: '综合考虑以下因素对内容进行评分：',
								subItems: [
									'内容质量（40%）：原创性、完整性、清晰度',
									'用户互动（30%）：点赞、评论、分享、收藏',
									'时效性（15%）：发布时间、热度趋势',
									'用户匹配度（15%）：兴趣标签、行为偏好'
								]
							},
							{
								title: '负面因素降权',
								content: '以下情况将降低内容推荐权重：',
								subItems: [
									'用户举报较多',
									'互动率异常（刷量行为）',
									'内容重复度高',
									'违规记录较多',
									'用户反馈较差'
								]
							}
						]
					}
				]
			};
		},

		// 初始化内容规范
		initializeContentRules() {
			this.ruleDocuments.content = {
				title: '闲伴发布内容规范',
				subtitle: '创作优质内容，传播正能量',
				version: 'v1.0.0',
				publishDate: '2025年6月3日',
				effectiveDate: '2025年6月3日',
				updateTime: '2025年6月3日',
				content: [
					{
						title: '第一章 内容标准',
						numbering: '1.',
						items: [
							{
								title: '内容真实性',
								content: '发布的内容应当真实可信，不得编造虚假信息。',
								subItems: [
									'真实记录生活体验',
									'准确描述产品服务',
									'如实分享个人观点',
									'标明转载来源'
								]
							},
							{
								title: '内容原创性',
								content: '鼓励用户发布原创内容，转载内容需注明出处。',
								subItems: [
									'优先推荐原创作品',
									'转载需获得授权',
									'标注原作者信息',
									'禁止抄袭剽窃'
								]
							},
							{
								title: '内容质量要求',
								content: '发布的内容应当具有一定的质量标准：',
								subItems: [
									'图片清晰，构图美观',
									'视频画质良好，声音清楚',
									'文字表达清晰，语法正确',
									'内容完整，信息充分'
								]
							}
						]
					},
					{
						title: '第二章 禁止内容',
						numbering: '2.',
						items: [
							{
								title: '违法违规内容',
								content: '严禁发布违反国家法律法规的内容：',
								subItems: [
									'危害国家安全的内容',
									'涉及政治敏感话题',
									'宣扬暴力恐怖主义',
									'传播淫秽色情信息',
									'涉及赌博毒品等违法活动',
									'侵犯他人知识产权',
									'泄露他人隐私信息'
								]
							},
							{
								title: '不当商业行为',
								content: '禁止以下商业推广行为：',
								subItems: [
									'未经授权的广告推广',
									'虚假宣传和欺诈信息',
									'传销和非法集资',
									'售卖违禁物品',
									'恶意营销和刷量行为'
								]
							}
						]
					}
				]
			};
		},

		// 初始化广告说明
		initializeAdvertisingRules() {
			this.ruleDocuments.advertising = {
				title: '闲伴AD广告推广服务说明',
				subtitle: '规范广告投放，保障用户体验',
				version: 'v1.0.0',
				publishDate: '2025年6月3日',
				effectiveDate: '2025年6月3日',
				updateTime: '2025年6月3日',
				content: [
					{
						title: '第一章 广告服务概述',
						numbering: '1.',
						items: [
							{
								title: '服务介绍',
								content: '闲伴为企业和个人提供专业的广告推广服务，帮助客户在平台上精准触达目标用户。'
							},
							{
								title: '广告形式',
								content: '平台支持多种广告形式：',
								subItems: [
									'信息流广告：融入用户浏览内容中',
									'横幅广告：页面顶部或底部展示',
									'开屏广告：应用启动时展示',
									'品牌合作：与平台深度合作推广'
								]
							}
						]
					},
					{
						title: '第二章 广告投放规范',
						numbering: '2.',
						items: [
							{
								title: '内容要求',
								content: '广告内容必须符合以下要求：',
								subItems: [
									'内容真实，不得虚假宣传',
									'符合国家法律法规',
									'不得误导消费者',
									'明确标识为广告内容',
									'尊重用户隐私权益'
								]
							},
							{
								title: '投放流程',
								content: '广告投放需要经过以下流程：',
								subItems: [
									'提交广告申请和资质材料',
									'平台审核广告内容',
									'确定投放计划和预算',
									'上线投放并监控效果',
									'定期优化和调整策略'
								]
							},
							{
								title: '收费标准',
								content: '广告服务采用以下收费模式：',
								subItems: [
									'CPM：按千次展示收费',
									'CPC：按点击次数收费',
									'CPD：按天数收费',
									'CPA：按转化效果收费'
								]
							}
						]
					},
					{
						title: '第三章 服务保障',
						numbering: '3.',
						items: [
							{
								title: '数据报告',
								content: '为客户提供详细的投放数据报告，包括展示量、点击率、转化率等关键指标。'
							},
							{
								title: '客户服务',
								content: '提供专业的客户服务支持：',
								subItems: [
									'专属客户经理服务',
									'7×24小时技术支持',
									'定期投放效果分析',
									'优化建议和策略指导'
								]
							}
						]
					}
				]
			};
		},

		// 初始化审核规则
		initializeReviewRules() {
			this.ruleDocuments.review = {
				title: '闲伴内容审核规则',
				subtitle: '智能审核+人工审核，确保内容质量',
				version: 'v3.1',
				publishDate: '2025年6月4日',
				effectiveDate: '2025年6月4日',
				updateTime: '2025年6月4日',
				content: []
			};
		},

		// 初始化解封规则
		initializeUnbanRules() {
			this.ruleDocuments.unban = {
				title: '闲伴账号解封规则',
				subtitle: '给予改过机会，重新融入社区',
				version: 'v2.4',
				publishDate: '2025年6月4日',
				effectiveDate: '2025年6月4日',
				updateTime: '2025年6月4日',
				content: []
			};
		},

		// 初始化创作者协议
		initializeCreatorAgreement() {
			this.ruleDocuments.creator = {
				title: '闲伴创作者服务协议',
				subtitle: '携手共建优质内容生态',
				version: 'v1.8',
				publishDate: '2025年6月4日',
				effectiveDate: '2025年6月4日',
				updateTime: '2025年6月4日',
				content: []
			};
		},

		// 初始化反作弊规则
		initializeAntiFraudRules() {
			this.ruleDocuments.antifraud = {
				title: '闲伴反作弊管理规则',
				subtitle: '维护公平环境，打击违规行为',
				version: 'v2.7',
				publishDate: '2025年6月4日',
				effectiveDate: '2025年6月4日',
				updateTime: '2025年6月4日',
				content: []
			};
		},

		// 初始化青少年模式规则
		initializeYouthModeRules() {
			this.ruleDocuments.youth = {
				title: '闲伴青少年模式规则',
				subtitle: '守护青少年健康成长',
				version: 'v1.5',
				publishDate: '2025年6月4日',
				effectiveDate: '2025年6月4日',
				updateTime: '2025年6月4日',
				content: []
			};
		},

		// 初始化数据保护规则
		initializeDataProtectionRules() {
			this.ruleDocuments.dataprotection = {
				title: '闲伴数据保护规则',
				subtitle: '保护用户隐私，确保数据安全',
				version: 'v3.2',
				publishDate: '2025年6月4日',
				effectiveDate: '2025年6月4日',
				updateTime: '2025年6月4日',
				content: []
			};
		},

		// 初始化组局完全指南
		initializeGroupGuide() {
			this.ruleDocuments.groupguide = {
				title: '闲伴组局完全指南',
				subtitle: '规则·安全·温馨·装备 一站式指南',
				version: 'v3.0',
				publishDate: '2025年6月4日',
				effectiveDate: '2025年6月4日',
				updateTime: '2025年6月4日',
				content: []
			};
		}
	}
}
</script>

<style lang="scss" scoped>
.rules-container {
	background: #f8f9fa;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.status-bar {
	background: #fff;
}

// 顶部导航栏
.top-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16rpx 24rpx;
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	
	.header-left {
		display: flex;
		align-items: center;
		gap: 8rpx;
		
		.back-text {
			font-size: 32rpx;
			color: #333;
		}
	}
	
	.header-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}
	
	.header-right {
		width: 80rpx; // 占位保持居中
	}
}

// 主要内容区域
.main-content {
	flex: 1;
	background: #f8f9fa;
}

.content-wrapper {
	padding: 24rpx;
}

// 页面介绍
.page-intro {
	text-align: center;
	margin-bottom: 32rpx;
	padding: 32rpx 24rpx;
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
	border-radius: 16rpx;

	.intro-title {
		font-size: 40rpx;
		font-weight: 700;
		color: #333;
		display: block;
		margin-bottom: 12rpx;
	}

	.intro-subtitle {
		font-size: 28rpx;
		color: #666;
		display: block;
	}
}

// 文章列表
.article-list {
	.article-item {
		display: flex;
		align-items: center;
		background: #fff;
		border-radius: 16rpx;
		padding: 24rpx;
		margin-bottom: 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
		}

		.article-content {
			flex: 1;

			.article-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
				display: block;
				margin-bottom: 8rpx;
			}

			.article-subtitle {
				font-size: 26rpx;
				color: #666;
				display: block;
				margin-bottom: 12rpx;
				line-height: 1.4;
			}

			.article-meta {
				display: flex;
				align-items: center;
				gap: 16rpx;

				.meta-tag {
					font-size: 22rpx;
					color: #333;
					background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
					padding: 4rpx 12rpx;
					border-radius: 12rpx;
					font-weight: 500;
				}

				.meta-date {
					font-size: 22rpx;
					color: #999;
				}
			}
		}

		.article-arrow {
			margin-left: 16rpx;
		}
	}

	// 政策分区
	.policy-section {
		margin-top: 32rpx;

		.section-title {
			font-size: 30rpx;
			font-weight: 600;
			color: #333;
			display: block;
			margin-bottom: 16rpx;
			padding-left: 12rpx;
			border-left: 4rpx solid #FFD700;
		}

		.policy-item {
			border-left: 4rpx solid #e0e0e0;

			&:hover {
				border-left-color: #FFD700;
			}
		}
	}
}

// 联系方式
.contact-info {
	margin-top: 32rpx;
	padding: 24rpx;
	background: #fff;
	border-radius: 16rpx;
	border-left: 4rpx solid #FFD700;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

	.contact-title {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 16rpx;
	}

	.contact-text {
		font-size: 26rpx;
		color: #666;
		line-height: 1.5;
		display: block;
		margin-bottom: 8rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}
}




</style>
