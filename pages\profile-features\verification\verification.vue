<template>
	<view class="verification-page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="nav-left" @click="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
			</view>
			<view class="nav-title">实名认证</view>
			<view class="nav-right"></view>
		</view>

		<!-- 认证状态卡片 -->
		<view class="status-card">
			<view class="status-icon">
				<uni-icons v-if="verificationStatus === 'verified'" type="checkmarkempty" size="40" color="#4CAF50"></uni-icons>
				<uni-icons v-else-if="verificationStatus === 'pending'" type="spinner-cycle" size="40" color="#FF9800"></uni-icons>
				<uni-icons v-else type="person" size="40" color="#999"></uni-icons>
			</view>
			<view class="status-content">
				<text class="status-title">{{ statusTitle }}</text>
				<text class="status-desc">{{ statusDesc }}</text>
			</view>
		</view>

		<!-- 认证信息 -->
		<view v-if="verificationStatus === 'verified'" class="verified-info">
			<view class="info-item">
				<text class="info-label">姓名</text>
				<text class="info-value">{{ userInfo.realName }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">身份证号</text>
				<text class="info-value">{{ maskedIdCard }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">认证时间</text>
				<text class="info-value">{{ verificationTime }}</text>
			</view>
		</view>

		<!-- 认证表单 -->
		<view v-else class="verification-form">
			<view class="form-section">
				<view class="section-title">
					<uni-icons type="person-filled" size="18" color="#4CAF50"></uni-icons>
					<text>个人信息</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">真实姓名</text>
					<input class="form-input" v-model="formData.realName" placeholder="请输入真实姓名" />
				</view>
				
				<view class="form-item">
					<text class="form-label">身份证号</text>
					<input class="form-input" v-model="formData.idCard" placeholder="请输入身份证号码" />
				</view>
			</view>

			<view class="form-section">
				<view class="section-title">
					<uni-icons type="camera-filled" size="18" color="#2196F3"></uni-icons>
					<text>身份证照片</text>
				</view>
				
				<view class="upload-section">
					<view class="upload-item">
						<view class="upload-card" @click="uploadFrontImage">
							<image v-if="formData.frontImage" :src="formData.frontImage" class="upload-preview"></image>
							<view v-else class="upload-placeholder">
								<uni-icons type="camera" size="30" color="#999"></uni-icons>
								<text class="upload-text">身份证正面</text>
							</view>
						</view>
						<text class="upload-tip">请上传身份证正面照片</text>
					</view>
					
					<view class="upload-item">
						<view class="upload-card" @click="uploadBackImage">
							<image v-if="formData.backImage" :src="formData.backImage" class="upload-preview"></image>
							<view v-else class="upload-placeholder">
								<uni-icons type="camera" size="30" color="#999"></uni-icons>
								<text class="upload-text">身份证反面</text>
							</view>
						</view>
						<text class="upload-tip">请上传身份证反面照片</text>
					</view>
				</view>
			</view>

			<!-- 温馨提示 -->
			<view class="tips-section">
				<view class="tips-title">
					<uni-icons type="info-filled" size="16" color="#FF9800"></uni-icons>
					<text>温馨提示</text>
				</view>
				<view class="tips-content">
					<text class="tip-item">• 请确保身份证照片清晰完整，四角完整可见</text>
					<text class="tip-item">• 照片中的文字信息清晰可读</text>
					<text class="tip-item">• 请使用本人真实身份证件</text>
					<text class="tip-item">• 我们将严格保护您的个人信息安全</text>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<button class="submit-btn" :disabled="!canSubmit" @click="submitVerification">
					{{ verificationStatus === 'pending' ? '审核中...' : '提交认证' }}
				</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0,
			verificationStatus: 'unverified', // unverified, pending, verified
			formData: {
				realName: '',
				idCard: '',
				frontImage: '',
				backImage: ''
			},
			userInfo: {
				realName: '张**',
				idCard: '110101199001011234'
			}
		}
	},
	
	computed: {
		statusTitle() {
			switch(this.verificationStatus) {
				case 'verified': return '认证成功';
				case 'pending': return '审核中';
				default: return '未认证';
			}
		},
		
		statusDesc() {
			switch(this.verificationStatus) {
				case 'verified': return '您的身份信息已通过认证';
				case 'pending': return '您的认证信息正在审核中，请耐心等待';
				default: return '完成实名认证，享受更多服务';
			}
		},
		
		maskedIdCard() {
			const idCard = this.userInfo.idCard;
			return idCard.substring(0, 6) + '********' + idCard.substring(14);
		},
		
		verificationTime() {
			return '2024-01-15 14:30:25';
		},
		
		canSubmit() {
			return this.formData.realName && 
				   this.formData.idCard && 
				   this.formData.frontImage && 
				   this.formData.backImage &&
				   this.verificationStatus !== 'pending';
		}
	},
	
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
		
		// 模拟获取认证状态
		this.loadVerificationStatus();
	},
	
	methods: {
		goBack() {
			uni.navigateBack();
		},
		
		loadVerificationStatus() {
			// 模拟API调用
			// this.verificationStatus = 'verified'; // 测试已认证状态
		},
		
		uploadFrontImage() {
			uni.chooseImage({
				count: 1,
				sourceType: ['camera', 'album'],
				success: (res) => {
					this.formData.frontImage = res.tempFilePaths[0];
				}
			});
		},
		
		uploadBackImage() {
			uni.chooseImage({
				count: 1,
				sourceType: ['camera', 'album'],
				success: (res) => {
					this.formData.backImage = res.tempFilePaths[0];
				}
			});
		},
		
		submitVerification() {
			if (!this.canSubmit) return;
			
			uni.showLoading({
				title: '提交中...'
			});
			
			// 模拟API提交
			setTimeout(() => {
				uni.hideLoading();
				this.verificationStatus = 'pending';
				uni.showToast({
					title: '提交成功，请等待审核',
					icon: 'success'
				});
			}, 2000);
		}
	}
}
</script>

<style lang="scss" scoped>
.verification-page {
	min-height: 100vh;
	background: #f5f5f5;
}

.status-bar {
	background: transparent;
}

/* 导航栏 */
.nav-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 40rpx;
	background: #fff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

/* 状态卡片 */
.status-card {
	margin: 40rpx;
	padding: 60rpx 40rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 32rpx;
	display: flex;
	align-items: center;
	gap: 32rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.status-icon {
	width: 120rpx;
	height: 120rpx;
	background: #f8f9fa;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.status-content {
	flex: 1;
}

.status-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 16rpx;
}

.status-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

/* 已认证信息 */
.verified-info {
	margin: 0 40rpx 40rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.info-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.info-item:last-child {
	border-bottom: none;
}

.info-label {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

.info-value {
	font-size: 30rpx;
	color: #666;
}

/* 认证表单 */
.verification-form {
	padding: 0 40rpx 40rpx;
}

.form-section {
	margin-bottom: 40rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 40rpx;
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.form-item {
	margin-bottom: 32rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
	font-weight: 500;
}

.form-input {
	width: 100%;
	height: 88rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	padding: 0 24rpx;
	font-size: 30rpx;
	color: #333;
	border: 2rpx solid transparent;
	transition: all 0.3s;
}

.form-input:focus {
	background: #fff;
	border-color: #4CAF50;
	box-shadow: 0 0 0 4rpx rgba(76, 175, 80, 0.1);
}

/* 上传区域 */
.upload-section {
	display: flex;
	gap: 32rpx;
}

.upload-item {
	flex: 1;
	text-align: center;
}

.upload-card {
	width: 100%;
	height: 240rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	border: 2rpx dashed #ddd;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 16rpx;
	transition: all 0.3s;
	overflow: hidden;
}

.upload-card:active {
	transform: scale(0.98);
}

.upload-preview {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.upload-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
}

.upload-text {
	font-size: 24rpx;
	color: #999;
}

.upload-tip {
	font-size: 22rpx;
	color: #999;
	line-height: 1.4;
}

/* 温馨提示 */
.tips-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tips-title {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 24rpx;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.tips-content {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.tip-item {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

/* 提交按钮 */
.submit-section {
	padding: 0 40rpx 80rpx;
}

.submit-btn {
	width: 100%;
	height: 96rpx;
	background: linear-gradient(135deg, #4CAF50, #45a049);
	border-radius: 48rpx;
	border: none;
	font-size: 32rpx;
	font-weight: 600;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);
	transition: all 0.3s;
}

.submit-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.submit-btn[disabled] {
	background: #ccc;
	box-shadow: none;
	transform: none;
}
</style>
