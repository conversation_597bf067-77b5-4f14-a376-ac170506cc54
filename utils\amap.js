// 高德地图API工具类
class AmapUtil {
  constructor() {
    this.key = 'e318a539d606974c5f13654e905cf555'; // 从README.md获取的Web端Key
    this.securityKey = 'b2d738d21b1aed6f1ec0d61a9c935a7b'; // 安全密钥
    this.isLoaded = false;
    this.loadPromise = null;
  }

  // 加载高德地图API
  loadAMapAPI() {
    if (this.isLoaded) {
      return Promise.resolve();
    }

    if (this.loadPromise) {
      return this.loadPromise;
    }

    this.loadPromise = new Promise((resolve, reject) => {
      // 只在H5环境中加载
      // #ifdef H5
      if (typeof AMap !== 'undefined') {
        this.isLoaded = true;
        resolve();
        return;
      }

      // 创建script标签加载高德地图API
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = `https://webapi.amap.com/maps?v=1.4.15&key=${this.key}&callback=onAMapLoaded`;
      
      // 设置全局回调函数
      window.onAMapLoaded = () => {
        this.isLoaded = true;
        resolve();
      };

      script.onerror = () => {
        reject(new Error('高德地图API加载失败'));
      };

      document.head.appendChild(script);
      // #endif

      // 非H5环境直接reject
      // #ifndef H5
      reject(new Error('非H5环境不支持高德地图API'));
      // #endif
    });

    return this.loadPromise;
  }

  // 获取当前城市信息
  async getCurrentCity() {
    try {
      await this.loadAMapAPI();
      
      return new Promise((resolve, reject) => {
        // #ifdef H5
        AMap.plugin('AMap.CitySearch', () => {
          const citySearch = new AMap.CitySearch();
          citySearch.getLocalCity((status, result) => {
            if (status === 'complete' && result.info === 'OK') {
              resolve({
                city: result.city,
                province: result.province,
                citycode: result.citycode,
                adcode: result.adcode
              });
            } else {
              reject(new Error('获取城市信息失败'));
            }
          });
        });
        // #endif

        // #ifndef H5
        reject(new Error('非H5环境'));
        // #endif
      });
    } catch (error) {
      throw error;
    }
  }

  // 根据IP获取城市信息
  async getCityByIP(ip) {
    try {
      await this.loadAMapAPI();
      
      return new Promise((resolve, reject) => {
        // #ifdef H5
        AMap.plugin('AMap.CitySearch', () => {
          const citySearch = new AMap.CitySearch();
          citySearch.getCityByIp(ip, (status, result) => {
            if (status === 'complete' && result.info === 'OK') {
              resolve({
                city: result.city,
                province: result.province,
                citycode: result.citycode,
                adcode: result.adcode
              });
            } else {
              reject(new Error('根据IP获取城市信息失败'));
            }
          });
        });
        // #endif

        // #ifndef H5
        reject(new Error('非H5环境'));
        // #endif
      });
    } catch (error) {
      throw error;
    }
  }

  // 地理编码（地址转坐标）
  async geocode(address, city) {
    try {
      await this.loadAMapAPI();
      
      return new Promise((resolve, reject) => {
        // #ifdef H5
        AMap.plugin('AMap.Geocoder', () => {
          const geocoder = new AMap.Geocoder({
            city: city || '全国'
          });
          
          geocoder.getLocation(address, (status, result) => {
            if (status === 'complete' && result.info === 'OK') {
              const geocodes = result.geocodes;
              if (geocodes.length > 0) {
                resolve({
                  location: geocodes[0].location,
                  formattedAddress: geocodes[0].formattedAddress,
                  addressComponent: geocodes[0].addressComponent
                });
              } else {
                reject(new Error('未找到地址'));
              }
            } else {
              reject(new Error('地理编码失败'));
            }
          });
        });
        // #endif

        // #ifndef H5
        reject(new Error('非H5环境'));
        // #endif
      });
    } catch (error) {
      throw error;
    }
  }

  // 逆地理编码（坐标转地址）
  async regeocode(lng, lat) {
    try {
      await this.loadAMapAPI();
      
      return new Promise((resolve, reject) => {
        // #ifdef H5
        AMap.plugin('AMap.Geocoder', () => {
          const geocoder = new AMap.Geocoder();
          const lnglat = new AMap.LngLat(lng, lat);
          
          geocoder.getAddress(lnglat, (status, result) => {
            if (status === 'complete' && result.info === 'OK') {
              const regeocode = result.regeocode;
              resolve({
                formattedAddress: regeocode.formattedAddress,
                addressComponent: regeocode.addressComponent,
                pois: regeocode.pois,
                roads: regeocode.roads,
                crosses: regeocode.crosses
              });
            } else {
              reject(new Error('逆地理编码失败'));
            }
          });
        });
        // #endif

        // #ifndef H5
        reject(new Error('非H5环境'));
        // #endif
      });
    } catch (error) {
      throw error;
    }
  }

  // 浏览器定位
  async getCurrentPosition() {
    try {
      await this.loadAMapAPI();
      
      return new Promise((resolve, reject) => {
        // #ifdef H5
        AMap.plugin('AMap.Geolocation', () => {
          const geolocation = new AMap.Geolocation({
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0,
            convert: true,
            showButton: false,
            showMarker: false,
            showCircle: false,
            panToLocation: false,
            zoomToAccuracy: false,
            extensions: 'all'
          });

          geolocation.getCurrentPosition((status, result) => {
            if (status === 'complete') {
              resolve({
                position: result.position,
                accuracy: result.accuracy,
                location_type: result.location_type,
                message: result.message,
                isConverted: result.isConverted,
                addressComponent: result.addressComponent,
                formattedAddress: result.formattedAddress,
                pois: result.pois || [],
                roads: result.roads || [],
                crosses: result.crosses || []
              });
            } else {
              reject(new Error(result.message || '定位失败'));
            }
          });
        });
        // #endif

        // #ifndef H5
        reject(new Error('非H5环境'));
        // #endif
      });
    } catch (error) {
      throw error;
    }
  }

  // 计算两点间距离
  async getDistance(lng1, lat1, lng2, lat2) {
    try {
      await this.loadAMapAPI();
      
      // #ifdef H5
      const point1 = new AMap.LngLat(lng1, lat1);
      const point2 = new AMap.LngLat(lng2, lat2);
      const distance = point1.distance(point2);
      return Math.round(distance);
      // #endif

      // #ifndef H5
      throw new Error('非H5环境');
      // #endif
    } catch (error) {
      throw error;
    }
  }

  // 格式化距离显示
  formatDistance(distance) {
    if (distance < 1000) {
      return distance + 'm';
    } else if (distance < 10000) {
      return (distance / 1000).toFixed(1) + 'km';
    } else {
      return Math.round(distance / 1000) + 'km';
    }
  }
}

// 创建单例实例
const amapUtil = new AmapUtil();

export default amapUtil;
