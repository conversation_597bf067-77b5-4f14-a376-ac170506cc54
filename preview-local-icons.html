<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地图标预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 50%, #7fcdcd 100%);
            padding: 40px 20px 20px;
            color: white;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        /* 顶部导航区 */
        .top-nav {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 50%, #7fcdcd 100%);
            color: white;
        }
        
        .location-area {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .location-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 4px;
        }
        
        .location-icon-img {
            width: 16px;
            height: 16px;
            filter: brightness(0) invert(1);
        }
        
        .city-text {
            font-size: 16px;
            font-weight: 500;
        }
        
        .search-container {
            flex: 1;
            margin: 0 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .avatar-container {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 标签导航 */
        .tab-navigation {
            padding: 10px 0;
            background: #fff;
            margin: 0;
            border-radius: 0;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
            border-bottom: 1px solid #f0f0f0;
        }

        .tab-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: relative;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 5px;
            position: relative;
            flex: 1;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-text {
            font-size: 14px;
            color: #666;
            font-weight: 400;
            transition: all 0.3s ease;
        }

        .tab-item.active .tab-text {
            color: #333;
            font-size: 16px;
            font-weight: 600;
        }

        .tab-indicator {
            position: absolute;
            bottom: 0px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 10px;
            overflow: hidden;
            animation: fadeInUp 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .tab-indicator::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 20px;
            background: linear-gradient(90deg, #a8e6cf 0%, #88d8a3 50%, #7fcdcd 100%);
            border-radius: 50%;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(5px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0) scale(1);
            }
        }
        
        .content {
            padding: 20px;
            color: #666;
        }
        
        /* 区域样式 */
        .section {
            margin-bottom: 20px;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .section-title-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }
        
        .section-icon-img {
            width: 16px;
            height: 16px;
        }
        
        .section-title {
            font-size: 18px;
            color: #333;
            font-weight: 600;
        }
        
        .section-more {
            font-size: 14px;
            color: #4CAF50;
            cursor: pointer;
        }
        
        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .activity-item {
            display: flex;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            border-left: 4px solid #4CAF50;
        }
        
        .activity-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            margin-right: 15px;
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
        }
        
        .activity-info {
            flex: 1;
        }
        
        .activity-title {
            font-size: 16px;
            color: #333;
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .activity-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .activity-meta {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #999;
        }
        
        .partner-list {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding-bottom: 10px;
        }
        
        .partner-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 80px;
            cursor: pointer;
        }
        
        .partner-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
        }
        
        .partner-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .partner-tags {
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        
        .demo-note {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.5;
            text-align: left;
        }
        
        .icon-comparison {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 20px 0;
        }
        
        .icon-demo {
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e0e0e0;
        }
        
        .icon-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .old-icon {
            background: #fff3e0;
            border-color: #ffcc80;
        }
        
        .new-icon {
            background: #e8f5e8;
            border-color: #a5d6a7;
        }
        
        .old-icon .icon-title {
            color: #f57c00;
        }
        
        .new-icon .icon-title {
            color: #388e3c;
        }
        
        .icon-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        
        .icon-preview {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 10px 0;
            font-size: 16px;
            font-weight: 500;
        }
        
        .icon-path {
            background: #f0f0f0;
            padding: 8px 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin: 5px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>趣嗒同行</h1>
            <p>📁 本地图标预览 📁</p>
        </div>
        
        <!-- 顶部导航区 -->
        <div class="top-nav">
            <div class="location-area">
                <div class="location-icon">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z'/%3E%3C/svg%3E" class="location-icon-img" alt="定位">
                </div>
                <span class="city-text">北海</span>
            </div>
            <div class="search-container">
                搜索活动、搭子、地点...
            </div>
            <div class="avatar-container">
                👤
            </div>
        </div>
        
        <!-- 标签导航 -->
        <div class="tab-navigation">
            <div class="tab-container">
                <div class="tab-item active" onclick="switchTab(this, 0)">
                    <span class="tab-text">首页推荐</span>
                    <div class="tab-indicator"></div>
                </div>
                <div class="tab-item" onclick="switchTab(this, 1)">
                    <span class="tab-text">组局约伴</span>
                </div>
                <div class="tab-item" onclick="switchTab(this, 2)">
                    <span class="tab-text">城市玩伴</span>
                </div>
                <div class="tab-item" onclick="switchTab(this, 3)">
                    <span class="tab-text">游戏玩伴</span>
                </div>
            </div>
        </div>
        
        <div class="content">
            <!-- 热门活动区域 -->
            <div class="section">
                <div class="section-header">
                    <div class="section-title-container">
                        <div class="section-title-icon">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF5722'%3E%3Cpath d='M13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.77-.36 3.6-1.21 4.62-2.58.39 1.29.59 2.65.59 4.04 0 2.65-2.15 4.8-4.8 4.8z'/%3E%3C/svg%3E" class="section-icon-img" alt="热门">
                        </div>
                        <span class="section-title">热门活动</span>
                    </div>
                    <span class="section-more">查看更多</span>
                </div>
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-image"></div>
                        <div class="activity-info">
                            <div class="activity-title">🏕️ 周末露营BBQ</div>
                            <div class="activity-desc">一起来户外露营，享受大自然</div>
                            <div class="activity-meta">
                                <span>📍 银滩</span>
                                <span>⏰ 明天 18:00</span>
                                <span style="color: #4CAF50;">👥 6人参与</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 推荐搭子区域 -->
            <div class="section">
                <div class="section-header">
                    <div class="section-title-container">
                        <div class="section-title-icon">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234CAF50'%3E%3Cpath d='M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2.01.99l-2.54 3.4c-.74.99-.74 2.31 0 3.3l1.54 2.05v2.26h2zm-7.5-10.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10.5s.67 1.5 1.5 1.5zM5.5 6c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2zm2 16v-7H9l-1.5-4.5L6 14H4.5l1.5-4.5L4.5 6 3 9.5 1.5 6 0 9.5 1.5 14H0v7h7.5z'/%3E%3C/svg%3E" class="section-icon-img" alt="朋友">
                        </div>
                        <span class="section-title">推荐搭子</span>
                    </div>
                    <span class="section-more">查看更多</span>
                </div>
                <div class="partner-list">
                    <div class="partner-item">
                        <div class="partner-avatar"></div>
                        <div class="partner-name">户外小王</div>
                        <div class="partner-tags">露营 · 徒步</div>
                    </div>
                    <div class="partner-item">
                        <div class="partner-avatar"></div>
                        <div class="partner-name">旅行小美</div>
                        <div class="partner-tags">旅游 · 摄影</div>
                    </div>
                    <div class="partner-item">
                        <div class="partner-avatar"></div>
                        <div class="partner-name">骑行阿强</div>
                        <div class="partner-tags">骑行 · 运动</div>
                    </div>
                </div>
            </div>
            
            <!-- 图标对比 -->
            <div class="icon-comparison">
                <div class="icon-demo old-icon">
                    <div class="icon-title">❌ 之前：uni-icons组件</div>
                    <div class="icon-desc">
                        使用uni-icons组件，依赖字体文件<br>
                        可能在某些设备上加载失败
                    </div>
                </div>
                
                <div class="icon-demo new-icon">
                    <div class="icon-title">✅ 现在：本地PNG图标</div>
                    <div class="icon-desc">
                        使用本地图片文件，加载更稳定<br>
                        支持自定义设计，视觉效果更佳
                    </div>
                    <div class="icon-path">/static/home/<USER>/div>
                    <div class="icon-path">/static/home/<USER>/div>
                    <div class="icon-path">/static/home/<USER>/div>
                </div>
            </div>
            
            <div class="demo-note">
                <strong>📁 本地图标更新完成：</strong><br>
                • <strong>热门活动</strong>：/static/home/<USER>
                • <strong>推荐搭子</strong>：/static/home/<USER>
                • <strong>定位图标</strong>：/static/home/<USER>
                • <strong>图标尺寸</strong>：32rpx × 32rpx<br>
                • <strong>加载模式</strong>：mode="aspectFit"<br>
                • <strong>动画效果</strong>：保持脉冲动画<br>
                • <strong>优势</strong>：加载稳定，支持自定义设计
            </div>
        </div>
    </div>

    <script>
        const tabNames = ['首页推荐', '组局约伴', '城市玩伴', '游戏玩伴'];
        
        function switchTab(element, index) {
            // 移除所有active状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
                const indicator = item.querySelector('.tab-indicator');
                if (indicator) {
                    indicator.remove();
                }
            });
            
            // 添加active状态到当前选中的标签
            element.classList.add('active');
            
            // 添加指示器
            const indicator = document.createElement('div');
            indicator.className = 'tab-indicator';
            element.appendChild(indicator);
        }
    </script>
</body>
</html>
