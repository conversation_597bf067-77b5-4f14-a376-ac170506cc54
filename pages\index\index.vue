<template>
	<view class="home-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<!-- 自定义下拉刷新 -->
		<view class="custom-refresh" v-if="refreshing">
			<view class="refresh-container">
				<view class="irregular-shapes-animation">
					<view class="shape-item shape-1"></view>
					<view class="shape-item shape-2"></view>
					<view class="shape-item shape-3"></view>
					<view class="shape-item shape-4"></view>
					<view class="shape-item shape-5"></view>
					<view class="shape-item shape-6"></view>
				</view>
				<text class="refresh-text">正在刷新...</text>
			</view>
		</view>

		<!-- 主内容滚动区域 -->
		<scroll-view class="main-scroll" scroll-y @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
			<!-- 现代化顶部导航区 -->
			<view class="top-nav">
				<view class="location-area" @click="onLocationClick">
					<view class="location-icon">
						<image src="/static/home/<USER>" class="location-icon-img" mode="aspectFit"></image>
					</view>
					<text class="city-text">{{ currentCity }}</text>
					<view class="dropdown-icon">
						<uni-icons type="arrowdown" size="12" color="#666"></uni-icons>
					</view>
				</view>

				<view class="search-container" @click="goToSearch">
					<view class="search-icon">
						<uni-icons type="search" size="16" color="#999"></uni-icons>
					</view>
					<text class="search-placeholder">搜索活动、搭子、地点...</text>
				</view>

				<view class="avatar-container" @click="onAvatarClick">
					<image :src="currentAvatarSrc" class="avatar-img" @error="onAvatarError" @load="onAvatarLoad"></image>
				</view>
			</view>

			<!-- 顶部标签导航 - 全屏贴合 -->
			<view class="tab-navigation">
				<view class="tab-container">
					<view
						class="tab-item"
						:class="{ active: activeTab === 0 }"
						@click="switchTab(0)"
					>
						<text class="tab-text">首页推荐</text>
						<view class="tab-indicator" v-if="activeTab === 0">
							<image src="/static/home/<USER>" class="tab-indicator-img" mode="aspectFit"></image>
						</view>
					</view>
					<view
						class="tab-item"
						:class="{ active: activeTab === 1 }"
						@click="switchTab(1)"
					>
						<text class="tab-text">组局约伴</text>
						<view class="tab-indicator" v-if="activeTab === 1">
							<image src="/static/home/<USER>" class="tab-indicator-img" mode="aspectFit"></image>
						</view>
					</view>
					<view
						class="tab-item"
						:class="{ active: activeTab === 2 }"
						@click="switchTab(2)"
					>
						<text class="tab-text">城市玩伴</text>
						<view class="tab-indicator" v-if="activeTab === 2">
							<image src="/static/home/<USER>" class="tab-indicator-img" mode="aspectFit"></image>
						</view>
					</view>
					<view
						class="tab-item"
						:class="{ active: activeTab === 3 }"
						@click="switchTab(3)"
					>
						<text class="tab-text">游戏玩伴</text>
						<view class="tab-indicator" v-if="activeTab === 3">
							<image src="/static/home/<USER>" class="tab-indicator-img" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</view>

			<!-- 轮播横幅 -->
			<view class="banner-section">
				<swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500" indicator-color="rgba(0,0,0,0.3)" indicator-active-color="#66D4C8">
					<swiper-item v-for="(banner, index) in banners" :key="index">
						<view class="banner-item">
							<image :src="banner.image" class="banner-bg-image" mode="aspectFill"></image>
						</view>
					</swiper-item>
				</swiper>
			</view>

			<!-- 简洁公告栏 -->
			<view class="notice-section" v-if="showNotice">
				<view class="notice-container">
					<view class="notice-icon">
						<image src="/static/home/<USER>" class="notice-icon-img" mode="aspectFit"></image>
					</view>
					<view class="notice-content">
						<text class="notice-text">{{ currentNotice || '欢迎来到趣嗒同行，发现身边有趣的人和事' }}</text>
					</view>
					<view class="notice-close" @click="hideNotice">
						<uni-icons type="clear" size="14" color="#999"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 热门活动区域 -->
			<view class="hot-activities">
				<view class="section-header">
					<view class="section-title-container">
						<view class="section-title-icon">
							<image src="/static/home/<USER>" class="section-icon-img" mode="aspectFit"></image>
						</view>
						<text class="section-title">热门活动</text>
					</view>
					<text class="section-more" @click="viewAllActivities">查看更多</text>
				</view>
				<view class="activity-list">
					<view class="activity-item" v-for="(activity, index) in hotActivities" :key="index" @click="viewActivity(activity)">
						<image :src="activity.image" class="activity-image" mode="aspectFill"></image>
						<view class="activity-info">
							<text class="activity-title">{{ activity.title }}</text>
							<text class="activity-desc">{{ activity.desc }}</text>
							<view class="activity-meta">
								<text class="activity-time">{{ activity.time }}</text>
								<text class="activity-participants">{{ activity.participants }}人参与</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 推荐搭子区域 -->
			<view class="recommended-partners">
				<view class="section-header">
					<view class="section-title-container">
						<view class="section-title-icon">
							<image src="/static/home/<USER>" class="section-icon-img" mode="aspectFit"></image>
						</view>
						<text class="section-title">推荐搭子</text>
					</view>
					<text class="section-more" @click="viewAllPartners">查看更多</text>
				</view>
				<view class="partner-list">
					<view class="partner-item" v-for="(partner, index) in recommendedPartners" :key="index" @click="viewPartner(partner)">
						<image :src="partner.avatar" class="partner-avatar" mode="aspectFill"></image>
						<text class="partner-name">{{ partner.name }}</text>
						<text class="partner-tags">{{ partner.tags.join(' · ') }}</text>
					</view>
				</view>
			</view>

			<!-- 广告banner -->
			<view class="ad-banner-section">
				<image src="/static/banner/one.png" class="ad-banner-img" mode="widthFix" @click="onAdClick"></image>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		name: 'IndexPage',
		data() {
			return {
				currentCity: '北海',
				statusBarHeight: 0,
				searchKeyword: '',
				refreshing: false,
				touchStartY: 0,
				scrollTop: 0,
				locationPermissionGranted: false,
				showDeviceInfoPopup: false,
				showNotice: true,
				currentNotice: '欢迎来到趣嗒同行，发现身边有趣的人和事',

				// 标签导航
				activeTab: 0, // 默认选中首页推荐

				// 用户头像相关
				currentAvatarSrc: '/static/default-avatar.jpg',
				isLoggedIn: false,
				userInfo: {},

				// 轮播横幅数据
				banners: [
					{
						image: '/static/images/banners/banner1.jpg'
					},
					{
						image: '/static/images/banners/banner2.jpg'
					},
					{
						image: '/static/images/banners/banner3.jpg'
					},
					{
						image: '/static/images/banners/banner4.jpg'
					}
				],

				// 热门活动数据
				hotActivities: [
					{
						id: 1,
						title: '周末海边BBQ',
						desc: '一起来海边烧烤，享受阳光沙滩',
						image: '/static/event1.jpg',
						time: '今天 14:00',
						participants: 12
					},
					{
						id: 2,
						title: '王者荣耀五排',
						desc: '寻找靠谱队友，冲击王者',
						image: '/static/event2.jpg',
						time: '明天 20:00',
						participants: 4
					}
				],

				// 推荐搭子数据
				recommendedPartners: [
					{
						id: 1,
						name: '小阳哥',
						avatar: '/static/avatar1.jpg',
						tags: ['游戏', '运动', '摄影']
					},
					{
						id: 2,
						name: '萌萌',
						avatar: '/static/avatar2.jpg',
						tags: ['美食', '旅行', '电影']
					},
					{
						id: 3,
						name: '阿强',
						avatar: '/static/avatar3.jpg',
						tags: ['健身', '音乐', '读书']
					}
				]
			}
		},

		onLoad() {
			// 获取系统信息
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.statusBarHeight);
				}
			});

			// 确保刷新状态为false
			this.refreshing = false;
			console.log('首页加载完成，刷新状态:', this.refreshing);
		},

		onShow() {
			const selectedCity = uni.getStorageSync('selectedCity');
			if (selectedCity) {
				this.currentCity = selectedCity;
			}
			console.log('首页显示，当前城市:', this.currentCity);
			
			// 初始化用户头像
			this.initUserAvatar();
		},

		methods: {
			// 切换标签
			switchTab(index) {
				this.activeTab = index;
				// 这里可以添加切换标签后的逻辑，比如加载不同的内容
				console.log('切换到标签:', index);
			},

			// 初始化用户头像
			initUserAvatar() {
				console.log('🖼️ 首页初始化用户头像');

				// 获取存储的用户信息
				const userInfo = uni.getStorageSync('userInfo');
				const token = uni.getStorageSync('token');
				const isLoggedIn = uni.getStorageSync('isLoggedIn');

				console.log('🏠 首页初始化用户头像');
				console.log('📋 首页获取的用户信息:', userInfo);
				console.log('🔑 首页获取的token:', token);
				console.log('🔐 首页获取的登录状态:', isLoggedIn);

				if (userInfo && token && isLoggedIn) {
					this.isLoggedIn = true;
					this.userInfo = { ...userInfo };

					console.log('✅ 首页设置登录状态为true');
					console.log('📋 首页this.userInfo:', this.userInfo);

					// 设置头像 - 确保路径正确
					if (userInfo.avatar && userInfo.avatar !== '' && !userInfo.avatar.includes('default-avatar')) {
						this.currentAvatarSrc = userInfo.avatar;
						console.log('🖼️ 首页使用用户头像:', this.currentAvatarSrc);
					} else {
						this.currentAvatarSrc = '/static/default-avatar.jpg';
						console.log('🖼️ 首页使用默认头像:', this.currentAvatarSrc);
					}
				} else {
					this.isLoggedIn = false;
					this.userInfo = {};
					this.currentAvatarSrc = '/static/default-avatar.jpg';
					console.log('🖼️ 首页未登录，使用默认头像');
				}
			},

			// 头像加载错误处理
			onAvatarError(e) {
				console.log('🖼️ 首页头像加载失败:', this.currentAvatarSrc);
				this.currentAvatarSrc = '/static/default-avatar.jpg';
			},

			// 头像加载成功处理
			onAvatarLoad(e) {
				console.log('🖼️ 首页头像加载成功:', this.currentAvatarSrc);
			},

			// 隐藏公告栏
			hideNotice() {
				this.showNotice = false;
			},

			// 功能跳转方法
			goToGroupActivity() {
				console.log('跳转到组局约伴');
				uni.navigateTo({
					url: '/pages/category/category?categoryId=1&categoryTitle=组局约伴'
				});
			},

			goToCityPartner() {
				console.log('跳转到城市玩伴');
				uni.navigateTo({
					url: '/pages/category/category?categoryId=3&categoryTitle=城市玩伴'
				});
			},

			goToGamePartner() {
				console.log('跳转到游戏玩伴');
				uni.navigateTo({
					url: '/pages/category/category?categoryId=2&categoryTitle=游戏玩伴'
				});
			},

			// 其他方法
			onLocationClick() {
				console.log('点击定位');
				uni.navigateTo({
					url: '/pages/city-select/city-select'
				});
			},

			goToSearch() {
				console.log('跳转到搜索');
				uni.navigateTo({
					url: '/pages/search/search'
				});
			},

			onAvatarClick() {
				console.log('点击头像');
				uni.switchTab({
					url: '/pages/profile/profile'
				});
			},

			// 下拉刷新相关
			onTouchStart(e) {
				this.touchStartY = e.touches[0].clientY;
				this.scrollTop = 0;
			},

			onTouchMove(e) {
				const currentY = e.touches[0].clientY;
				const deltaY = currentY - this.touchStartY;

				// 只有在页面顶部且向下拉时才触发刷新
				if (this.scrollTop <= 0 && deltaY > 0) {
					if (deltaY > 100 && !this.refreshing) {
						this.triggerRefresh();
					}
				}
			},

			onTouchEnd(e) {
				this.touchStartY = 0;
			},

			// 触发刷新
			triggerRefresh() {
				this.refreshing = true;
				// 模拟刷新数据
				setTimeout(() => {
					this.refreshing = false;
					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					});
				}, 2000);
			},

			// 查看活动
			viewActivity(activity) {
				console.log('查看活动:', activity.title);
			},

			// 查看搭子
			viewPartner(partner) {
				console.log('查看搭子:', partner.name);
			},

			// 查看更多
			viewAllActivities() {
				console.log('查看所有活动');
			},

			viewAllPartners() {
				console.log('查看所有搭子');
			},

			// 广告点击
			onAdClick() {
				console.log('点击广告banner');
				// 这里可以添加广告跳转逻辑
				uni.showToast({
					title: '广告点击',
					icon: 'none',
					duration: 1500
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
.home-container {
	background-color: #ffffff;
	min-height: 100vh;
}

.status-bar {
	background-color: #ffffff;
}

/* 下拉刷新样式 */
.custom-refresh {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10px);
}

.refresh-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 0;
}

/* 多色系不规则图形刷新动画 */
.irregular-shapes-animation {
	position: relative;
	width: 120rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.shape-item {
	position: absolute;
	border-radius: 50%;
	animation-duration: 1.5s;
	animation-iteration-count: infinite;
	animation-timing-function: ease-in-out;
}

.shape-1 {
	width: 16rpx;
	height: 16rpx;
	background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
	top: 10rpx;
	left: 20rpx;
	animation-name: bounce1;
	animation-delay: 0s;
	border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
}

.shape-2 {
	width: 20rpx;
	height: 20rpx;
	background: linear-gradient(45deg, #4ECDC4, #44A08D);
	top: 5rpx;
	left: 50rpx;
	animation-name: bounce2;
	animation-delay: 0.2s;
	border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
}

.shape-3 {
	width: 14rpx;
	height: 14rpx;
	background: linear-gradient(45deg, #A8EDEA, #FED6E3);
	top: 15rpx;
	left: 80rpx;
	animation-name: bounce3;
	animation-delay: 0.4s;
	border-radius: 40% 60% 60% 40% / 40% 40% 60% 60%;
}

.shape-4 {
	width: 18rpx;
	height: 18rpx;
	background: linear-gradient(45deg, #a8e6cf, #88d8a3);
	top: 25rpx;
	left: 30rpx;
	animation-name: bounce1;
	animation-delay: 0.6s;
	border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
}

.shape-5 {
	width: 12rpx;
	height: 12rpx;
	background: linear-gradient(45deg, #F093FB, #F5576C);
	top: 30rpx;
	left: 60rpx;
	animation-name: bounce2;
	animation-delay: 0.8s;
	border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
}

.shape-6 {
	width: 16rpx;
	height: 16rpx;
	background: linear-gradient(45deg, #4FACFE, #00F2FE);
	top: 8rpx;
	left: 90rpx;
	animation-name: bounce3;
	animation-delay: 1s;
	border-radius: 30% 70% 40% 60% / 30% 40% 60% 70%;
}

@keyframes bounce1 {
	0%, 100% {
		transform: translateY(0) scale(1) rotate(0deg);
		opacity: 0.8;
	}
	50% {
		transform: translateY(-20rpx) scale(1.2) rotate(180deg);
		opacity: 1;
	}
}

@keyframes bounce2 {
	0%, 100% {
		transform: translateY(0) scale(1) rotate(0deg);
		opacity: 0.7;
	}
	50% {
		transform: translateY(-15rpx) scale(1.1) rotate(-180deg);
		opacity: 1;
	}
}

@keyframes bounce3 {
	0%, 100% {
		transform: translateY(0) scale(1) rotate(0deg);
		opacity: 0.9;
	}
	50% {
		transform: translateY(-25rpx) scale(1.3) rotate(360deg);
		opacity: 1;
	}
}

.refresh-text {
	font-size: 24rpx;
	color: #666;
	margin-top: 10rpx;
}

/* 主滚动区域 */
.main-scroll {
	height: 100vh;
	background-color: #ffffff;
}

/* 顶部导航 */
.top-nav {
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 50%, #5DD5C9 100%);
	border-bottom: 1rpx solid #f5f5f5;
}

.location-area {
	display: flex;
	align-items: center;
	margin-right: 20rpx;
}

.location-icon {
	margin-right: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.location-icon-img {
	width: 32rpx;
	height: 32rpx;
}

.city-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.dropdown-icon {
	margin-left: 8rpx;
}

.search-container {
	flex: 1;
	display: flex;
	align-items: center;
	background-color: #f8f8f8;
	border-radius: 50rpx;
	padding: 16rpx 24rpx;
	margin: 0 20rpx;
}

.search-icon {
	margin-right: 12rpx;
}

.search-placeholder {
	font-size: 26rpx;
	color: #999;
}

.avatar-container {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	overflow: hidden;
}

.avatar-img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

/* 标签导航 - 全屏贴合 */
.tab-navigation {
	padding: 20rpx 0;
	background: #fff;
	margin: 0;
	border-radius: 0;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-container {
	display: flex;
	justify-content: space-around;
	align-items: center;
	position: relative;
}

.tab-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 10rpx;
	position: relative;
	flex: 1;
	cursor: pointer;
	transition: all 0.3s ease;
}

.tab-text {
	font-size: 28rpx;
	color: #666;
	font-weight: 400;
	transition: all 0.3s ease;
}

.tab-item.active .tab-text {
	color: #333;
	font-size: 32rpx;
	font-weight: 600;
}

.tab-indicator {
	position: absolute;
	bottom: 0rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 60rpx;
	height: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: fadeInUp 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.tab-indicator-img {
	width: 100%;
	height: 100%;
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateX(-50%) translateY(10rpx) scale(0.8);
	}
	to {
		opacity: 1;
		transform: translateX(-50%) translateY(0) scale(1);
	}
}

/* 轮播横幅 */
.banner-section {
	margin: 0 30rpx 30rpx;
}

.banner-swiper {
	height: 320rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.banner-item {
	width: 100%;
	height: 100%;
}

.banner-bg-image {
	width: 100%;
	height: 100%;
	border-radius: 20rpx;
}

/* 公告栏 */
.notice-section {
	margin: 0 30rpx 30rpx;
}

.notice-container {
	display: flex;
	align-items: center;
	background-color: #f8f9ff;
	border-radius: 16rpx;
	padding: 20rpx 24rpx;
	border-left: 6rpx solid #66D4C8;
}

.notice-icon {
	margin-right: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.notice-icon-img {
	width: 32rpx;
	height: 32rpx;
	object-fit: contain;
}

.notice-content {
	flex: 1;
}

.notice-text {
	font-size: 26rpx;
	color: #333;
	line-height: 1.4;
}

.notice-close {
	margin-left: 16rpx;
	padding: 8rpx;
}

/* 热门活动区域 */
.hot-activities {
	margin: 0 30rpx 40rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.section-title-container {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.section-title-icon {
	display: flex;
	align-items: center;
	justify-content: center;
}

.section-icon-img {
	width: 32rpx;
	height: 32rpx;
}



.section-title {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
}

.section-more {
	font-size: 24rpx;
	color: #66D4C8;
}

.activity-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.activity-item {
	display: flex;
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.activity-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
}

.activity-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.activity-title {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.activity-desc {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 12rpx;
}

.activity-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.activity-time {
	font-size: 22rpx;
	color: #999;
}

.activity-participants {
	font-size: 22rpx;
	color: #66D4C8;
}

/* 推荐搭子区域 */
.recommended-partners {
	margin: 0 30rpx 40rpx;
}

.partner-list {
	display: flex;
	gap: 20rpx;
	overflow-x: auto;
	padding-bottom: 10rpx;
}

.partner-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 24rpx 20rpx;
	min-width: 160rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.partner-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-bottom: 12rpx;
}

.partner-name {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.partner-tags {
	font-size: 22rpx;
	color: #666;
	text-align: center;
}

/* 广告banner */
.ad-banner-section {
	margin: 0 auto 30rpx;
	padding: 0 30rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
}

.ad-banner-img {
	max-width: 80%;
	height: auto;
	border-radius: 12rpx;
	transition: transform 0.2s ease;
	cursor: pointer;
	display: block;
	margin: 0 auto;
}

.ad-banner-img:hover {
	transform: scale(1.02);
}

.ad-banner-img:active {
	transform: scale(0.98);
}
</style>
