<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲伴 - 我的页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .profile-container {
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .profile-header {
            position: relative;
            background:
                radial-gradient(ellipse 400px 300px at 20% 10%, rgba(255, 248, 220, 0.8) 0%, transparent 50%),
                radial-gradient(ellipse 300px 400px at 80% 70%, rgba(255, 228, 196, 0.6) 0%, transparent 50%),
                linear-gradient(135deg, #FFF8DC 0%, #F5DEB3 40%, #DEB887 100%);
            margin-bottom: 8px;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: transparent;
        }
        
        .header-decorations {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }
        
        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .circle-1 {
            width: 60px;
            height: 60px;
            top: 30px;
            right: 20px;
            animation: float 6s ease-in-out infinite;
        }
        
        .circle-2 {
            width: 40px;
            height: 40px;
            top: 90px;
            left: 30px;
            animation: float 8s ease-in-out infinite reverse;
        }
        
        .circle-3 {
            width: 30px;
            height: 30px;
            top: 60px;
            left: 100px;
            animation: float 7s ease-in-out infinite;
        }
        
        .decoration-wave {
            position: absolute;
            bottom: -10px;
            left: 0;
            right: 0;
            height: 20px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50% 50% 0 0 / 100% 100% 0 0;
            transform: scaleX(1.5);
        }
        
        .profile-content {
            position: relative;
            z-index: 2;
        }
        
        .profile-info {
            padding: 20px 12px 12px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }
        
        .avatar-section {
            position: relative;
            flex-shrink: 0;
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            border: 2px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            object-fit: cover;
        }
        
        .edit-avatar-btn {
            position: absolute;
            bottom: 3px;
            right: 3px;
            width: 16px;
            height: 16px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(255, 255, 255, 0.8);
            color: white;
            font-size: 10px;
        }
        
        .user-details {
            flex: 1;
            min-width: 0;
        }
        
        .user-name {
            font-size: 19px;
            font-weight: 700;
            color: #fff;
            display: block;
            margin-bottom: 4px;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        }
        
        .user-id {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.9);
            display: block;
            margin-bottom: 6px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
        
        .user-desc {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.95);
            line-height: 1.4;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
        
        .profile-actions {
            display: flex;
            align-items: center;
            gap: 6px;
            flex-shrink: 0;
        }
        
        .edit-profile-btn {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 6px 10px;
            display: flex;
            align-items: center;
            gap: 4px;
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .edit-profile-btn:active {
            background: rgba(255, 255, 255, 0.8);
            transform: scale(0.95);
        }
        
        .edit-text {
            font-size: 12px;
            color: #333;
            font-weight: 600;
        }
        
        .more-menu-btn {
            width: 22px;
            height: 22px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 11px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .more-menu-btn:active {
            background: rgba(255, 255, 255, 0.8);
            transform: scale(0.95);
        }
        
        .stats-section {
            padding: 12px;
            display: flex;
            justify-content: space-around;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(5px);
            margin: 0 12px 12px;
            border-radius: 10px;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .stat-item:active {
            transform: scale(0.95);
        }
        
        .stat-number {
            font-size: 16px;
            font-weight: 700;
            color: #fff;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        }
        
        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }
        
        .demo-content {
            padding: 20px;
            background: white;
            margin: 8px;
            border-radius: 10px;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="profile-container">
        <div class="profile-header">
            <div class="status-bar"></div>
            
            <div class="header-decorations">
                <div class="decoration-circle circle-1"></div>
                <div class="decoration-circle circle-2"></div>
                <div class="decoration-circle circle-3"></div>
                <div class="decoration-wave"></div>
            </div>
            
            <div class="profile-content">
                <div class="profile-info">
                    <div class="avatar-section">
                        <img class="user-avatar" src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="用户头像">
                        <div class="edit-avatar-btn">📷</div>
                    </div>
                    <div class="user-details">
                        <span class="user-name">闲伴用户</span>
                        <span class="user-id">ID: 1001</span>
                        <span class="user-desc">热爱生活，享受每一个美好时刻 ✨</span>
                    </div>
                    <div class="profile-actions">
                        <button class="edit-profile-btn">
                            <span>✏️</span>
                            <span class="edit-text">编辑</span>
                        </button>
                        <button class="more-menu-btn">⋯</button>
                    </div>
                </div>

                <div class="stats-section">
                    <div class="stat-item">
                        <span class="stat-number">128</span>
                        <span class="stat-label">关注</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">256</span>
                        <span class="stat-label">粉丝</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1024</span>
                        <span class="stat-label">获赞</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">32</span>
                        <span class="stat-label">作品</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-content">
            <h3>✨ 重构完成的个人信息栏预览</h3>
            <p>• 从状态栏开始的完整渐变背景</p>
            <p>• 所有个人信息都在渐变区域内</p>
            <p>• 添加了生动的装饰动画</p>
            <p>• 大厂级别的高级感设计</p>
        </div>
    </div>
</body>
</html>
