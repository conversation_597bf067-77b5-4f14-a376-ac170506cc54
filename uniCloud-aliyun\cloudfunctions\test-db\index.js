'use strict';

exports.main = async (event, context) => {
	console.log('test-db 云函数被调用，参数:', event);

	const { action } = event;

	try {
		const db = uniCloud.database();

		switch (action) {
			case 'test-connection':
				return await testConnection(db);
			case 'test-insert':
				return await testInsert(db);
			case 'cleanup-test':
				return await cleanupTestData(db);
			default:
				return await testConnection(db);
		}

	} catch (error) {
		console.error('数据库测试失败:', error);
		return {
			code: -1,
			message: '数据库测试失败',
			error: error.message,
			stack: error.stack
		};
	}
};

// 测试数据库连接
async function testConnection(db) {
	try {
		const userCollection = db.collection('uni-id-users');

		// 测试基本查询
		const result = await userCollection.limit(1).get();

		// 获取数据库统计信息
		const stats = await userCollection.count();

		return {
			code: 0,
			message: '数据库连接测试成功',
			data: {
				connected: true,
				userCount: stats.total,
				sampleData: result.data,
				timestamp: new Date().toISOString()
			}
		};
	} catch (error) {
		return {
			code: -1,
			message: '数据库连接测试失败',
			error: error.message
		};
	}
}

// 测试数据插入
async function testInsert(db) {
	try {
		const userCollection = db.collection('uni-id-users');

		// 查询现有用户数量
		const beforeCount = await userCollection.count();

		// 创建测试用户
		const testUser = {
			username: 'test_user_' + Date.now(),
			mobile: '13800000000',
			password: 'test123456',
			nickname: '测试用户',
			planet_id: 'TEST' + Math.random().toString(36).substring(2, 9).toUpperCase(),
			avatar: '/static/default-avatar.png',
			register_date: new Date(),
			test_flag: true // 标记为测试数据
		};

		// 插入测试用户
		const insertResult = await userCollection.add(testUser);

		// 查询插入后的用户数量
		const afterCount = await userCollection.count();

		return {
			code: 0,
			message: '数据库插入测试成功',
			data: {
				beforeCount: beforeCount.total,
				afterCount: afterCount.total,
				insertResult: insertResult,
				testUser: testUser
			}
		};
	} catch (error) {
		return {
			code: -1,
			message: '数据库插入测试失败',
			error: error.message
		};
	}
}

// 清理测试数据
async function cleanupTestData(db) {
	try {
		const userCollection = db.collection('uni-id-users');

		// 删除所有测试用户
		const deleteResult = await userCollection.where({
			test_flag: true
		}).remove();

		return {
			code: 0,
			message: '测试数据清理完成',
			data: {
				deletedCount: deleteResult.deleted,
				timestamp: new Date().toISOString()
			}
		};
	} catch (error) {
		return {
			code: -1,
			message: '测试数据清理失败',
			error: error.message
		};
	}
}
