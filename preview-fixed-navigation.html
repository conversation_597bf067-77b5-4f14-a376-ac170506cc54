<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后的标签导航预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 20px 20px;
            color: white;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        /* 标签导航 */
        .tab-navigation {
            padding: 10px 0;
            background: #fff;
            margin: 10px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        }

        .tab-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: relative;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 5px;
            position: relative;
            flex: 1;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-text {
            font-size: 14px;
            color: #666;
            font-weight: 400;
            transition: all 0.3s ease;
        }

        .tab-item.active .tab-text {
            color: #333;
            font-size: 16px;
            font-weight: 600;
        }

        /* 半弧椭圆形指示器 */
        .tab-indicator {
            position: absolute;
            bottom: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 10px;
            background: linear-gradient(90deg, #BBB2FF 0%, #E3D2FF 50%, #A7C0FF 100%);
            border-radius: 50%;
            clip-path: ellipse(20px 5px at 50% 100%);
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                width: 0;
                opacity: 0;
            }
            to {
                width: 40px;
                opacity: 1;
            }
        }
        
        .content {
            padding: 20px;
            text-align: center;
            color: #666;
        }
        
        .demo-note {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.5;
            text-align: left;
        }
        
        .fix-note {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            text-align: left;
        }
        
        .refresh-demo-container {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            text-align: center;
        }
        
        .refresh-button {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            margin-bottom: 15px;
        }
        
        .refresh-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        /* 多色系不规则图形刷新动画 */
        .refresh-animation {
            position: relative;
            width: 60px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px auto;
        }

        .shape-item {
            position: absolute;
            border-radius: 50%;
            animation-duration: 1.5s;
            animation-iteration-count: infinite;
            animation-timing-function: ease-in-out;
        }

        .shape-1 {
            width: 8px;
            height: 8px;
            background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
            top: 5px;
            left: 10px;
            animation-name: bounce1;
            animation-delay: 0s;
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
        }

        .shape-2 {
            width: 10px;
            height: 10px;
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            top: 2px;
            left: 25px;
            animation-name: bounce2;
            animation-delay: 0.2s;
            border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
        }

        .shape-3 {
            width: 7px;
            height: 7px;
            background: linear-gradient(45deg, #A8EDEA, #FED6E3);
            top: 8px;
            left: 40px;
            animation-name: bounce3;
            animation-delay: 0.4s;
            border-radius: 40% 60% 60% 40% / 40% 40% 60% 60%;
        }

        .shape-4 {
            width: 9px;
            height: 9px;
            background: linear-gradient(45deg, #667EEA, #764BA2);
            top: 12px;
            left: 15px;
            animation-name: bounce1;
            animation-delay: 0.6s;
            border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
        }

        .shape-5 {
            width: 6px;
            height: 6px;
            background: linear-gradient(45deg, #F093FB, #F5576C);
            top: 15px;
            left: 30px;
            animation-name: bounce2;
            animation-delay: 0.8s;
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
        }

        .shape-6 {
            width: 8px;
            height: 8px;
            background: linear-gradient(45deg, #4FACFE, #00F2FE);
            top: 4px;
            left: 45px;
            animation-name: bounce3;
            animation-delay: 1s;
            border-radius: 30% 70% 40% 60% / 30% 40% 60% 70%;
        }

        @keyframes bounce1 {
            0%, 100% {
                transform: translateY(0) scale(1) rotate(0deg);
                opacity: 0.8;
            }
            50% {
                transform: translateY(-10px) scale(1.2) rotate(180deg);
                opacity: 1;
            }
        }

        @keyframes bounce2 {
            0%, 100% {
                transform: translateY(0) scale(1) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-8px) scale(1.1) rotate(-180deg);
                opacity: 1;
            }
        }

        @keyframes bounce3 {
            0%, 100% {
                transform: translateY(0) scale(1) rotate(0deg);
                opacity: 0.9;
            }
            50% {
                transform: translateY(-12px) scale(1.3) rotate(360deg);
                opacity: 1;
            }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>趣嗒同行</h1>
            <p>修复后的标签导航</p>
        </div>
        
        <!-- 标签导航 -->
        <div class="tab-navigation">
            <div class="tab-container">
                <div class="tab-item active" onclick="switchTab(this, 0)">
                    <span class="tab-text">首页推荐</span>
                    <div class="tab-indicator"></div>
                </div>
                <div class="tab-item" onclick="switchTab(this, 1)">
                    <span class="tab-text">组局约伴</span>
                </div>
                <div class="tab-item" onclick="switchTab(this, 2)">
                    <span class="tab-text">城市玩伴</span>
                </div>
                <div class="tab-item" onclick="switchTab(this, 3)">
                    <span class="tab-text">游戏玩伴</span>
                </div>
            </div>
        </div>
        
        <div class="content">
            <h3 id="content-title">首页推荐</h3>
            <p>当前选中的标签内容</p>
            
            <div class="fix-note">
                <strong>✅ 已修复的问题：</strong><br>
                1. 禁用了系统默认下拉刷新，避免冲突<br>
                2. 选中线条改为U型弧度（圆形下半部分）<br>
                3. 增大了线条尺寸，更加明显<br>
                4. 优化了多色系不规则图形刷新动画
            </div>
            
            <div class="demo-note">
                <strong>标签导航特点：</strong><br>
                • 文字标签，无图标<br>
                • 选中状态：字体加粗变大<br>
                • U型弧度线条指示器（更大更明显）<br>
                • 颜色渐变：#BBB2FF → #E3D2FF → #A7C0FF<br>
                • 默认选中"首页推荐"
            </div>
            
            <div class="refresh-demo-container">
                <button class="refresh-button" onclick="showRefreshAnimation()">
                    演示刷新动画
                </button>
                <div id="refresh-demo" class="hidden">
                    <div class="refresh-animation">
                        <div class="shape-item shape-1"></div>
                        <div class="shape-item shape-2"></div>
                        <div class="shape-item shape-3"></div>
                        <div class="shape-item shape-4"></div>
                        <div class="shape-item shape-5"></div>
                        <div class="shape-item shape-6"></div>
                    </div>
                    <p style="margin-top: 10px; color: #666; font-size: 14px;">多色系不规则图形刷新动画</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const tabNames = ['首页推荐', '组局约伴', '城市玩伴', '游戏玩伴'];
        
        function switchTab(element, index) {
            // 移除所有active状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
                const indicator = item.querySelector('.tab-indicator');
                if (indicator) {
                    indicator.remove();
                }
            });
            
            // 添加active状态到当前选中的标签
            element.classList.add('active');
            
            // 添加指示器
            const indicator = document.createElement('div');
            indicator.className = 'tab-indicator';
            element.appendChild(indicator);
            
            // 更新内容标题
            document.getElementById('content-title').textContent = tabNames[index];
        }
        
        function showRefreshAnimation() {
            const refreshDemo = document.getElementById('refresh-demo');
            refreshDemo.classList.remove('hidden');
            
            // 3秒后隐藏
            setTimeout(() => {
                refreshDemo.classList.add('hidden');
            }, 3000);
        }
    </script>
</body>
</html>
