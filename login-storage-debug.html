<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录存储调试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .title {
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 40px;
            color: #FFD700;
        }
        
        .debug-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .section-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #FFD700;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .debug-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #FFD700;
        }
        
        .debug-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #FFD700;
        }
        
        .debug-content {
            font-size: 14px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .working-case, .broken-case {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .working-case {
            border-left: 4px solid #63ff63;
        }
        
        .broken-case {
            border-left: 4px solid #ff6363;
        }
        
        .case-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .working-case .case-title {
            color: #63ff63;
        }
        
        .broken-case .case-title {
            color: #ff6363;
        }
        
        .flow-step {
            display: flex;
            align-items: flex-start;
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        
        .step-number {
            background: #FFD700;
            color: #333;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #FFD700;
        }
        
        .step-desc {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
        }
        
        .hypothesis {
            background: rgba(255, 215, 0, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }
        
        .hypothesis-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #FFD700;
        }
        
        .hypothesis-list {
            list-style: none;
            padding: 0;
        }
        
        .hypothesis-list li {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid #FFD700;
        }
        
        .test-plan {
            background: rgba(99, 255, 99, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 1px solid rgba(99, 255, 99, 0.3);
        }
        
        .test-plan-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #63ff63;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔍 登录存储调试工具</h1>
        
        <div class="debug-section">
            <div class="section-title">
                📊 问题现象对比
            </div>
            
            <div class="comparison-grid">
                <div class="working-case">
                    <div class="case-title">✅ 正常工作：验证码注册</div>
                    <div class="debug-content">
                        <strong>流程：</strong><br>
                        1. 输入手机号获取验证码<br>
                        2. 验证码注册成功<br>
                        3. 跳转到首页<br>
                        4. 切换到"我的"页面<br>
                        5. ✅ 正确显示用户信息
                    </div>
                    <div class="code-block">
// verify-code.vue 存储逻辑
uni.setStorageSync('userInfo', result.result.data.userInfo)
uni.setStorageSync('token', result.result.data.token)
uni.setStorageSync('isLoggedIn', true)

// 跳转
uni.reLaunch({ url: '/pages/index/index' })
                    </div>
                </div>
                
                <div class="broken-case">
                    <div class="case-title">❌ 有问题：密码登录</div>
                    <div class="debug-content">
                        <strong>流程：</strong><br>
                        1. 手机号检测已注册<br>
                        2. 跳转到密码登录页面<br>
                        3. 密码登录成功<br>
                        4. 跳转到首页<br>
                        5. 切换到"我的"页面<br>
                        6. ❌ 仍显示"请登录"
                    </div>
                    <div class="code-block">
// phone-login.vue 存储逻辑（已修复）
uni.setStorageSync('userInfo', result.result.data.userInfo)
uni.setStorageSync('token', result.result.data.token)
uni.setStorageSync('isLoggedIn', true)

// 跳转（已改为直接跳转到我的页面测试）
uni.switchTab({ url: '/pages/profile/profile' })
                    </div>
                </div>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="section-title">
                🔍 调试信息分析
            </div>
            
            <div class="debug-item">
                <div class="debug-title">📝 已添加的调试日志</div>
                <div class="debug-content">
                    <strong>密码登录页面 (phone-login.vue)：</strong>
                    <div class="code-block">
console.log('密码登录成功，保存用户信息:', result.result.data)
console.log('存储验证 - userInfo:', savedUserInfo)
console.log('存储验证 - token:', savedToken)
console.log('存储验证 - isLoggedIn:', savedLoginStatus)
                    </div>
                    
                    <strong>"我的"页面 (profile.vue)：</strong>
                    <div class="code-block">
console.log('检查登录状态 - userInfo:', userInfo)
console.log('检查登录状态 - token:', token)
console.log('检查登录状态 - isLoggedIn:', isLoggedIn)
                    </div>
                </div>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="section-title">
                🤔 可能原因假设
            </div>
            
            <div class="hypothesis">
                <div class="hypothesis-title">💡 假设分析</div>
                <ul class="hypothesis-list">
                    <li><strong>假设1：存储时机问题</strong><br>
                        密码登录页面的存储可能在页面跳转前被清除或覆盖</li>
                    <li><strong>假设2：页面跳转方式差异</strong><br>
                        uni.reLaunch vs uni.switchTab 可能有不同的行为</li>
                    <li><strong>假设3：云函数返回数据差异</strong><br>
                        登录和注册的云函数返回结构可能不完全一致</li>
                    <li><strong>假设4：页面生命周期问题</strong><br>
                        某个页面的onShow/onLoad可能清除了存储</li>
                    <li><strong>假设5：存储权限或异步问题</strong><br>
                        uni.setStorageSync可能在某些情况下失败</li>
                </ul>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="section-title">
                🧪 测试计划
            </div>
            
            <div class="test-plan">
                <div class="test-plan-title">🎯 调试步骤</div>
                
                <div class="flow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <div class="step-title">修改跳转目标</div>
                        <div class="step-desc">
                            将密码登录成功后的跳转改为直接跳转到"我的"页面，避免首页可能的干扰
                        </div>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <div class="step-title">观察控制台日志</div>
                        <div class="step-desc">
                            查看密码登录时的存储日志和"我的"页面的读取日志，确认数据是否正确存储和读取
                        </div>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <div class="step-title">对比云函数返回</div>
                        <div class="step-desc">
                            对比注册和登录的云函数返回数据结构，确认数据格式一致性
                        </div>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <div class="step-title">检查存储持久性</div>
                        <div class="step-desc">
                            在登录成功后立即检查存储，然后在页面跳转后再次检查，确认数据是否被意外清除
                        </div>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <div class="step-title">添加存储监听</div>
                        <div class="step-desc">
                            如果问题仍然存在，添加更详细的存储监听和错误处理
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="section-title">
                📋 当前修改总结
            </div>
            
            <div class="debug-item">
                <div class="debug-title">✅ 已完成的修复</div>
                <div class="debug-content">
                    1. ✅ 统一了所有登录页面的数据解析方式<br>
                    2. ✅ 修复了云函数返回结构不一致问题<br>
                    3. ✅ 添加了详细的调试日志<br>
                    4. ✅ 修改密码登录页面直接跳转到"我的"页面进行测试<br>
                    5. ✅ 确保所有登录成功后都设置 isLoggedIn: true
                </div>
            </div>
            
            <div class="debug-item">
                <div class="debug-title">🔄 下一步测试</div>
                <div class="debug-content">
                    现在请重新测试密码登录流程：<br>
                    1. 输入已注册的手机号<br>
                    2. 跳转到密码登录页面<br>
                    3. 输入正确密码登录<br>
                    4. 观察控制台日志<br>
                    5. 查看是否直接跳转到"我的"页面并显示用户信息
                </div>
            </div>
        </div>
    </div>
</body>
</html>
