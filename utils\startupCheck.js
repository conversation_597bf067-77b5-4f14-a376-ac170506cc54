/**
 * 应用启动检测工具
 * 在应用启动时进行必要的网络和服务检测
 */

import { comprehensiveNetworkDiagnosis } from './networkTest.js'

// 启动检测配置
const STARTUP_CONFIG = {
	// 是否启用启动检测
	enabled: true,
	// 检测超时时间（毫秒）
	timeout: 10000,
	// 是否显示检测过程
	showProgress: true,
	// 失败重试次数
	retryCount: 2
}

/**
 * 执行启动检测
 */
export async function performStartupCheck() {
	if (!STARTUP_CONFIG.enabled) {
		console.log('启动检测已禁用')
		return { success: true, message: '检测已禁用' }
	}

	console.log('开始应用启动检测...')
	
	if (STARTUP_CONFIG.showProgress) {
		uni.showLoading({
			title: '初始化中...',
			mask: true
		})
	}

	try {
		const checkResult = await Promise.race([
			runStartupChecks(),
			new Promise((_, reject) => 
				setTimeout(() => reject(new Error('检测超时')), STARTUP_CONFIG.timeout)
			)
		])

		if (STARTUP_CONFIG.showProgress) {
			uni.hideLoading()
		}

		console.log('启动检测完成:', checkResult)
		return checkResult

	} catch (error) {
		if (STARTUP_CONFIG.showProgress) {
			uni.hideLoading()
		}

		console.error('启动检测失败:', error)
		return {
			success: false,
			error: error.message,
			message: '启动检测失败'
		}
	}
}

/**
 * 运行启动检测项目
 */
async function runStartupChecks() {
	const results = {
		timestamp: new Date().toISOString(),
		checks: {},
		overall: true,
		issues: [],
		recommendations: []
	}

	// 1. 检测设备信息
	console.log('1. 检测设备信息...')
	results.checks.deviceInfo = await checkDeviceInfo()

	// 2. 检测网络状态
	console.log('2. 检测网络状态...')
	results.checks.networkStatus = await checkNetworkStatus()

	// 3. 检测uniCloud连接
	console.log('3. 检测uniCloud连接...')
	results.checks.uniCloudStatus = await checkUniCloudStatus()

	// 4. 检测本地存储
	console.log('4. 检测本地存储...')
	results.checks.storageStatus = await checkStorageStatus()

	// 5. 检测权限状态
	console.log('5. 检测权限状态...')
	results.checks.permissionStatus = await checkPermissionStatus()

	// 分析检测结果
	analyzeStartupResults(results)

	return results
}

/**
 * 检测设备信息
 */
function checkDeviceInfo() {
	return new Promise((resolve) => {
		try {
			const systemInfo = uni.getSystemInfoSync()
			resolve({
				success: true,
				data: {
					platform: systemInfo.platform,
					system: systemInfo.system,
					version: systemInfo.version,
					model: systemInfo.model,
					brand: systemInfo.brand,
					screenWidth: systemInfo.screenWidth,
					screenHeight: systemInfo.screenHeight,
					statusBarHeight: systemInfo.statusBarHeight,
					safeArea: systemInfo.safeArea
				}
			})
		} catch (error) {
			resolve({
				success: false,
				error: error.message
			})
		}
	})
}

/**
 * 检测网络状态
 */
function checkNetworkStatus() {
	return new Promise((resolve) => {
		uni.getNetworkType({
			success: (res) => {
				const isConnected = res.networkType !== 'none'
				resolve({
					success: isConnected,
					data: {
						networkType: res.networkType,
						isConnected: isConnected,
						isWifi: res.networkType === 'wifi',
						isMobile: ['2g', '3g', '4g', '5g'].includes(res.networkType)
					}
				})
			},
			fail: (error) => {
				resolve({
					success: false,
					error: error.errMsg
				})
			}
		})
	})
}

/**
 * 检测uniCloud连接状态
 */
async function checkUniCloudStatus() {
	try {
		// 尝试调用简单的云函数测试连接
		const result = await uniCloud.callFunction({
			name: 'simple-test',
			data: { test: 'startup-check' }
		})

		return {
			success: true,
			data: {
				connected: true,
				response: result.result
			}
		}
	} catch (error) {
		return {
			success: false,
			error: error.message || error.errMsg,
			data: {
				connected: false,
				errorCode: error.errCode
			}
		}
	}
}

/**
 * 检测本地存储状态
 */
function checkStorageStatus() {
	return new Promise((resolve) => {
		try {
			// 测试存储读写
			const testKey = 'startup_test_' + Date.now()
			const testValue = 'test_value'
			
			uni.setStorageSync(testKey, testValue)
			const readValue = uni.getStorageSync(testKey)
			uni.removeStorageSync(testKey)

			const success = readValue === testValue

			resolve({
				success: success,
				data: {
					canWrite: success,
					canRead: success,
					storageInfo: uni.getStorageInfoSync()
				}
			})
		} catch (error) {
			resolve({
				success: false,
				error: error.message
			})
		}
	})
}

/**
 * 检测权限状态
 */
function checkPermissionStatus() {
	return new Promise((resolve) => {
		// 在实际应用中，这里可以检测各种权限
		// 目前简单返回成功状态
		resolve({
			success: true,
			data: {
				location: 'unknown',
				camera: 'unknown',
				storage: 'unknown'
			}
		})
	})
}

/**
 * 分析启动检测结果
 */
function analyzeStartupResults(results) {
	const { checks } = results

	// 检查设备信息
	if (!checks.deviceInfo.success) {
		results.overall = false
		results.issues.push('设备信息获取失败')
		results.recommendations.push('请重启应用')
	}

	// 检查网络状态
	if (!checks.networkStatus.success || !checks.networkStatus.data.isConnected) {
		results.overall = false
		results.issues.push('网络连接异常')
		results.recommendations.push('请检查网络连接')
	}

	// 检查uniCloud连接
	if (!checks.uniCloudStatus.success) {
		results.overall = false
		results.issues.push('云服务连接失败')
		results.recommendations.push('请检查网络或稍后重试')
	}

	// 检查本地存储
	if (!checks.storageStatus.success) {
		results.overall = false
		results.issues.push('本地存储异常')
		results.recommendations.push('请清理应用缓存')
	}

	// 如果没有问题
	if (results.issues.length === 0) {
		results.overall = true
		results.recommendations.push('应用状态正常')
	}
}

/**
 * 显示启动检测结果
 */
export function showStartupResult(results) {
	if (results.overall) {
		console.log('启动检测通过')
		return
	}

	// 如果有问题，显示提示
	const issueText = results.issues.join('、')
	const recommendationText = results.recommendations.join('、')

	uni.showModal({
		title: '启动检测发现问题',
		content: `问题：${issueText}\n\n建议：${recommendationText}`,
		showCancel: true,
		cancelText: '忽略',
		confirmText: '网络诊断',
		success: (res) => {
			if (res.confirm) {
				// 运行完整的网络诊断
				comprehensiveNetworkDiagnosis().then(diagnosis => {
					console.log('网络诊断结果:', diagnosis)
				})
			}
		}
	})
}

/**
 * 快速网络检测（用于页面加载前）
 */
export function quickNetworkCheck() {
	return new Promise((resolve) => {
		uni.getNetworkType({
			success: (res) => {
				resolve({
					isConnected: res.networkType !== 'none',
					networkType: res.networkType
				})
			},
			fail: () => {
				resolve({
					isConnected: false,
					networkType: 'unknown'
				})
			}
		})
	})
}
