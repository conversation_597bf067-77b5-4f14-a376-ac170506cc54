<template>
	<view class="following-page">
		<!-- 状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left" @click="goBack">
				<view class="back-btn">
					<uni-icons type="left" size="18" color="#fff"></uni-icons>
				</view>
			</view>
			<view class="nav-center">
				<text class="nav-title">我的关注</text>
				<text class="nav-subtitle">关注了{{ totalFollowing }}人</text>
			</view>
			<view class="nav-right">
				<view class="manage-btn" @click="toggleManageMode">
					<text class="manage-text">{{ isManageMode ? '完成' : '管理' }}</text>
				</view>
			</view>
		</view>

		<!-- 分类标签 -->
		<view class="category-tabs">
			<scroll-view scroll-x class="tabs-scroll" :show-scrollbar="false">
				<view class="tabs-container">
					<view 
						class="tab-item" 
						:class="{ active: activeCategory === index }"
						v-for="(category, index) in categories" 
						:key="index"
						@click="switchCategory(index)"
					>
						<view class="tab-content">
							<uni-icons :type="category.icon" size="16" :color="activeCategory === index ? '#fff' : '#666'"></uni-icons>
							<text class="tab-text">{{ category.name }}</text>
							<text class="tab-count">{{ category.count }}</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 自定义下拉刷新 -->
		<view class="custom-refresh" v-if="refreshing">
			<view class="refresh-container">
				<view class="stars-animation">
					<view class="star-item star-1">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-2">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-3">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-4">
						<text class="star-text">✦</text>
					</view>
					<view class="star-item star-5">
						<text class="star-text">✦</text>
					</view>
				</view>
				<text class="refresh-text">正在刷新...</text>
			</view>
		</view>

		<!-- 关注列表 -->
		<scroll-view
			class="following-list"
			scroll-y
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd"
		>
			<view class="list-container">
				<view 
					class="following-card" 
					v-for="user in filteredFollowing" 
					:key="user.id"
					@click="viewProfile(user)"
				>
					<view class="card-content">
						<!-- 管理模式选择框 -->
						<view class="select-box" v-if="isManageMode" @click.stop="toggleSelect(user)">
							<view class="checkbox" :class="{ checked: user.selected }">
								<uni-icons v-if="user.selected" type="checkmarkempty" size="14" color="#fff"></uni-icons>
							</view>
						</view>
						
						<view class="avatar-section">
							<image :src="user.avatar" class="user-avatar"></image>
							<view v-if="user.isOnline" class="online-dot"></view>
							<view class="category-badge" :style="{ background: getCategoryColor(user.category) }">
								<uni-icons :type="getCategoryIcon(user.category)" size="12" color="#fff"></uni-icons>
							</view>
						</view>
						
						<view class="info-section">
							<view class="user-info">
								<text class="user-name">{{ user.name }}</text>
								<view class="user-tags">
									<text class="tag vip" v-if="user.isVip">VIP</text>
									<text class="tag creator" v-if="user.isCreator">创作者</text>
								</view>
							</view>
							<text class="user-desc">{{ user.description }}</text>
							<view class="stats-info">
								<view class="stat-item">
									<uni-icons type="heart-filled" size="12" color="#FF6B6B"></uni-icons>
									<text class="stat-text">{{ user.likes }}获赞</text>
								</view>
								<view class="stat-item">
									<uni-icons type="compose" size="12" color="#4ECDC4"></uni-icons>
									<text class="stat-text">{{ user.posts }}作品</text>
								</view>
							</view>
						</view>
						
						<view class="action-section" v-if="!isManageMode">
							<view class="action-btn message-btn" @click.stop="sendMessage(user)">
								<uni-icons type="chat" size="16" color="#667eea"></uni-icons>
							</view>
							<view 
								class="action-btn unfollow-btn" 
								@click.stop="unfollowUser(user)"
							>
								<uni-icons type="minus" size="16" color="#FF6B6B"></uni-icons>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 空状态 -->
				<view class="empty-state" v-if="filteredFollowing.length === 0">
					<view class="empty-icon">
						<uni-icons type="heart" size="60" color="#ddd"></uni-icons>
					</view>
					<text class="empty-title">暂无关注</text>
					<text class="empty-desc">去发现页面找找感兴趣的人吧~</text>
					<view class="discover-btn" @click="goToDiscover">
						<text class="discover-text">去发现</text>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 管理模式底部操作栏 -->
		<view class="manage-toolbar" v-if="isManageMode">
			<view class="toolbar-left">
				<view class="select-all-btn" @click="toggleSelectAll">
					<view class="checkbox" :class="{ checked: isAllSelected }">
						<uni-icons v-if="isAllSelected" type="checkmarkempty" size="14" color="#fff"></uni-icons>
					</view>
					<text class="select-text">全选</text>
				</view>
				<text class="selected-count">已选择{{ selectedCount }}人</text>
			</view>
			<view class="toolbar-right">
				<view class="batch-btn category-btn" @click="batchSetCategory">
					<uni-icons type="list" size="16" color="#4ECDC4"></uni-icons>
					<text class="btn-text">分组</text>
				</view>
				<view class="batch-btn unfollow-btn" @click="batchUnfollow">
					<uni-icons type="minus" size="16" color="#FF6B6B"></uni-icons>
					<text class="btn-text">取消关注</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				activeCategory: 0,
				refreshing: false,
				isManageMode: false,
				totalFollowing: 128,
				touchStartY: 0,
				scrollTop: 0,
				
				categories: [
					{ name: '全部', icon: 'list', count: 128 },
					{ name: '朋友', icon: 'heart-filled', count: 45 },
					{ name: '创作者', icon: 'star-filled', count: 23 },
					{ name: '游戏', icon: 'videocam', count: 31 },
					{ name: '生活', icon: 'home', count: 29 }
				],
				
				following: [
					{
						id: 1,
						name: '小雨摄影师',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						description: '专业摄影师，分享生活美好瞬间',
						category: 'creator',
						isOnline: true,
						isVip: true,
						isCreator: true,
						likes: 1234,
						posts: 89,
						selected: false
					},
					{
						id: 2,
						name: '游戏达人阿杰',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						description: '王者荣耀主播，带你上分',
						category: 'game',
						isOnline: false,
						isVip: false,
						isCreator: false,
						likes: 567,
						posts: 45,
						selected: false
					}
				]
			}
		},
		
		computed: {
			filteredFollowing() {
				if (this.activeCategory === 0) {
					return this.following;
				}
				
				const categoryMap = {
					1: 'friend',
					2: 'creator', 
					3: 'game',
					4: 'life'
				};
				
				const targetCategory = categoryMap[this.activeCategory];
				return this.following.filter(user => user.category === targetCategory);
			},
			
			selectedCount() {
				return this.following.filter(user => user.selected).length;
			},
			
			isAllSelected() {
				return this.filteredFollowing.length > 0 && 
					   this.filteredFollowing.every(user => user.selected);
			}
		},
		
		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},
		
		methods: {
			goBack() {
				uni.navigateBack();
			},
			
			toggleManageMode() {
				this.isManageMode = !this.isManageMode;
				if (!this.isManageMode) {
					// 退出管理模式时清除所有选择
					this.following.forEach(user => user.selected = false);
				}
			},
			
			switchCategory(index) {
				this.activeCategory = index;
			},

			// 触摸开始
			onTouchStart(e) {
				this.touchStartY = e.touches[0].clientY;
				this.scrollTop = 0;
			},

			// 触摸移动
			onTouchMove(e) {
				const currentY = e.touches[0].clientY;
				const deltaY = currentY - this.touchStartY;

				// 只有在页面顶部且向下拉时才触发刷新
				if (this.scrollTop <= 0 && deltaY > 100 && !this.refreshing) {
					this.triggerRefresh();
				}
			},

			// 触摸结束
			onTouchEnd() {
				this.touchStartY = 0;
			},

			// 触发刷新
			triggerRefresh() {
				this.refreshing = true;
				setTimeout(() => {
					this.refreshing = false;
					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					});
				}, 2000);
			},
			
			viewProfile(user) {
				if (!this.isManageMode) {
					console.log('查看用户资料:', user.name);
				}
			},
			
			toggleSelect(user) {
				user.selected = !user.selected;
			},
			
			toggleSelectAll() {
				const shouldSelectAll = !this.isAllSelected;
				this.filteredFollowing.forEach(user => {
					user.selected = shouldSelectAll;
				});
			},
			
			sendMessage(user) {
				console.log('发送消息给:', user.name);
			},
			
			unfollowUser(user) {
				uni.showModal({
					title: '确认取消关注',
					content: `确定要取消关注"${user.name}"吗？`,
					success: (res) => {
						if (res.confirm) {
							const index = this.following.findIndex(u => u.id === user.id);
							if (index > -1) {
								this.following.splice(index, 1);
								this.totalFollowing--;
							}
						}
					}
				});
			},
			
			batchUnfollow() {
				const selectedUsers = this.following.filter(user => user.selected);
				if (selectedUsers.length === 0) {
					uni.showToast({ title: '请先选择要取消关注的用户', icon: 'none' });
					return;
				}
				
				uni.showModal({
					title: '批量取消关注',
					content: `确定要取消关注${selectedUsers.length}个用户吗？`,
					success: (res) => {
						if (res.confirm) {
							this.following = this.following.filter(user => !user.selected);
							this.totalFollowing -= selectedUsers.length;
							this.isManageMode = false;
						}
					}
				});
			},
			
			batchSetCategory() {
				const selectedUsers = this.following.filter(user => user.selected);
				if (selectedUsers.length === 0) {
					uni.showToast({ title: '请先选择要分组的用户', icon: 'none' });
					return;
				}
				
				console.log('批量设置分组:', selectedUsers.length);
			},
			
			goToDiscover() {
				uni.switchTab({ url: '/pages/discover/discover' });
			},
			
			getCategoryColor(category) {
				const colors = {
					friend: '#FF6B6B',
					creator: '#FFD700', 
					game: '#4ECDC4',
					life: '#9B59B6'
				};
				return colors[category] || '#999';
			},
			
			getCategoryIcon(category) {
				const icons = {
					friend: 'heart-filled',
					creator: 'star-filled',
					game: 'videocam', 
					life: 'home'
				};
				return icons[category] || 'person';
			}
		}
	}
</script>

<style lang="scss" scoped>
	.following-page {
		background: #f5f6fa;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}
	
	.status-bar {
		background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
	}
	
	// 顶部导航
	.top-nav {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16rpx 24rpx;
		background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
		
		.nav-left {
			.back-btn {
				width: 40rpx;
				height: 40rpx;
				border-radius: 20rpx;
				background: rgba(255, 255, 255, 0.2);
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				
				&:active {
					background: rgba(255, 255, 255, 0.3);
					transform: scale(0.95);
				}
			}
		}
		
		.nav-center {
			flex: 1;
			text-align: center;
			
			.nav-title {
				font-size: 34rpx;
				font-weight: 700;
				color: #fff;
				display: block;
				margin-bottom: 4rpx;
			}
			
			.nav-subtitle {
				font-size: 22rpx;
				color: rgba(255, 255, 255, 0.8);
			}
		}
		
		.nav-right {
			.manage-btn {
				padding: 8rpx 16rpx;
				border-radius: 16rpx;
				background: rgba(255, 255, 255, 0.2);
				transition: all 0.3s ease;
				
				&:active {
					background: rgba(255, 255, 255, 0.3);
					transform: scale(0.95);
				}
				
				.manage-text {
					font-size: 24rpx;
					color: #fff;
					font-weight: 500;
				}
			}
		}
	}

	// 自定义下拉刷新
	.custom-refresh {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10rpx);

		.refresh-container {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 40rpx 20rpx 20rpx;

			.stars-animation {
				position: relative;
				width: 80rpx;
				height: 80rpx;
				margin-bottom: 16rpx;

				.star-item {
					position: absolute;
					display: flex;
					align-items: center;
					justify-content: center;

					.star-text {
						font-size: 20rpx;
						color: #4ECDC4;
						font-weight: bold;
					}

					&.star-1 {
						top: 0;
						left: 50%;
						transform: translateX(-50%);
						animation: starPulse 1.5s ease-in-out infinite;
					}

					&.star-2 {
						top: 20rpx;
						right: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 0.3s;
					}

					&.star-3 {
						bottom: 20rpx;
						right: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 0.6s;
					}

					&.star-4 {
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						animation: starPulse 1.5s ease-in-out infinite 0.9s;
					}

					&.star-5 {
						top: 20rpx;
						left: 10rpx;
						animation: starPulse 1.5s ease-in-out infinite 1.2s;
					}
				}
			}

			.refresh-text {
				font-size: 24rpx;
				color: #666;
				font-weight: 500;
			}
		}
	}

	// 分类标签
	.category-tabs {
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;

		.tabs-scroll {
			white-space: nowrap;

			.tabs-container {
				display: flex;
				padding: 0 24rpx;

				.tab-item {
					margin-right: 16rpx;
					border-radius: 24rpx;
					transition: all 0.3s ease;

					&.active {
						background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);

						.tab-content {
							.tab-text {
								color: #fff;
							}

							.tab-count {
								background: rgba(255, 255, 255, 0.2);
								color: #fff;
							}
						}
					}

					.tab-content {
						display: flex;
						align-items: center;
						gap: 8rpx;
						padding: 16rpx 20rpx;

						.tab-text {
							font-size: 26rpx;
							color: #666;
							font-weight: 500;
						}

						.tab-count {
							background: #f0f0f0;
							color: #999;
							font-size: 20rpx;
							padding: 4rpx 8rpx;
							border-radius: 10rpx;
							min-width: 32rpx;
							text-align: center;
						}
					}
				}
			}
		}
	}

	// 关注列表
	.following-list {
		flex: 1;

		.list-container {
			padding: 16rpx 24rpx;

			.following-card {
				background: #fff;
				border-radius: 20rpx;
				margin-bottom: 16rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
				}

				.card-content {
					display: flex;
					align-items: center;
					padding: 24rpx;
					gap: 20rpx;

					.select-box {
						flex-shrink: 0;

						.checkbox {
							width: 32rpx;
							height: 32rpx;
							border-radius: 16rpx;
							border: 2rpx solid #ddd;
							display: flex;
							align-items: center;
							justify-content: center;
							transition: all 0.3s ease;

							&.checked {
								background: #4ECDC4;
								border-color: #4ECDC4;
							}
						}
					}

					.avatar-section {
						position: relative;
						flex-shrink: 0;

						.user-avatar {
							width: 88rpx;
							height: 88rpx;
							border-radius: 44rpx;
							border: 3rpx solid #f0f0f0;
						}

						.online-dot {
							position: absolute;
							bottom: 4rpx;
							right: 4rpx;
							width: 20rpx;
							height: 20rpx;
							background: #2ed573;
							border-radius: 50%;
							border: 3rpx solid #fff;
						}

						.category-badge {
							position: absolute;
							top: -8rpx;
							right: -8rpx;
							width: 28rpx;
							height: 28rpx;
							border-radius: 14rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							border: 2rpx solid #fff;
						}
					}

					.info-section {
						flex: 1;
						min-width: 0;

						.user-info {
							display: flex;
							align-items: center;
							gap: 12rpx;
							margin-bottom: 8rpx;

							.user-name {
								font-size: 30rpx;
								font-weight: 600;
								color: #333;
							}

							.user-tags {
								display: flex;
								gap: 8rpx;

								.tag {
									font-size: 18rpx;
									padding: 4rpx 8rpx;
									border-radius: 8rpx;
									font-weight: 500;

									&.vip {
										background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
										color: #333;
									}

									&.creator {
										background: #9B59B6;
										color: #fff;
									}
								}
							}
						}

						.user-desc {
							font-size: 24rpx;
							color: #666;
							margin-bottom: 12rpx;
							line-height: 1.4;
						}

						.stats-info {
							display: flex;
							gap: 24rpx;

							.stat-item {
								display: flex;
								align-items: center;
								gap: 6rpx;

								.stat-text {
									font-size: 22rpx;
									color: #999;
								}
							}
						}
					}

					.action-section {
						flex-shrink: 0;
						display: flex;
						gap: 12rpx;

						.action-btn {
							width: 44rpx;
							height: 44rpx;
							border-radius: 22rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							transition: all 0.3s ease;

							&.message-btn {
								background: rgba(102, 126, 234, 0.1);
								border: 1rpx solid rgba(102, 126, 234, 0.3);

								&:active {
									background: rgba(102, 126, 234, 0.2);
								}
							}

							&.unfollow-btn {
								background: rgba(255, 107, 107, 0.1);
								border: 1rpx solid rgba(255, 107, 107, 0.3);

								&:active {
									background: rgba(255, 107, 107, 0.2);
								}
							}
						}
					}
				}
			}

			// 空状态
			.empty-state {
				text-align: center;
				padding: 120rpx 40rpx;

				.empty-icon {
					margin-bottom: 24rpx;
				}

				.empty-title {
					font-size: 32rpx;
					color: #666;
					font-weight: 600;
					display: block;
					margin-bottom: 12rpx;
				}

				.empty-desc {
					font-size: 26rpx;
					color: #999;
					line-height: 1.5;
					margin-bottom: 32rpx;
				}

				.discover-btn {
					background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
					color: #fff;
					padding: 16rpx 32rpx;
					border-radius: 24rpx;
					display: inline-block;

					.discover-text {
						font-size: 28rpx;
						font-weight: 600;
						color: #fff;
					}
				}
			}
		}
	}

	// 管理模式工具栏
	.manage-toolbar {
		background: #fff;
		border-top: 1rpx solid #f0f0f0;
		padding: 16rpx 24rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.06);

		.toolbar-left {
			display: flex;
			align-items: center;
			gap: 24rpx;

			.select-all-btn {
				display: flex;
				align-items: center;
				gap: 12rpx;

				.checkbox {
					width: 28rpx;
					height: 28rpx;
					border-radius: 14rpx;
					border: 2rpx solid #ddd;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.3s ease;

					&.checked {
						background: #4ECDC4;
						border-color: #4ECDC4;
					}
				}

				.select-text {
					font-size: 26rpx;
					color: #333;
					font-weight: 500;
				}
			}

			.selected-count {
				font-size: 24rpx;
				color: #999;
			}
		}

		.toolbar-right {
			display: flex;
			gap: 16rpx;

			.batch-btn {
				display: flex;
				align-items: center;
				gap: 8rpx;
				padding: 12rpx 20rpx;
				border-radius: 20rpx;
				transition: all 0.3s ease;

				&.category-btn {
					background: rgba(78, 205, 196, 0.1);
					border: 1rpx solid rgba(78, 205, 196, 0.3);

					.btn-text {
						color: #4ECDC4;
					}
				}

				&.unfollow-btn {
					background: rgba(255, 107, 107, 0.1);
					border: 1rpx solid rgba(255, 107, 107, 0.3);

					.btn-text {
						color: #FF6B6B;
					}
				}

				&:active {
					transform: scale(0.95);
				}

				.btn-text {
					font-size: 24rpx;
					font-weight: 500;
				}
			}
		}
	}

	// 星星脉冲动画
	@keyframes starPulse {
		0%, 100% {
			transform: scale(0.8);
			opacity: 0.5;
		}
		50% {
			transform: scale(1.2);
			opacity: 1;
		}
	}
</style>
