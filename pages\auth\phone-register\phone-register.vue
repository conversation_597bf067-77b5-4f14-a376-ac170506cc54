<template>
	<view class="container" @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
		<!-- 背景图片 -->
		<image src="/static/logoo.png" class="background-image" mode="aspectFill"></image>

		<!-- 状态栏占位 -->
		<view class="status-bar" :style="getStatusBarStyle()"></view>

		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="back-button" @click="goBack">
				<image src="/static/fanhuilogo.png" class="back-icon" mode="aspectFit"></image>
			</view>
		</view>

		<!-- 手势返回指示器 -->
		<view class="gesture-indicator" v-if="showGestureIndicator" :style="{ opacity: gestureOpacity }">
			<image src="/static/fanhuilogo.png" class="gesture-icon" mode="aspectFit"></image>
			<text class="gesture-text">松手返回</text>
		</view>

		<!-- 内容区域 -->
		<view class="content">


			<!-- 手机号输入区域 -->
			<view class="phone-input-container">
				<!-- 地区选择 -->
				<view class="country-code-selector" @click="showCountryPicker">
					<text class="country-code">{{ selectedCountryCode }}</text>
					<image src="/static/icons/chevron-down.png" class="dropdown-icon" mode="aspectFit"></image>
				</view>

				<!-- 手机号输入框 -->
				<input
					class="phone-input"
					type="number"
					:placeholder="phonePlaceholder"
					placeholder-style="color: #999999"
					v-model="phoneNumber"
					maxlength="11"
				/>
			</view>
			
			<!-- 获取验证码按钮 -->
			<view class="verify-btn" @click="getVerificationCode">
				<text class="verify-btn-text">获取验证码</text>
			</view>
			
			<!-- 用户协议 -->
			<view class="agreement-container">
				<view class="agreement-checkbox" @click="toggleAgreement">
					<view class="checkbox" :class="{ 'checked': isAgreed }">
						<text v-if="isAgreed" class="check-icon">✓</text>
					</view>
				</view>
				<text class="agreement-text">
					同意趣嗒同行
					<text class="agreement-link" @click="showUserAgreement">《用户协议》</text>
					<text class="agreement-link" @click="showPrivacyPolicy">《隐私政策》</text>
				</text>
			</view>
		</view>

		<!-- 自定义Toast -->
		<view class="custom-toast" v-if="showCustomToast" :class="{ 'show': showCustomToast }">
			<text class="toast-text">{{ toastMessage }}</text>
		</view>

		<!-- 验证码发送成功弹窗 -->
		<view class="success-modal-overlay" v-if="showSuccessModal" @click="hideSuccessModal">
			<view class="success-modal" @click.stop>
				<!-- 顶部渐变标题区域 -->
				<view class="modal-header">
					<!-- 成功图标 -->
					<view class="success-icon">
						<text class="icon-text">✓</text>
					</view>
					<!-- 标题 -->
					<text class="modal-title">短信验证码</text>
				</view>

				<!-- 内容 -->
				<view class="modal-content">
					<!-- 短信内容 -->
					<view class="sms-content">
						<text class="sms-text">【趣哒同行】尊敬的用户，您的验证码为：</text>
						<text class="verification-code">{{ currentVerificationCode }}</text>
						<text class="sms-text">，有效期1分钟，请勿泄露验证码，谨防诈骗。</text>
					</view>

					<!-- 发送目标 -->
					<text class="modal-phone">发送至：{{ phoneNumber }}</text>

					<!-- 确定按钮 -->
					<view class="modal-btn" @click="confirmSuccess">
						<text class="modal-btn-text">好的</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 地区选择弹窗 -->
		<view class="country-picker-overlay" v-if="showCountrySelector" @click="hideCountryPicker">
			<view class="country-picker" @click.stop>
				<view class="picker-header">
					<!-- 不规则圆形装饰 -->
					<view class="header-decoration">
						<view class="circle circle-1"></view>
						<view class="circle circle-2"></view>
						<view class="circle circle-3"></view>
						<view class="circle circle-4"></view>
						<view class="circle circle-5"></view>
						<view class="circle circle-6"></view>
					</view>
					<text class="picker-title">选择国家/地区</text>
					<view class="picker-close" @click="hideCountryPicker">
						<text class="close-text">×</text>
					</view>
				</view>
				<scroll-view class="country-list" scroll-y>
					<view
						class="country-item"
						v-for="country in countryList"
						:key="country.code"
						@click="selectCountry(country)"
					>
						<text class="country-name">{{ country.name }}</text>
						<text class="country-code-text">{{ country.code }}</text>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
import gestureBackMixin from '@/mixins/gesture-back.js'

export default {
	mixins: [gestureBackMixin],
	data() {
		return {
			phoneNumber: '',
			selectedCountryCode: '+86',
			selectedCountryName: '中国',
			isAgreed: false,
			showCountrySelector: false,
			showCustomToast: false,
			toastMessage: '',
			showSuccessModal: false,
			currentVerificationCode: '',
			countryList: [
				{ name: '中国', code: '+86' },
				{ name: '美国', code: '+1' },
				{ name: '日本', code: '+81' },
				{ name: '韩国', code: '+82' },
				{ name: '英国', code: '+44' },
				{ name: '法国', code: '+33' },
				{ name: '德国', code: '+49' },
				{ name: '澳大利亚', code: '+61' },
				{ name: '加拿大', code: '+1' },
				{ name: '新加坡', code: '+65' },
				{ name: '马来西亚', code: '+60' },
				{ name: '泰国', code: '+66' },
				{ name: '印度', code: '+91' },
				{ name: '俄罗斯', code: '+7' },
				{ name: '巴西', code: '+55' }
			]
		}
	},



	computed: {
		phonePlaceholder() {
			return this.selectedCountryCode === '+86' ? '请输入手机号' : '请输入手机号码'
		}
	},
	
	methods: {
		// 返回登录页面
		goBack() {
			uni.navigateBack({
				url: '/pages/auth/auth'
			})
		},



		// 显示地区选择器
		showCountryPicker() {
			this.showCountrySelector = true
		},
		
		// 隐藏地区选择器
		hideCountryPicker() {
			this.showCountrySelector = false
		},
		
		// 选择国家
		selectCountry(country) {
			// 仅支持中国，其他国家暂不开放
			if (country.code !== '+86') {
				this.showCustomToastMessage('暂未开放该国家注册')
				// 显示提示后也要关闭弹窗
				this.hideCountryPicker()
				return
			}

			this.selectedCountryCode = country.code
			this.selectedCountryName = country.name
			this.hideCountryPicker()
		},

		// 显示自定义Toast
		showCustomToastMessage(message) {
			this.toastMessage = message
			this.showCustomToast = true
			setTimeout(() => {
				this.showCustomToast = false
			}, 2000)
		},

		// 隐藏成功弹窗
		hideSuccessModal() {
			this.showSuccessModal = false
		},

		// 确认成功弹窗
		confirmSuccess() {
			this.showSuccessModal = false
			// 跳转到验证码输入页面
			uni.navigateTo({
				url: `/pages/auth/verify-code/verify-code?mobile=${this.phoneNumber}`
			})
		},
		
		// 切换协议同意状态
		toggleAgreement() {
			this.isAgreed = !this.isAgreed
		},
		
		// 获取验证码
		async getVerificationCode() {
			if (!this.phoneNumber) {
				uni.showToast({
					title: '请输入手机号',
					icon: 'none',
					duration: 2000
				})
				return
			}

			if (!this.isAgreed) {
				uni.showToast({
					title: '请先同意用户协议',
					icon: 'none',
					duration: 2000
				})
				return
			}

			// 验证手机号格式
			if (this.selectedCountryCode === '+86' && !/^1[3-9]\d{9}$/.test(this.phoneNumber)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none',
					duration: 2000
				})
				return
			}

			try {
				uni.showLoading({
					title: '检测中...'
				})

				// 先检测手机号是否已注册
				const checkResult = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'checkUserExists',
						mobile: this.phoneNumber
					}
				})

				console.log('检测结果:', checkResult)

				if (checkResult.result.code === 0) {
					if (checkResult.result.data.exists) {
						uni.hideLoading()
						// 手机号已注册，跳转到登录页面
						uni.showToast({
							title: '手机号已注册，跳转到登录页面',
							icon: 'none',
							duration: 2000
						})

						setTimeout(() => {
							uni.navigateTo({
								url: `/pages/auth/phone-login/phone-login?mobile=${this.phoneNumber}`
							})
						}, 2000)
						return
					}
					// 手机号未注册，继续发送验证码
				} else {
					// 检测失败，显示错误信息
					uni.hideLoading()
					uni.showToast({
						title: checkResult.result.message || '检测失败，请重试',
						icon: 'none',
						duration: 2000
					})
					return
				}

				// 手机号未注册，继续发送注册验证码
				uni.showLoading({
					title: '发送中...'
				})

				// 调用云函数获取验证码
				const result = await uniCloud.callFunction({
					name: 'auth',
					data: {
						action: 'getVerificationCode',
						mobile: this.phoneNumber,
						type: 'register'
					}
				})

				uni.hideLoading()

				if (result.result.code === 0) {
					// 保存验证码用于弹窗显示
					if (result.result.data && result.result.data.verificationCode) {
						this.currentVerificationCode = result.result.data.verificationCode
						console.log('验证码:', result.result.data.verificationCode)
					}

					// 显示自定义成功弹窗
					this.showSuccessModal = true
				} else {
					uni.showToast({
						title: result.result.message || '发送失败',
						icon: 'none',
						duration: 2000
					})
				}
			} catch (error) {
				uni.hideLoading()
				console.error('获取验证码失败:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none',
					duration: 2000
				})
			}
		},
		
		// 显示用户协议
		showUserAgreement() {
			uni.navigateTo({
				url: '/pages/user-agreement/user-agreement'
			})
		},

		// 显示隐私政策
		showPrivacyPolicy() {
			uni.navigateTo({
				url: '/pages/privacy-policy/privacy-policy'
			})
		}
	}
}
</script>

<style scoped>
@import '@/styles/global.scss';

/* 强制消除所有可能的边距和留白 */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

page, body, html, uni-page-body, uni-page, uni-page-wrapper {
	margin: 0 !important;
	padding: 0 !important;
	width: 100% !important;
	height: 100% !important;
	overflow: hidden !important;
	border: none !important;
	outline: none !important;
}

.container {
	position: fixed;
	top: -10px;
	left: -10px;
	right: -10px;
	bottom: -10px;
	width: calc(100vw + 20px);
	height: calc(100vh + 20px);
	overflow: hidden;
	margin: 0;
	padding: 10px;
}

.nav-bar {
	position: fixed;
	top: var(--status-bar-height);
	left: 0;
	right: 0;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	padding: 0 var(--spacing-lg);
	z-index: 100;
	background: transparent;
}

.back-button {
	width: 50rpx;
	height: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.3);
	border-radius: 50%;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
}

.back-button:active {
	transform: scale(0.95);
	background: rgba(0, 0, 0, 0.5);
}

.back-icon {
	width: 28rpx;
	height: 28rpx;
	filter: brightness(0) invert(1);
}



.background-image {
	width: calc(100% + 20px);
	height: calc(100% + 20px);
	position: fixed;
	top: -10px;
	left: -10px;
	z-index: 1;
}

.status-bar {
	background: transparent;
	position: relative;
	z-index: 2;
}

.content {
	position: relative;
	z-index: 2;
	padding: 0 60rpx 60rpx;
	height: 100%;
	display: flex;
	flex-direction: column;
}



.phone-input-container {
	margin-top: 800rpx;
	margin-bottom: 80rpx;
	margin-left: 40rpx;
	margin-right: 40rpx;
	position: relative;
	display: flex;
	align-items: center;
	height: 120rpx;
	background: #FFFFFF;
	border-radius: 60rpx;
	padding: 0 40rpx;
	border: 3rpx solid #66D4C8;
	box-shadow: 0 8rpx 24rpx rgba(102, 212, 200, 0.2);
}

.country-code-selector {
	display: flex;
	align-items: center;
	margin-right: 20rpx;
	padding-right: 20rpx;
	border-right: 2rpx solid #E0E0E0;
}

.country-code {
	color: #333333;
	font-size: 36rpx;
	font-weight: bold;
	margin-right: 10rpx;
}

.dropdown-icon {
	width: 28rpx;
	height: 28rpx;
	opacity: 0.8;
}

.phone-input {
	flex: 1;
	height: 120rpx;
	padding: 0 20rpx;
	color: #333333;
	font-size: 36rpx;
	font-weight: bold;
	background: transparent;
	border: none;
	outline: none;
}

.phone-input::placeholder {
	color: #999999 !important;
}

/* 兼容不同平台的占位符样式 */
.phone-input::-webkit-input-placeholder {
	color: #999999 !important;
}

.phone-input::-moz-placeholder {
	color: #999999 !important;
}

.phone-input:-ms-input-placeholder {
	color: #999999 !important;
}

.verify-btn {
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 100%);
	border-radius: 60rpx;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 40rpx;
	margin-bottom: 80rpx;
	margin-left: 40rpx;
	margin-right: 40rpx;
	border: none;
	transition: all 0.3s ease;
	box-shadow: 0 8rpx 32rpx rgba(102, 212, 200, 0.4);
}

.verify-btn:active {
	transform: scale(0.98);
	box-shadow: 0 4rpx 16rpx rgba(102, 212, 200, 0.3);
}

.verify-btn:active {
	background: rgba(255, 255, 255, 0.35);
	transform: scale(0.98);
}

.verify-btn-text {
	color: #FFFFFF;
	font-size: 36rpx;
	font-weight: 500;
}

.agreement-container {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 40rpx;
	margin-left: 40rpx;
	margin-right: 40rpx;
	margin-bottom: 100rpx;
	flex-wrap: nowrap;
}

.agreement-checkbox {
	margin-right: 16rpx;
	flex-shrink: 0;
}

.checkbox {
	width: 32rpx;
	height: 32rpx;
	border: 3rpx solid #999999;
	border-radius: 6rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #FFFFFF;
	transition: all 0.3s ease;
}

.checkbox.checked {
	background: #66D4C8;
	border-color: #66D4C8;
}

.check-icon {
	color: #FFFFFF;
	font-size: 20rpx;
	font-weight: bold;
	line-height: 1;
}

.agreement-text {
	color: #333333;
	font-size: 24rpx;
	white-space: nowrap;
	flex-shrink: 0;
}

.agreement-link {
	color: #66D4C8;
	text-decoration: none;
	font-weight: 500;
}

/* 地区选择弹窗样式 */
.country-picker-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

.country-picker {
	width: 100%;
	max-height: 70%;
	background-color: #FFFFFF;
	border-radius: 40rpx 40rpx 0 0;
	overflow: hidden;
}

.picker-header {
	padding: 40rpx;
	border-bottom: 2rpx solid #F0F0F0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: relative;
	height: 120rpx;
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 50%, #5DD5C9 100%);
	overflow: hidden;
}

.header-decoration {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.circle {
	position: absolute;
	border-radius: 50%;
	opacity: 0.6;
}

.circle-1 {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(45deg, #66D4C8, #5DD5C9);
	top: -20rpx;
	left: 50rpx;
	transform: rotate(15deg);
}

.circle-2 {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(45deg, #4ECDC4, #66D4C8);
	top: 20rpx;
	right: 80rpx;
	transform: rotate(-25deg);
}

.circle-3 {
	width: 100rpx;
	height: 100rpx;
	background: linear-gradient(45deg, #5DD5C9, #4ECDC4);
	top: -30rpx;
	right: -20rpx;
	transform: rotate(35deg);
}

.circle-4 {
	width: 70rpx;
	height: 70rpx;
	background: linear-gradient(45deg, #FFD700, #FFA500);
	bottom: -15rpx;
	left: 30%;
	transform: rotate(-15deg);
}

.circle-5 {
	width: 50rpx;
	height: 50rpx;
	background: linear-gradient(45deg, #FF6B9D, #F06292);
	top: 60rpx;
	left: 20rpx;
	transform: rotate(45deg);
}

.circle-6 {
	width: 90rpx;
	height: 90rpx;
	background: linear-gradient(45deg, #E8F5E8, #C8E6C9);
	bottom: 10rpx;
	right: 40rpx;
	transform: rotate(-30deg);
}

.picker-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #FFFFFF;
	position: relative;
	z-index: 2;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.picker-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	z-index: 2;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
}

.close-text {
	font-size: 48rpx;
	color: #FFFFFF;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.country-list {
	max-height: 800rpx;
}

.country-item {
	padding: 30rpx 40rpx;
	border-bottom: 2rpx solid #F8F8F8;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.country-name {
	font-size: 32rpx;
	color: #333333;
}

.country-code-text {
	font-size: 28rpx;
	color: #666666;
}

/* 自定义Toast样式 */
.custom-toast {
	position: fixed;
	top: 70%;
	left: 50%;
	transform: translateX(-50%);
	background: rgba(0, 0, 0, 0.8);
	color: #FFFFFF;
	padding: 24rpx 40rpx;
	border-radius: 40rpx;
	z-index: 9999;
	opacity: 0;
	transition: all 0.3s ease;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.custom-toast.show {
	opacity: 1;
}

.toast-text {
	font-size: 28rpx;
	color: #FFFFFF;
	text-align: center;
	white-space: nowrap;
}

/* 验证码发送成功弹窗样式 */
.success-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.6);
	z-index: 10000;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(5rpx);
}

.success-modal {
	width: 600rpx;
	background: #FFFFFF;
	border-radius: 40rpx;
	position: relative;
	overflow: hidden;
	margin: 0 40rpx;
	box-shadow: 0 20rpx 60rpx rgba(102, 212, 200, 0.3);
}

.modal-header {
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 50%, #5DD5C9 100%);
	padding: 40rpx 40rpx 30rpx;
	text-align: center;
	border-radius: 40rpx 40rpx 0 0;
}




.success-icon {
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
	backdrop-filter: blur(10rpx);
}

.icon-text {
	font-size: 48rpx;
	color: #FFFFFF;
	font-weight: bold;
}

.modal-content {
	position: relative;
	z-index: 2;
	text-align: center;
	padding: 30rpx 40rpx 40rpx;
}

.modal-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #FFFFFF;
	display: block;
}

.sms-content {
	background: rgba(255, 255, 255, 0.15);
	padding: 30rpx;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	text-align: center;
}

.sms-text {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.5;
}

.verification-code {
	font-size: 36rpx;
	font-weight: bold;
	color: #66D4C8;
	text-shadow: 0 2rpx 8rpx rgba(102, 212, 200, 0.6);
	margin: 0 10rpx;
}

.modal-phone {
	display: block;
	font-size: 28rpx;
	color: #666666;
	text-align: center;
}

.modal-btn {
	background: linear-gradient(135deg, #66D4C8 0%, #4ECDC4 100%);
	border-radius: 50rpx;
	padding: 24rpx 60rpx;
	margin-top: 40rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 212, 200, 0.3);
}

.modal-btn:active {
	background: linear-gradient(135deg, #3CBAB0 0%, #2BA89E 100%);
	transform: scale(0.98);
}

.modal-btn-text {
	font-size: 32rpx;
	font-weight: 500;
	color: #FFFFFF;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}
</style>
