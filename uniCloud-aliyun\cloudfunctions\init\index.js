'use strict';

const uniID = require('uni-id-common');

exports.main = async (event, context) => {
	try {
		// 初始化uni-id
		const uniIdIns = uniID.createInstance({
			context
		});
		
		const db = uniCloud.database();
		
		// 检查uni-id-users表是否存在
		try {
			await db.collection('uni-id-users').limit(1).get();
			console.log('uni-id-users表已存在');
		} catch (error) {
			console.log('uni-id-users表不存在，开始创建...');
			
			// 创建uni-id-users表的基础结构
			const userCollection = db.collection('uni-id-users');
			
			// 插入一个示例用户来初始化表结构
			await userCollection.add({
				_id: 'temp_user_for_init',
				username: 'temp_user',
				mobile: '',
				password: '',
				nickname: '',
				gender: 0,
				avatar: '/static/default-avatar.png',
				status: 0,
				register_date: new Date(),
				register_ip: '',
				last_login_date: null,
				last_login_ip: '',
				// 自定义字段
				planet_id: '',
				level: 1,
				exp: 0,
				is_verified: false,
				is_vip: false,
				vip_level: 0,
				followers_count: 0,
				following_count: 0,
				likes_count: 0,
				posts_count: 0,
				location: '',
				ip_location: '',
				bio: ''
			});
			
			// 删除临时用户
			await userCollection.doc('temp_user_for_init').remove();
			
			console.log('uni-id-users表创建成功');
		}
		
		// 检查verification_codes表是否存在
		try {
			await db.collection('verification_codes').limit(1).get();
			console.log('verification_codes表已存在');
		} catch (error) {
			console.log('verification_codes表不存在，开始创建...');
			
			const codeCollection = db.collection('verification_codes');
			
			// 插入一个示例记录来初始化表结构
			await codeCollection.add({
				_id: 'temp_code_for_init',
				code: '0000',
				mobile: '00000000000',
				type: 'init',
				expires_at: new Date(),
				used: false,
				created_at: new Date()
			});
			
			// 删除临时记录
			await codeCollection.doc('temp_code_for_init').remove();
			
			console.log('verification_codes表创建成功');
		}
		
		return {
			code: 0,
			message: '数据库初始化成功',
			data: {
				tables_created: ['uni-id-users', 'verification_codes']
			}
		};
		
	} catch (error) {
		console.error('初始化失败:', error);
		return {
			code: -1,
			message: '初始化失败: ' + error.message
		};
	}
};
