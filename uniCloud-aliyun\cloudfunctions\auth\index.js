'use strict';

const crypto = require('crypto');

// 全局数据库实例
const db = uniCloud.database();

exports.main = async (event, context) => {
	console.log('=== 云函数开始执行 ===');
	console.log('event:', JSON.stringify(event));

	const { action, ...params } = event;
	console.log('解析的action:', action);
	console.log('解析的params:', JSON.stringify(params));

	// 检查action是否存在
	if (!action) {
		console.log('action为空');
		return {
			code: -1,
			message: 'action参数缺失'
		};
	}

	console.log('准备执行switch，action类型:', typeof action);
	console.log('action值:', action);

	switch (action) {
		case 'getVerificationCode':
			console.log('匹配到getVerificationCode');
			return await getVerificationCode(params);
		case 'verifyCode':
			console.log('匹配到verifyCode');
			return await verifyCode(params);
		case 'register':
			console.log('匹配到register');
			return await register(params);
		case 'login':
			console.log('匹配到login');
			return await login(params);
		case 'checkUserExists':
			console.log('匹配到checkUserExists');
			return await checkUserExists(params);
		default:
			console.log('进入default分支，未知操作');
			console.log('action值:', action);
			console.log('action类型:', typeof action);
			return {
				code: -1,
				message: '未知操作: ' + action + ' (类型: ' + typeof action + ')'
			};
	}
};

// 检测用户是否存在
async function checkUserExists({ mobile }) {
	try {
		// 验证手机号格式
		if (!/^1[3-9]\d{9}$/.test(mobile)) {
			return {
				code: -1,
				message: '手机号格式不正确'
			};
		}

		// 查询数据库检查用户是否存在
		const userCollection = db.collection('uni-id-users');
		const userQuery = await userCollection.where({
			mobile: mobile
		}).get();

		console.log('检查用户存在性，手机号:', mobile, '查询结果:', userQuery.data.length);

		return {
			code: 0,
			message: '检测成功',
			data: {
				exists: userQuery.data.length > 0
			}
		};
	} catch (error) {
		console.error('检测用户失败:', error);
		return {
			code: -1,
			message: '检测失败，请重试'
		};
	}
}

// 获取验证码
async function getVerificationCode({ mobile, type }) {
	try {
		// 验证手机号格式
		if (!/^1[3-9]\d{9}$/.test(mobile)) {
			return {
				code: -1,
				message: '手机号格式不正确'
			};
		}
		
		// 检查手机号是否已注册（注册时）
		if (type === 'register') {
			const userCol = db.collection('uni-id-users');
			const existUser = await userCol.where({
				mobile: mobile
			}).get();
			
			if (existUser.data.length > 0) {
				return {
					code: -1,
					message: '该手机号已注册'
				};
			}
		}
		
		// 生成4位随机验证码
		const code = Math.floor(1000 + Math.random() * 9000).toString();
		
		// 保存验证码到数据库
		const codeCollection = db.collection('verification_codes');
		
		// 删除该手机号之前的验证码
		await codeCollection.where({
			mobile: mobile,
			type: type
		}).remove();
		
		// 插入新验证码
		const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟后过期
		await codeCollection.add({
			code: code,
			mobile: mobile,
			type: type,
			expires_at: expiresAt,
			used: false,
			created_at: new Date()
		});
		
		// 这里应该调用短信服务发送验证码
		// 为了演示，我们直接返回验证码（生产环境中不应该这样做）
		console.log(`验证码发送到 ${mobile}: ${code}`);
		
		return {
			code: 0,
			message: '验证码发送成功',
			data: {
				// 开发环境返回验证码，生产环境不返回
				verificationCode: code
			}
		};
	} catch (error) {
		console.error('获取验证码失败:', error);
		return {
			code: -1,
			message: '获取验证码失败'
		};
	}
}

// 验证验证码
async function verifyCode({ mobile, code, type }) {
	try {
		// 验证参数
		if (!/^1[3-9]\d{9}$/.test(mobile)) {
			return {
				code: -1,
				message: '手机号格式不正确'
			};
		}

		if (!code || code.length !== 4) {
			return {
				code: -1,
				message: '验证码格式不正确'
			};
		}

		// 验证验证码
		const codeCollection = db.collection('verification_codes');
		const codeResult = await codeCollection.where({
			mobile: mobile,
			code: code,
			type: type,
			used: false,
			expires_at: db.command.gt(new Date())
		}).get();

		if (codeResult.data.length === 0) {
			return {
				code: -1,
				message: '验证码无效或已过期'
			};
		}

		return {
			code: 0,
			message: '验证码验证成功'
		};
	} catch (error) {
		console.error('验证码验证失败:', error);
		return {
			code: -1,
			message: '验证失败，请重试'
		};
	}
}

// 注册
async function register({ username, email, password, mobile, code, avatar, gender, birthday, region, bio }) {
	console.log('=== 开始注册流程 ===');
	console.log('参数:', { username, email, mobile, code: code ? '****' : null, password: password ? '****' : null });

	try {

		// 验证参数
		if (!username || username.length < 2) {
			console.log('用户名验证失败:', username);
			return {
				code: -1,
				message: '用户名至少2个字符'
			};
		}

		if (!/^1[3-9]\d{9}$/.test(mobile)) {
			console.log('手机号验证失败:', mobile);
			return {
				code: -1,
				message: '手机号格式不正确'
			};
		}

		if (!code || code.length !== 4) {
			console.log('验证码验证失败:', code);
			return {
				code: -1,
				message: '验证码格式不正确'
			};
		}

		if (!password || password.length < 6) {
			console.log('密码验证失败，长度:', password ? password.length : 0);
			return {
				code: -1,
				message: '密码至少6个字符'
			};
		}

		// 验证邮箱格式（如果提供了邮箱）
		if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
			console.log('邮箱格式验证失败:', email);
			return {
				code: -1,
				message: '邮箱格式不正确'
			};
		}
		
		// 验证验证码
		console.log('开始验证验证码...');
		const codeCollection = db.collection('verification_codes');
		const codeResult = await codeCollection.where({
			mobile: mobile,
			code: code,
			type: 'register',
			used: false,
			expires_at: db.command.gt(new Date())
		}).get();

		console.log('验证码查询结果:', codeResult.data.length, codeResult.data);

		if (codeResult.data.length === 0) {
			console.log('验证码验证失败');
			return {
				code: -1,
				message: '验证码无效或已过期'
			};
		}
		
		// 检查手机号是否已注册
		console.log('检查手机号是否已注册...');
		const userCol2 = db.collection('uni-id-users');
		const existUser = await userCol2.where({
			mobile: mobile
		}).get();

		console.log('用户存在性检查结果:', existUser.data.length);

		if (existUser.data.length > 0) {
			console.log('手机号已注册');
			return {
				code: -1,
				message: '该手机号已注册'
			};
		}
		
		// 获取数据库实例
		console.log('获取数据库实例...');
		const userCol3 = db.collection('uni-id-users');
		console.log('数据库实例获取成功');

		// 生成简单的7位数ID（暂时不检查唯一性）
		const planetId = Math.floor(1000000 + Math.random() * 9000000).toString();
		console.log('生成的 planetId:', planetId);

		// 加密密码
		const hashedPassword = crypto.createHash('md5').update(password + 'quwanplanet_salt').digest('hex');
		console.log('密码加密完成');

		// 创建完整的用户数据
		const userData = {
			username: username,
			email: email || '',
			mobile: mobile,
			password: hashedPassword,
			nickname: username,
			planet_id: planetId,
			avatar: avatar || '/static/default-avatar.jpg',
			gender: gender || 'unknown',
			birthday: birthday || '',
			location: region || '',
			bio: bio || '',
			register_date: new Date(),
			status: 0,
			level: 1,
			experience: 0,
			is_vip: false,
			mobile_confirmed: 1 // 已验证手机号
		};

		console.log('用户数据准备完成:', { ...userData, password: '****' });

		// 插入用户数据
		console.log('开始插入用户数据...');
		const insertResult = await userCol3.add(userData);
		console.log('用户数据插入结果:', insertResult);

		if (insertResult.id) {
			console.log('用户数据插入成功，ID:', insertResult.id);

			// 标记验证码为已使用
			await codeCollection.doc(codeResult.data[0]._id).update({
				used: true
			});
			console.log('验证码已标记为已使用');

			// 验证用户是否真的保存到数据库
			const savedUser = await userCol3.doc(insertResult.id).get();
			console.log('验证保存的用户信息:', savedUser.data);

			// 确保返回正确的用户信息结构
			const finalUserInfo = savedUser.data && savedUser.data.length > 0 ? savedUser.data[0] : userData;
			console.log('最终返回的用户信息:', finalUserInfo);

			return {
				code: 0,
				message: '注册成功',
				data: {
					uid: insertResult.id,
					planet_id: planetId,
					userInfo: finalUserInfo,
					token: 'temp_token_' + insertResult.id
				}
			};
		} else {
			console.log('用户数据插入失败，没有返回ID');
			return {
				code: -1,
				message: '注册失败：用户数据保存失败'
			};
		}
	} catch (error) {
		console.error('注册失败，详细错误:', error);
		return {
			code: -1,
			message: '注册失败: ' + (error.message || error.toString())
		};
	}
}

// 登录
async function login({ mobile, password }) {
	try {
		// 验证参数
		if (!/^1[3-9]\d{9}$/.test(mobile)) {
			return {
				code: -1,
				message: '手机号格式不正确'
			};
		}
		
		if (!password || password.length < 6) {
			return {
				code: -1,
				message: '密码格式不正确'
			};
		}
		
		// 加密密码进行比较
		const hashedPassword = crypto.createHash('md5').update(password + 'quwanplanet_salt').digest('hex');

		// 查询用户
		const userCol4 = db.collection('uni-id-users');
		const userQuery = await userCol4.where({
			mobile: mobile,
			password: hashedPassword
		}).get();

		if (userQuery.data.length > 0) {
			const userInfo = userQuery.data[0];

			// 更新最后登录时间
			await userCol4.doc(userInfo._id).update({
				last_login_date: new Date()
			});

			// 生成简单的token（实际项目中应该使用JWT）
			const token = crypto.createHash('md5').update(userInfo._id + Date.now() + 'quwanplanet_token').digest('hex');

			const loginResult = {
				code: 0,
				message: '登录成功',
				data: {
					userInfo: userInfo,
					token: token
				}
			};

			console.log('🔍 云函数登录返回结果:', JSON.stringify(loginResult, null, 2));
			return loginResult;
		} else {
			return {
				code: -1,
				message: '手机号或密码错误'
			};
		}
	} catch (error) {
		console.error('登录失败:', error);
		return {
			code: -1,
			message: '登录失败'
		};
	}
}
