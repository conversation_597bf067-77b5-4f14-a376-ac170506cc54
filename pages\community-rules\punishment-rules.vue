<template>
	<view class="page-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 头部导航 -->
		<view class="header">
			<view class="header-content">
				<view class="back-button" @click="goBack">
					<text class="back-icon">‹</text>
				</view>
				<text class="header-title">处罚规则</text>
				<view class="header-placeholder"></view>
			</view>
		</view>

		<!-- 主要内容 -->
		<view class="main-content">
			<scroll-view scroll-y="true" class="scroll-container">
				<view class="document-content">
					<!-- 文档标题 -->
					<view class="document-header">
						<text class="document-title">闲伴社区处罚规则</text>
						<text class="document-subtitle">社区违规行为处罚标准</text>
						<view class="document-info">
							<text class="info-item">版本：v2.2</text>
							<text class="info-item">发布日期：2025年6月4日</text>
							<text class="info-item">生效日期：2025年6月4日</text>
						</view>
					</view>

					<!-- 第1章 -->
					<view class="content-section">
						<text class="section-title">1. 总则</text>
						<view class="section-item">
							<text class="item-number">1.1</text>
							<view class="item-content">
								<text class="item-title">制定目的</text>
								<text class="item-text">为维护闲伴社区良好秩序，保护用户合法权益，规范平台管理行为，特制定本处罚规则。</text>
							</view>
						</view>

						<view class="section-item">
							<text class="item-number">1.2</text>
							<view class="item-content">
								<text class="item-title">适用范围</text>
								<text class="item-text">本规则适用于所有使用闲伴平台服务的用户，包括但不限于注册用户、游客用户等。</text>
							</view>
						</view>

						<view class="section-item">
							<text class="item-number">1.3</text>
							<view class="item-content">
								<text class="item-title">基本原则</text>
								<text class="item-text">处罚遵循公平、公正、公开原则，坚持教育与惩戒相结合，过罚相当的基本要求。</text>
							</view>
						</view>
					</view>

					<!-- 第2章 -->
					<view class="content-section">
						<text class="section-title">2. 违规行为分类</text>
						<view class="section-item">
							<text class="item-number">2.1</text>
							<view class="item-content">
								<text class="item-title">政治敏感类违规</text>
								<text class="item-text">包括但不限于发布危害国家安全、煽动民族仇恨、传播极端思想等内容。</text>
							</view>
						</view>

						<view class="section-item">
							<text class="item-number">2.2</text>
							<view class="item-content">
								<text class="item-title">有害信息类违规</text>
								<text class="item-text">包括但不限于色情低俗、暴力血腥、赌博诈骗、毒品相关等有害信息。</text>
							</view>
						</view>

						<view class="section-item">
							<text class="item-number">2.3</text>
							<view class="item-content">
								<text class="item-title">社区秩序类违规</text>
								<text class="item-text">包括但不限于恶意刷屏、骚扰他人、发布虚假信息、冒充官方等行为。</text>
							</view>
						</view>

						<view class="section-item">
							<text class="item-number">2.4</text>
							<view class="item-content">
								<text class="item-title">隐私侵犯类违规</text>
								<text class="item-text">包括但不限于未经同意公开他人信息、恶意收集隐私数据等行为。</text>
							</view>
						</view>

						<view class="section-item">
							<text class="item-number">2.5</text>
							<view class="item-content">
								<text class="item-title">技术违规类</text>
								<text class="item-text">包括但不限于使用外挂软件、恶意攻击、传播病毒等技术手段破坏平台的行为。</text>
							</view>
						</view>
					</view>

					<!-- 第3章 - 处罚标准 -->
					<view class="content-section">
						<text class="section-title">3. 违规行为处罚标准</text>

						<!-- 3.1 政治敏感类违规 -->
						<view class="section-item">
							<text class="item-number">3.1</text>
							<view class="item-content">
								<text class="item-title">政治敏感类违规处罚</text>
								<text class="item-text">涉及政治敏感、危害国家安全等内容的处罚标准：</text>

								<view class="table-container">
									<view class="table-header">
										<view class="header-cell">
											<text class="header-text">违规行为</text>
										</view>
										<view class="header-cell">
											<text class="header-text">首次处罚</text>
										</view>
										<view class="header-cell">
											<text class="header-text">再犯处罚</text>
										</view>
										<view class="header-cell">
											<text class="header-text">终极处罚</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">发布分裂国家言论</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 永久封禁</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">煽动民族仇恨、民族歧视</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 限制功能30天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">传播邪教、极端宗教思想</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 限制功能30天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">侮辱、诽谤民族英雄</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 限制功能30天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">泄露国家军事机密</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 永久封禁</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
									</view>
								</view>
							</view>
						</view>

						<!-- 3.2 有害信息处罚 -->
						<view class="section-item">
							<text class="item-number">3.2</text>
							<view class="item-content">
								<text class="item-title">有害信息处罚</text>
								<text class="item-text">发布色情、暴力、赌博等有害信息处罚标准：</text>

								<view class="table-container">
									<view class="table-header">
										<view class="header-cell">
											<text class="header-text">违规行为</text>
										</view>
										<view class="header-cell">
											<text class="header-text">首次处罚</text>
										</view>
										<view class="header-cell">
											<text class="header-text">再犯处罚</text>
										</view>
										<view class="header-cell">
											<text class="header-text">终极处罚</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">发布色情、低俗内容</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 限制功能7天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能30天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">发布血腥暴力内容</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 限制功能7天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能30天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">发布赌博、诈骗信息</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 限制功能15天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">发布毒品相关内容</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 永久封禁</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">教唆未成年人犯罪</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 永久封禁</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
									</view>
								</view>
							</view>
						</view>

						<!-- 3.3 社区秩序违规处罚 -->
						<view class="section-item">
							<text class="item-number">3.3</text>
							<view class="item-content">
								<text class="item-title">社区秩序违规处罚</text>
								<text class="item-text">恶意骚扰、刷屏、发布个人联系方式等行为处罚标准：</text>

								<view class="table-container">
									<view class="table-header">
										<view class="header-cell">
											<text class="header-text">违规行为</text>
										</view>
										<view class="header-cell">
											<text class="header-text">首次处罚</text>
										</view>
										<view class="header-cell">
											<text class="header-text">再犯处罚</text>
										</view>
										<view class="header-cell">
											<text class="header-text">终极处罚</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">恶意刷屏、重复发布</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 限制功能3天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能15天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能30天</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">发布个人账号、联系方式</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 口头警告</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能7天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能30天</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">恶意骚扰、跟踪他人</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能7天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能30天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">发布虚假活动信息</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 限制功能7天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能30天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">冒充官方客服</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 限制功能15天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
									</view>
								</view>
							</view>
						</view>

						<!-- 3.4 个人信息和隐私类违规 -->
						<view class="section-item">
							<text class="item-number">3.4</text>
							<view class="item-content">
								<text class="item-title">个人信息和隐私类违规</text>
								<text class="item-text">侵犯他人隐私、恶意收集个人信息等行为处罚标准：</text>

								<view class="table-container">
									<view class="table-header">
										<view class="header-cell">
											<text class="header-text">违规行为</text>
										</view>
										<view class="header-cell">
											<text class="header-text">首次处罚</text>
										</view>
										<view class="header-cell">
											<text class="header-text">再犯处罚</text>
										</view>
										<view class="header-cell">
											<text class="header-text">终极处罚</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">未经同意公开他人个人信息</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">删除内容 + 限制功能7天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能30天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">恶意收集他人隐私数据</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能15天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能30天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">利用技术手段窃取他人信息</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
									</view>
								</view>
							</view>
						</view>

						<!-- 3.5 技术违规和破坏行为 -->
						<view class="section-item">
							<text class="item-number">3.5</text>
							<view class="item-content">
								<text class="item-title">技术违规和破坏行为</text>
								<text class="item-text">使用技术手段破坏平台秩序的行为处罚标准：</text>

								<view class="table-container">
									<view class="table-header">
										<view class="header-cell">
											<text class="header-text">违规行为</text>
										</view>
										<view class="header-cell">
											<text class="header-text">首次处罚</text>
										</view>
										<view class="header-cell">
											<text class="header-text">再犯处罚</text>
										</view>
										<view class="header-cell">
											<text class="header-text">终极处罚</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">使用外挂软件、恶意脚本</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能15天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">临时封禁30天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">使用爬虫程序、数据抓取工具</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能7天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">限制功能30天</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
									</view>

									<view class="table-row">
										<view class="table-cell">
											<text class="cell-text">恶意攻击平台服务器</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">永久封禁账号</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
										<view class="table-cell">
											<text class="cell-text">移交相关部门处理</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 第4章 - 处罚执行程序 -->
					<view class="content-section">
						<text class="section-title">4. 处罚执行程序</text>

						<view class="section-item">
							<text class="item-number">4.1</text>
							<view class="item-content">
								<text class="item-title">违规认定</text>
								<text class="item-text">平台通过以下方式认定违规行为：</text>
								<view class="sub-items">
									<view class="sub-item">
										<text class="sub-item-number">4.1.1</text>
										<text class="sub-item-text">用户举报：其他用户举报违规内容</text>
									</view>
									<view class="sub-item">
										<text class="sub-item-number">4.1.2</text>
										<text class="sub-item-text">系统检测：AI智能识别违规内容</text>
									</view>
									<view class="sub-item">
										<text class="sub-item-number">4.1.3</text>
										<text class="sub-item-text">人工审核：专业审核团队人工审查</text>
									</view>
									<view class="sub-item">
										<text class="sub-item-number">4.1.4</text>
										<text class="sub-item-text">主动巡查：平台主动发现违规行为</text>
									</view>
								</view>
							</view>
						</view>

						<view class="section-item">
							<text class="item-number">4.2</text>
							<view class="item-content">
								<text class="item-title">处罚通知</text>
								<text class="item-text">对违规用户的处罚通知包含以下信息：</text>
								<view class="sub-items">
									<view class="sub-item">
										<text class="sub-item-number">4.2.1</text>
										<text class="sub-item-text">违规行为具体描述</text>
									</view>
									<view class="sub-item">
										<text class="sub-item-number">4.2.2</text>
										<text class="sub-item-text">违反的具体条款</text>
									</view>
									<view class="sub-item">
										<text class="sub-item-number">4.2.3</text>
										<text class="sub-item-text">处罚措施和期限</text>
									</view>
									<view class="sub-item">
										<text class="sub-item-number">4.2.4</text>
										<text class="sub-item-text">申诉渠道和时限</text>
									</view>
									<view class="sub-item">
										<text class="sub-item-number">4.2.5</text>
										<text class="sub-item-text">处罚生效时间</text>
									</view>
								</view>
							</view>
						</view>

						<view class="section-item">
							<text class="item-number">4.3</text>
							<view class="item-content">
								<text class="item-title">申诉机制</text>
								<text class="item-text">用户对处罚决定不服的，可在收到通知后7日内提出申诉：</text>
								<view class="sub-items">
									<view class="sub-item">
										<text class="sub-item-number">4.3.1</text>
										<text class="sub-item-text">申诉渠道：通过平台申诉功能或邮件申诉</text>
									</view>
									<view class="sub-item">
										<text class="sub-item-number">4.3.2</text>
										<text class="sub-item-text">申诉材料：提供相关证据和说明</text>
									</view>
									<view class="sub-item">
										<text class="sub-item-number">4.3.3</text>
										<text class="sub-item-text">处理时限：平台在3个工作日内处理申诉</text>
									</view>
									<view class="sub-item">
										<text class="sub-item-number">4.3.4</text>
										<text class="sub-item-text">申诉结果：维持原处罚或撤销/减轻处罚</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PunishmentRulesNew',
	data() {
		return {
			statusBarHeight: 0
		};
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 0;
	},
	methods: {
		goBack() {
			// 尝试返回上一页，如果没有上一页则跳转到社区公约页面
			const pages = getCurrentPages();
			if (pages.length > 1) {
				uni.navigateBack({
					delta: 1
				});
			} else {
				uni.redirectTo({
					url: '/pages/community-rules/community-rules'
				});
			}
		}
	}
};
</script>

<style lang="scss">
.page-container {
	min-height: 100vh;
	background: #f8f9fa;
	display: flex;
	flex-direction: column;
}

.status-bar {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
}

.header {
	background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
	padding: 0 32rpx 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(255, 215, 0, 0.2);

	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;

		.back-button {
			width: 64rpx;
			height: 64rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50%;
			backdrop-filter: blur(10rpx);

			.back-icon {
				font-size: 36rpx;
				color: #333;
				font-weight: bold;
				line-height: 1;
			}
		}

		.header-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			flex: 1;
			text-align: center;
		}

		.header-placeholder {
			width: 64rpx;
		}
	}
}

.main-content {
	flex: 1;
	overflow: hidden;

	.scroll-container {
		height: 100%;
	}
}

.document-content {
	padding: 32rpx;

	.document-header {
		text-align: center;
		margin-bottom: 48rpx;
		padding: 48rpx 32rpx;
		background: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

		.document-title {
			font-size: 48rpx;
			font-weight: 700;
			color: #333;
			display: block;
			margin-bottom: 16rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}

		.document-subtitle {
			font-size: 28rpx;
			color: #999;
			display: block;
			margin-bottom: 32rpx;
			font-style: italic;
		}

		.document-info {
			display: flex;
			justify-content: center;
			flex-wrap: wrap;
			gap: 24rpx;

			.info-item {
				font-size: 24rpx;
				color: #666;
				padding: 8rpx 16rpx;
				background: #f8f9fa;
				border-radius: 8rpx;
			}
		}
	}

	.content-section {
		margin-bottom: 48rpx;
		background: #fff;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

		.section-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			display: block;
			margin-bottom: 32rpx;
			padding: 24rpx 32rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
			border-radius: 12rpx;
			text-align: center;
			box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.2);
		}

		.section-item {
			display: flex;
			margin-bottom: 24rpx;
			align-items: flex-start;

			&:last-child {
				margin-bottom: 0;
			}

			.item-number {
				font-size: 28rpx;
				color: #FFD700;
				font-weight: 600;
				margin-right: 20rpx;
				flex-shrink: 0;
				margin-top: 4rpx;
				min-width: 60rpx;
			}

			.item-content {
				flex: 1;
				line-height: 1.6;

				.item-title {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
					display: block;
					margin-bottom: 12rpx;
				}

				.item-text {
					font-size: 28rpx;
					color: #666;
					line-height: 1.8;
					text-align: justify;
					margin-bottom: 16rpx;
				}

				// 子项目样式
				.sub-items {
					margin-top: 16rpx;

					.sub-item {
						display: flex;
						margin-bottom: 12rpx;
						align-items: flex-start;

						&:last-child {
							margin-bottom: 0;
						}

						.sub-item-number {
							font-size: 24rpx;
							color: #999;
							margin-right: 12rpx;
							flex-shrink: 0;
							margin-top: 2rpx;
							min-width: 80rpx;
						}

						.sub-item-text {
							font-size: 26rpx;
							color: #666;
							line-height: 1.6;
							flex: 1;
						}
					}
				}

				// 表格样式
				.table-container {
					margin: 20rpx 0;
					background: #ffffff;
					border-radius: 8rpx;
					overflow: hidden;
					box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
					border: 2rpx solid #ddd;
					width: 100%;

					.table-header {
						display: flex;
						background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
						width: 100%;

						.header-cell {
							flex: 1;
							padding: 16rpx 12rpx;
							border-right: 1rpx solid rgba(255, 255, 255, 0.3);
							text-align: center;
							display: flex;
							align-items: center;
							justify-content: center;

							&:last-child {
								border-right: none;
							}

							.header-text {
								font-size: 24rpx;
								font-weight: bold;
								color: #333;
							}
						}
					}

					.table-row {
						display: flex;
						border-bottom: 1rpx solid #eee;
						width: 100%;

						&:last-child {
							border-bottom: none;
						}

						&:nth-child(even) {
							background: #f9f9f9;
						}

						.table-cell {
							flex: 1;
							padding: 16rpx 12rpx;
							border-right: 1rpx solid #eee;
							text-align: center;
							display: flex;
							align-items: center;
							justify-content: center;

							&:last-child {
								border-right: none;
							}

							&:first-child {
								flex: 1.5;
								background: rgba(255, 215, 0, 0.05);
								text-align: center;
								justify-content: center;
							}

							.cell-text {
								font-size: 22rpx;
								color: #666;
								line-height: 1.4;
								display: block;
								text-align: center;
							}
						}
					}
				}
			}
		}
	}
}
</style>
