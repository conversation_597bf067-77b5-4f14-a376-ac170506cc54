<template>
	<view class="category-page" :class="getPageClass()">
		<!-- 状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<!-- 顶部导航 -->
		<view class="top-nav" :style="{ background: getNavGradient() }">
			<view class="nav-left" @click="goBack">
				<view class="back-btn">
					<uni-icons type="left" size="18" color="#fff"></uni-icons>
				</view>
			</view>
			<view class="nav-center">
				<text class="nav-title">{{ categoryTitle }}</text>
			</view>
			<view class="nav-right">
				<view class="search-btn" @click="goToSearch">
					<uni-icons type="search" size="18" color="#fff"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 游戏玩伴页面 -->
		<view v-if="categoryTitle === '游戏玩伴'" class="game-companion-page">
			<!-- 游戏分类标签 -->
			<view class="game-tabs">
				<scroll-view scroll-x class="tabs-scroll" :show-scrollbar="false">
					<view class="tabs-container">
						<view
							class="tab-item"
							:class="{ active: activeGameTab === index }"
							v-for="(game, index) in gameCategories"
							:key="index"
							@click="switchGameTab(index)"
						>
							<image :src="game.icon" class="game-icon"></image>
							<text class="game-name">{{ game.name }}</text>
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 筛选条件 -->
			<view class="filter-bar">
				<view class="filter-item" @click="showRankFilter">
					<text class="filter-text">{{ selectedRank || '段位' }}</text>
					<uni-icons type="down" size="12" color="#666"></uni-icons>
				</view>
				<view class="filter-item" @click="showGenderFilter">
					<text class="filter-text">{{ selectedGender || '性别' }}</text>
					<uni-icons type="down" size="12" color="#666"></uni-icons>
				</view>
				<view class="filter-item" @click="showPriceFilter">
					<text class="filter-text">{{ selectedPrice || '价格' }}</text>
					<uni-icons type="down" size="12" color="#666"></uni-icons>
				</view>
				<view class="filter-item sort-btn" @click="toggleSort">
					<uni-icons type="list" size="14" color="#666"></uni-icons>
					<text class="filter-text">排序</text>
				</view>
			</view>

			<!-- 陪玩师列表 -->
			<view class="companion-list">
				<view
					class="companion-card"
					v-for="companion in getGameCompanions()"
					:key="companion.id"
					@click="viewCompanion(companion)"
				>
					<view class="card-header">
						<view class="avatar-section">
							<image :src="companion.avatar" class="companion-avatar"></image>
							<view v-if="companion.isOnline" class="online-dot"></view>
							<view class="gender-tag" :class="companion.gender">
								<uni-icons :type="companion.gender === 'male' ? 'male' : 'female'" size="10" color="#fff"></uni-icons>
							</view>
						</view>
						<view class="info-section">
							<view class="name-row">
								<text class="companion-name">{{ companion.name }}</text>
								<view class="rank-badge">
									<text class="rank-text">{{ companion.rank }}</text>
								</view>
							</view>
							<view class="tags-row">
								<text class="tag" v-for="tag in companion.tags" :key="tag">{{ tag }}</text>
							</view>
							<view class="stats-row">
								<view class="stat-item">
									<uni-icons type="star-filled" size="12" color="#FFD700"></uni-icons>
									<text class="stat-text">{{ companion.rating }}</text>
								</view>
								<view class="stat-item">
									<uni-icons type="heart-filled" size="12" color="#FF6B6B"></uni-icons>
									<text class="stat-text">{{ companion.likes }}%</text>
								</view>
								<view class="stat-item">
									<uni-icons type="checkmarkempty" size="12" color="#4ECDC4"></uni-icons>
									<text class="stat-text">{{ companion.orders }}单</text>
								</view>
							</view>
						</view>
						<view class="price-section">
							<text class="price-text">￥{{ companion.price }}</text>
							<text class="price-unit">/小时</text>
						</view>
					</view>
					<view class="card-footer">
						<text class="companion-intro">{{ companion.intro }}</text>
						<view class="action-buttons">
							<view class="btn-secondary" @click.stop="chatWithCompanion(companion)">
								<uni-icons type="chat" size="14" color="#666"></uni-icons>
								<text>聊天</text>
							</view>
							<view class="btn-primary" @click.stop="bookCompanion(companion)">
								<text>立即下单</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 组局约伴页面 -->
		<view v-else-if="categoryTitle === '组局约伴'" class="organize-companion-page">
			<!-- 活动分类 -->
			<view class="activity-categories">
				<scroll-view scroll-x class="categories-scroll" :show-scrollbar="false">
					<view class="categories-container">
						<view
							class="category-chip"
							:class="{ active: activeActivityCategory === index }"
							v-for="(category, index) in activityCategories"
							:key="index"
							@click="switchActivityCategory(index)"
						>
							<view class="chip-icon">
								<uni-icons :type="category.icon" size="16" color="#fff"></uni-icons>
							</view>
							<text class="chip-text">{{ category.name }}</text>
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 快速筛选 -->
			<view class="quick-filters">
				<view class="filter-chip" :class="{ active: selectedTime === '今天' }" @click="selectTime('今天')">
					<text>今天</text>
				</view>
				<view class="filter-chip" :class="{ active: selectedTime === '明天' }" @click="selectTime('明天')">
					<text>明天</text>
				</view>
				<view class="filter-chip" :class="{ active: selectedTime === '周末' }" @click="selectTime('周末')">
					<text>周末</text>
				</view>
				<view class="filter-chip" :class="{ active: selectedDistance === '附近' }" @click="selectDistance('附近')">
					<text>附近</text>
				</view>
			</view>

			<!-- 活动列表 -->
			<view class="activity-list">
				<view
					class="activity-card"
					v-for="activity in getOrganizeActivities()"
					:key="activity.id"
					@click="viewActivity(activity)"
				>
					<view class="activity-image">
						<image :src="activity.image" class="activity-bg" mode="aspectFill"></image>
						<view class="image-overlay">
							<view class="activity-tags">
								<text v-if="activity.isHot" class="tag hot">热门</text>
								<text v-if="activity.isNew" class="tag new">新活动</text>
							</view>
							<view class="participants-info">
								<text class="participants-text">{{ activity.participants }}/{{ activity.maxParticipants }}人</text>
							</view>
						</view>
					</view>
					<view class="activity-content">
						<text class="activity-title">{{ activity.title }}</text>
						<view class="activity-meta">
							<view class="meta-item">
								<uni-icons type="location" size="12" color="#999"></uni-icons>
								<text class="meta-text">{{ activity.location }}</text>
							</view>
							<view class="meta-item">
								<uni-icons type="calendar" size="12" color="#999"></uni-icons>
								<text class="meta-text">{{ activity.time }}</text>
							</view>
						</view>
						<view class="organizer-info">
							<image :src="activity.organizer.avatar" class="organizer-avatar"></image>
							<text class="organizer-name">{{ activity.organizer.name }}</text>
							<view class="organizer-badge" v-if="activity.organizer.isVerified">
								<uni-icons type="checkmarkempty" size="10" color="#4ECDC4"></uni-icons>
							</view>
						</view>
						<view class="activity-footer">
							<text class="activity-price">{{ activity.price }}</text>
							<view class="join-btn" @click.stop="joinActivity(activity)">
								<text>立即参加</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 城市玩伴页面 -->
		<view v-else-if="categoryTitle === '城市玩伴'" class="city-companion-page">
			<!-- 城市场景分类 -->
			<view class="scene-categories">
				<view class="categories-grid">
					<view
						class="scene-item"
						v-for="(scene, index) in cityScenes"
						:key="index"
						@click="selectScene(scene)"
					>
						<view class="scene-icon" :style="{ background: scene.gradient }">
							<uni-icons :type="scene.icon" size="20" color="#fff"></uni-icons>
						</view>
						<text class="scene-name">{{ scene.name }}</text>
					</view>
				</view>
			</view>

			<!-- 推荐玩伴 -->
			<view class="companion-section">
				<view class="section-header">
					<text class="section-title">附近的玩伴</text>
					<view class="filter-btn" @click="showCityFilter">
						<uni-icons type="tune" size="14" color="#666"></uni-icons>
						<text>筛选</text>
					</view>
				</view>

				<view class="city-companion-list">
					<view
						class="city-companion-card"
						v-for="companion in getCityCompanions()"
						:key="companion.id"
						@click="viewCityCompanion(companion)"
					>
						<view class="companion-header">
							<image :src="companion.avatar" class="companion-photo"></image>
							<view class="companion-info">
								<view class="name-age">
									<text class="companion-name">{{ companion.name }}</text>
									<text class="companion-age">{{ companion.age }}岁</text>
								</view>
								<view class="companion-tags">
									<text class="interest-tag" v-for="interest in companion.interests" :key="interest">{{ interest }}</text>
								</view>
								<view class="location-info">
									<uni-icons type="location" size="12" color="#999"></uni-icons>
									<text class="distance-text">距离{{ companion.distance }}</text>
								</view>
							</view>
							<view class="online-status" :class="{ online: companion.isOnline }">
								<text class="status-text">{{ companion.isOnline ? '在线' : '离线' }}</text>
							</view>
						</view>
						<text class="companion-mood">{{ companion.mood }}</text>
						<view class="companion-footer">
							<view class="action-btns">
								<view class="btn-like" @click.stop="likeCompanion(companion)">
									<uni-icons type="heart" size="16" :color="companion.isLiked ? '#FF6B6B' : '#ccc'"></uni-icons>
								</view>
								<view class="btn-chat" @click.stop="chatWithCityCompanion(companion)">
									<uni-icons type="chat" size="16" color="#4ECDC4"></uni-icons>
									<text>聊天</text>
								</view>
								<view class="btn-invite" @click.stop="inviteCompanion(companion)">
									<text>邀请</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				categoryId: '',
				categoryTitle: '',
				subCategories: [],

				// 游戏玩伴相关数据
				activeGameTab: 0,
				selectedRank: '',
				selectedGender: '',
				selectedPrice: '',
				gameCategories: [
					{ name: '王者荣耀', icon: '/static/images/games/wzry.png' },
					{ name: '和平精英', icon: '/static/images/games/hpjy.png' },
					{ name: 'LOL', icon: '/static/images/games/lol.png' },
					{ name: '原神', icon: '/static/images/games/ys.png' },
					{ name: 'CF', icon: '/static/images/games/cf.png' },
					{ name: 'CSGO', icon: '/static/images/games/csgo.png' }
				],
				gameCompanions: [
					{
						id: 1,
						name: '小雨',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						gender: 'female',
						rank: '王者30星',
						rating: 4.9,
						likes: 98,
						orders: 1234,
						price: 15,
						isOnline: true,
						tags: ['温柔', '技术好', '不坑'],
						intro: '王者荣耀资深玩家，擅长打野和辅助，带你轻松上分~'
					},
					{
						id: 2,
						name: '阿杰',
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						gender: 'male',
						rank: '荣耀王者',
						rating: 4.8,
						likes: 95,
						orders: 856,
						price: 12,
						isOnline: false,
						tags: ['幽默', '稳定', '大神'],
						intro: '专业代练，各种英雄都会，保证胜率，欢迎来撩~'
					}
				],

				// 组局约伴相关数据
				activeActivityCategory: 0,
				selectedTime: '',
				selectedDistance: '',
				activityCategories: [
					{ name: '户外运动', icon: 'navigate' },
					{ name: '聚餐聚会', icon: 'shop' },
					{ name: '文化娱乐', icon: 'star' },
					{ name: '兴趣爱好', icon: 'heart' }
				],
				organizeActivities: [
					{
						id: 1,
						title: '周末户外BBQ聚会',
						location: '奥林匹克森林公园',
						time: '周六 14:00-18:00',
						participants: 12,
						maxParticipants: 20,
						price: '￥68/人',
						image: '/static/images/activities/bbq.jpg',
						isHot: true,
						isNew: false,
						organizer: {
							name: '户外达人',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
							isVerified: true
						}
					},
					{
						id: 2,
						title: '深山露营观星',
						location: '怀柔雁栖湖',
						time: '周六 15:00-次日10:00',
						participants: 8,
						maxParticipants: 15,
						price: '￥128/人',
						image: '/static/images/activities/camping.jpg',
						isHot: false,
						isNew: true,
						organizer: {
							name: '露营专家',
							avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
							isVerified: true
						}
					}
				],

				// 城市玩伴相关数据
				cityScenes: [
					{ name: '看电影', icon: 'videocam', gradient: 'linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%)' },
					{ name: '逛街购物', icon: 'shop', gradient: 'linear-gradient(135deg, #4ECDC4 0%, #6FE7DD 100%)' },
					{ name: '咖啡厅', icon: 'star', gradient: 'linear-gradient(135deg, #FFD700 0%, #FFC107 100%)' },
					{ name: '展览馆', icon: 'image', gradient: 'linear-gradient(135deg, #A8E6CF 0%, #88D8A3 100%)' },
					{ name: '音乐会', icon: 'sound', gradient: 'linear-gradient(135deg, #9B59B6 0%, #BB6BD9 100%)' },
					{ name: '夜市', icon: 'location', gradient: 'linear-gradient(135deg, #45B7D1 0%, #6BC5E0 100%)' }
				],
				cityCompanions: [
					{
						id: 1,
						name: '小美',
						age: 24,
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						interests: ['摄影', '咖啡', '旅行'],
						distance: '1.2km',
						isOnline: true,
						isLiked: false,
						mood: '今天天气不错，想找个人一起去798看展览，有没有志同道合的小伙伴呀~'
					},
					{
						id: 2,
						name: '阿强',
						age: 28,
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						interests: ['电影', '美食', '健身'],
						distance: '800m',
						isOnline: false,
						isLiked: true,
						mood: '刚搬到北京，想认识一些新朋友，一起探索这座城市的美好~'
					},
					{
						id: 3,
						name: '小雨',
						age: 22,
						avatar: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
						interests: ['音乐', '书店', '甜品'],
						distance: '2.1km',
						isOnline: true,
						isLiked: false,
						mood: '周末想去三里屯逛逛，顺便看个电影，有人一起吗？'
					}
				]
			}
		},
		
		onLoad(options) {
			// 获取状态栏高度
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight;
			
			// 获取传递的参数
			if (options.id) {
				this.categoryId = options.id;
			}
			if (options.title) {
				this.categoryTitle = options.title;
			}
			if (options.subCategories) {
				try {
					this.subCategories = JSON.parse(options.subCategories);
				} catch (e) {
					console.error('解析子分类失败:', e);
					this.subCategories = [];
				}
			}
		},
		
		methods: {
			goBack() {
				uni.navigateBack();
			},

			goToSearch() {
				uni.navigateTo({
					url: '/pages/search/search'
				});
			},

			onSubCategoryClick(subCategory) {
				console.log('点击子分类:', subCategory);
				// 跳转到具体的活动列表页
				uni.navigateTo({
					url: `/pages/activity-list/activity-list?category=${this.categoryTitle}&subCategory=${subCategory}`
				});
			},

			// 获取页面样式类
			getPageClass() {
				return this.categoryTitle.replace(/\s+/g, '-').toLowerCase();
			},

			// 获取导航栏渐变色
			getNavGradient() {
				const gradients = {
					'组局约伴': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
					'游戏玩伴': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
					'城市玩伴': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
				};
				return gradients[this.categoryTitle] || 'linear-gradient(135deg, #FFD700 0%, #FFC107 100%)';
			},

			// 游戏玩伴相关方法
			switchGameTab(index) {
				this.activeGameTab = index;
			},

			showRankFilter() {
				uni.showActionSheet({
					itemList: ['青铜', '白银', '黄金', '铂金', '钻石', '星耀', '王者'],
					success: (res) => {
						const ranks = ['青铜', '白银', '黄金', '铂金', '钻石', '星耀', '王者'];
						this.selectedRank = ranks[res.tapIndex];
					}
				});
			},

			showGenderFilter() {
				uni.showActionSheet({
					itemList: ['不限', '小姐姐', '小哥哥'],
					success: (res) => {
						const genders = ['不限', '小姐姐', '小哥哥'];
						this.selectedGender = genders[res.tapIndex];
					}
				});
			},

			showPriceFilter() {
				uni.showActionSheet({
					itemList: ['不限', '10元以下', '10-20元', '20-30元', '30元以上'],
					success: (res) => {
						const prices = ['不限', '10元以下', '10-20元', '20-30元', '30元以上'];
						this.selectedPrice = prices[res.tapIndex];
					}
				});
			},

			toggleSort() {
				uni.showActionSheet({
					itemList: ['综合排序', '价格从低到高', '价格从高到低', '评分最高', '接单最多'],
					success: (res) => {
						console.log('选择排序方式:', res.tapIndex);
					}
				});
			},

			getGameCompanions() {
				return this.gameCompanions;
			},

			viewCompanion(companion) {
				console.log('查看陪玩师:', companion.name);
			},

			chatWithCompanion(companion) {
				console.log('与陪玩师聊天:', companion.name);
			},

			bookCompanion(companion) {
				console.log('预订陪玩师:', companion.name);
			},

			// 组局约伴相关方法
			switchActivityCategory(index) {
				this.activeActivityCategory = index;
			},

			selectTime(time) {
				this.selectedTime = this.selectedTime === time ? '' : time;
			},

			selectDistance(distance) {
				this.selectedDistance = this.selectedDistance === distance ? '' : distance;
			},

			getOrganizeActivities() {
				return this.organizeActivities;
			},

			joinActivity(activity) {
				console.log('参加活动:', activity.title);
			},

			// 城市玩伴相关方法
			selectScene(scene) {
				console.log('选择场景:', scene.name);
			},

			showCityFilter() {
				uni.showActionSheet({
					itemList: ['附近优先', '年龄筛选', '兴趣筛选', '在线状态'],
					success: (res) => {
						console.log('选择筛选条件:', res.tapIndex);
					}
				});
			},

			getCityCompanions() {
				return this.cityCompanions;
			},

			viewCityCompanion(companion) {
				console.log('查看城市玩伴:', companion.name);
			},

			likeCompanion(companion) {
				companion.isLiked = !companion.isLiked;
				console.log('点赞玩伴:', companion.name);
			},

			chatWithCityCompanion(companion) {
				console.log('与城市玩伴聊天:', companion.name);
			},

			inviteCompanion(companion) {
				console.log('邀请玩伴:', companion.name);
			},

			getGradientByIndex(index) {
				const gradients = [
					'linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%)',
					'linear-gradient(135deg, #4ECDC4 0%, #6FE7DD 100%)',
					'linear-gradient(135deg, #FFD700 0%, #FFC107 100%)',
					'linear-gradient(135deg, #A8E6CF 0%, #88D8A3 100%)',
					'linear-gradient(135deg, #9B59B6 0%, #BB6BD9 100%)',
					'linear-gradient(135deg, #45B7D1 0%, #6BC5E0 100%)',
					'linear-gradient(135deg, #FF8A80 0%, #FFAB91 100%)',
					'linear-gradient(135deg, #CE93D8 0%, #F8BBD9 100%)'
				];
				return gradients[index % gradients.length];
			},
			
			// 通用方法
			viewMore() {
				console.log('查看更多');
			},

			viewActivity(activity) {
				console.log('查看活动详情:', activity.title);
			},

			viewMore() {
				console.log('查看更多活动');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.category-page {
		background: #f5f6fa;
		min-height: 100vh;
	}

	.status-bar {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	// 顶部导航
	.top-nav {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16rpx 24rpx;

		.nav-left, .nav-right {
			.back-btn, .search-btn {
				width: 40rpx;
				height: 40rpx;
				border-radius: 20rpx;
				background: rgba(255, 255, 255, 0.2);
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;

				&:active {
					background: rgba(255, 255, 255, 0.3);
					transform: scale(0.95);
				}
			}
		}

		.nav-center {
			flex: 1;
			text-align: center;

			.nav-title {
				font-size: 34rpx;
				font-weight: 700;
				color: #fff;
			}
		}
	}

	// 游戏玩伴页面样式
	.game-companion-page {
		// 游戏分类标签
		.game-tabs {
			background: #fff;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #f0f0f0;

			.tabs-scroll {
				white-space: nowrap;

				.tabs-container {
					display: flex;
					padding: 0 24rpx;
					gap: 24rpx;

					.tab-item {
						display: flex;
						flex-direction: column;
						align-items: center;
						gap: 8rpx;
						padding: 16rpx 20rpx;
						border-radius: 16rpx;
						transition: all 0.3s ease;
						flex-shrink: 0;

						&.active {
							background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

							.game-name {
								color: #fff;
							}
						}

						.game-icon {
							width: 48rpx;
							height: 48rpx;
							border-radius: 12rpx;
						}

						.game-name {
							font-size: 24rpx;
							color: #333;
							font-weight: 500;
						}
					}
				}
			}
		}

		// 筛选条件
		.filter-bar {
			display: flex;
			align-items: center;
			padding: 20rpx 24rpx;
			background: #fff;
			gap: 20rpx;
			border-bottom: 1rpx solid #f0f0f0;

			.filter-item {
				display: flex;
				align-items: center;
				gap: 8rpx;
				padding: 12rpx 16rpx;
				background: #f8f9fa;
				border-radius: 20rpx;
				transition: all 0.3s ease;

				&.sort-btn {
					margin-left: auto;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

					.filter-text {
						color: #fff;
					}
				}

				&:active {
					transform: scale(0.95);
				}

				.filter-text {
					font-size: 26rpx;
					color: #666;
				}
			}
		}

		// 陪玩师列表
		.companion-list {
			padding: 20rpx 24rpx;

			.companion-card {
				background: #fff;
				border-radius: 20rpx;
				padding: 24rpx;
				margin-bottom: 20rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

				.card-header {
					display: flex;
					align-items: flex-start;
					gap: 16rpx;
					margin-bottom: 16rpx;

					.avatar-section {
						position: relative;

						.companion-avatar {
							width: 80rpx;
							height: 80rpx;
							border-radius: 40rpx;
							border: 3rpx solid #f0f0f0;
						}

						.online-dot {
							position: absolute;
							bottom: 4rpx;
							right: 4rpx;
							width: 16rpx;
							height: 16rpx;
							background: #2ed573;
							border-radius: 50%;
							border: 2rpx solid #fff;
						}

						.gender-tag {
							position: absolute;
							top: -4rpx;
							right: -4rpx;
							width: 24rpx;
							height: 24rpx;
							border-radius: 12rpx;
							display: flex;
							align-items: center;
							justify-content: center;

							&.male {
								background: #4ECDC4;
							}

							&.female {
								background: #FF6B6B;
							}
						}
					}

					.info-section {
						flex: 1;

						.name-row {
							display: flex;
							align-items: center;
							gap: 12rpx;
							margin-bottom: 8rpx;

							.companion-name {
								font-size: 30rpx;
								font-weight: 700;
								color: #333;
							}

							.rank-badge {
								background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
								padding: 4rpx 12rpx;
								border-radius: 12rpx;

								.rank-text {
									font-size: 20rpx;
									color: #333;
									font-weight: 600;
								}
							}
						}

						.tags-row {
							margin-bottom: 8rpx;

							.tag {
								display: inline-block;
								background: #f8f9fa;
								color: #666;
								font-size: 22rpx;
								padding: 4rpx 12rpx;
								border-radius: 12rpx;
								margin-right: 8rpx;
							}
						}

						.stats-row {
							display: flex;
							gap: 16rpx;

							.stat-item {
								display: flex;
								align-items: center;
								gap: 4rpx;

								.stat-text {
									font-size: 22rpx;
									color: #666;
								}
							}
						}
					}

					.price-section {
						text-align: right;

						.price-text {
							font-size: 32rpx;
							font-weight: 700;
							color: #FF6B6B;
						}

						.price-unit {
							font-size: 22rpx;
							color: #999;
						}
					}
				}

				.card-footer {
					.companion-intro {
						font-size: 26rpx;
						color: #666;
						line-height: 1.5;
						margin-bottom: 16rpx;
						display: block;
					}

					.action-buttons {
						display: flex;
						gap: 16rpx;

						.btn-secondary {
							flex: 1;
							display: flex;
							align-items: center;
							justify-content: center;
							gap: 8rpx;
							padding: 16rpx;
							background: #f8f9fa;
							border-radius: 16rpx;
							font-size: 26rpx;
							color: #666;
							transition: all 0.3s ease;

							&:active {
								background: #e9ecef;
							}
						}

						.btn-primary {
							flex: 2;
							display: flex;
							align-items: center;
							justify-content: center;
							padding: 16rpx;
							background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
							border-radius: 16rpx;
							font-size: 26rpx;
							color: #fff;
							font-weight: 600;
							transition: all 0.3s ease;

							&:active {
								transform: scale(0.98);
							}
						}
					}
				}
			}
		}
	}

	// 组局约伴页面样式
	.organize-companion-page {
		// 活动分类
		.activity-categories {
			background: #fff;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #f0f0f0;

			.categories-scroll {
				white-space: nowrap;

				.categories-container {
					display: flex;
					padding: 0 24rpx;
					gap: 16rpx;

					.category-chip {
						display: flex;
						align-items: center;
						gap: 8rpx;
						padding: 12rpx 20rpx;
						background: #f8f9fa;
						border-radius: 20rpx;
						transition: all 0.3s ease;
						flex-shrink: 0;

						&.active {
							background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

							.chip-text {
								color: #fff;
							}
						}

						.chip-icon {
							width: 32rpx;
							height: 32rpx;
							border-radius: 16rpx;
							background: rgba(255, 255, 255, 0.2);
							display: flex;
							align-items: center;
							justify-content: center;
						}

						.chip-text {
							font-size: 26rpx;
							color: #666;
							font-weight: 500;
						}
					}
				}
			}
		}

		// 快速筛选
		.quick-filters {
			display: flex;
			padding: 20rpx 24rpx;
			gap: 16rpx;
			background: #fff;
			border-bottom: 1rpx solid #f0f0f0;

			.filter-chip {
				padding: 12rpx 24rpx;
				background: #f8f9fa;
				border-radius: 20rpx;
				font-size: 26rpx;
				color: #666;
				transition: all 0.3s ease;

				&.active {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: #fff;
				}

				&:active {
					transform: scale(0.95);
				}
			}
		}

		// 活动列表
		.activity-list {
			padding: 20rpx 24rpx;

			.activity-card {
				background: #fff;
				border-radius: 20rpx;
				overflow: hidden;
				margin-bottom: 20rpx;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

				.activity-image {
					height: 240rpx;
					position: relative;

					.activity-bg {
						width: 100%;
						height: 100%;
					}

					.image-overlay {
						position: absolute;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;
						background: linear-gradient(180deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.3) 100%);
						display: flex;
						justify-content: space-between;
						align-items: flex-start;
						padding: 16rpx;

						.activity-tags {
							display: flex;
							gap: 8rpx;

							.tag {
								padding: 6rpx 12rpx;
								border-radius: 12rpx;
								font-size: 20rpx;
								font-weight: 600;
								color: #fff;

								&.hot {
									background: rgba(255, 107, 107, 0.9);
								}

								&.new {
									background: rgba(78, 205, 196, 0.9);
								}
							}
						}

						.participants-info {
							background: rgba(0, 0, 0, 0.5);
							padding: 8rpx 16rpx;
							border-radius: 16rpx;

							.participants-text {
								font-size: 22rpx;
								color: #fff;
								font-weight: 600;
							}
						}
					}
				}

				.activity-content {
					padding: 24rpx;

					.activity-title {
						font-size: 32rpx;
						font-weight: 700;
						color: #333;
						margin-bottom: 16rpx;
						display: block;
						line-height: 1.4;
					}

					.activity-meta {
						display: flex;
						gap: 24rpx;
						margin-bottom: 16rpx;

						.meta-item {
							display: flex;
							align-items: center;
							gap: 8rpx;

							.meta-text {
								font-size: 24rpx;
								color: #666;
							}
						}
					}

					.organizer-info {
						display: flex;
						align-items: center;
						gap: 12rpx;
						margin-bottom: 20rpx;

						.organizer-avatar {
							width: 40rpx;
							height: 40rpx;
							border-radius: 20rpx;
						}

						.organizer-name {
							font-size: 24rpx;
							color: #666;
						}

						.organizer-badge {
							width: 20rpx;
							height: 20rpx;
							border-radius: 10rpx;
							background: #4ECDC4;
							display: flex;
							align-items: center;
							justify-content: center;
						}
					}

					.activity-footer {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.activity-price {
							font-size: 28rpx;
							font-weight: 700;
							color: #FF6B6B;
						}

						.join-btn {
							padding: 12rpx 32rpx;
							background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
							border-radius: 20rpx;
							font-size: 26rpx;
							color: #fff;
							font-weight: 600;
							transition: all 0.3s ease;

							&:active {
								transform: scale(0.95);
							}
						}
					}
				}
			}
		}
	}
	
	// 城市玩伴页面样式
	.city-companion-page {
		// 城市场景分类
		.scene-categories {
			background: #fff;
			padding: 24rpx;
			border-bottom: 1rpx solid #f0f0f0;

			.categories-grid {
				display: grid;
				grid-template-columns: repeat(3, 1fr);
				gap: 20rpx;

				.scene-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					gap: 12rpx;
					padding: 20rpx;
					border-radius: 16rpx;
					transition: all 0.3s ease;

					&:active {
						transform: scale(0.95);
						background: #f8f9fa;
					}

					.scene-icon {
						width: 60rpx;
						height: 60rpx;
						border-radius: 16rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
					}

					.scene-name {
						font-size: 24rpx;
						color: #333;
						font-weight: 500;
					}
				}
			}
		}

		// 推荐玩伴
		.companion-section {
			.section-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 24rpx;
				background: #fff;
				border-bottom: 1rpx solid #f0f0f0;

				.section-title {
					font-size: 32rpx;
					font-weight: 700;
					color: #333;
				}

				.filter-btn {
					display: flex;
					align-items: center;
					gap: 8rpx;
					padding: 12rpx 20rpx;
					background: #f8f9fa;
					border-radius: 20rpx;
					font-size: 24rpx;
					color: #666;
					transition: all 0.3s ease;

					&:active {
						background: #e9ecef;
					}
				}
			}

			.city-companion-list {
				padding: 20rpx 24rpx;

				.city-companion-card {
					background: #fff;
					border-radius: 20rpx;
					padding: 24rpx;
					margin-bottom: 20rpx;
					box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

					.companion-header {
						display: flex;
						gap: 16rpx;
						margin-bottom: 16rpx;

						.companion-photo {
							width: 80rpx;
							height: 80rpx;
							border-radius: 40rpx;
							border: 3rpx solid #f0f0f0;
						}

						.companion-info {
							flex: 1;

							.name-age {
								display: flex;
								align-items: center;
								gap: 12rpx;
								margin-bottom: 8rpx;

								.companion-name {
									font-size: 30rpx;
									font-weight: 700;
									color: #333;
								}

								.companion-age {
									font-size: 22rpx;
									color: #999;
								}
							}

							.companion-tags {
								margin-bottom: 8rpx;

								.interest-tag {
									display: inline-block;
									background: #f8f9fa;
									color: #666;
									font-size: 20rpx;
									padding: 4rpx 12rpx;
									border-radius: 12rpx;
									margin-right: 8rpx;
								}
							}

							.location-info {
								display: flex;
								align-items: center;
								gap: 6rpx;

								.distance-text {
									font-size: 22rpx;
									color: #999;
								}
							}
						}

						.online-status {
							padding: 8rpx 16rpx;
							border-radius: 16rpx;
							background: #f0f0f0;

							&.online {
								background: rgba(46, 213, 115, 0.1);

								.status-text {
									color: #2ed573;
								}
							}

							.status-text {
								font-size: 22rpx;
								color: #999;
								font-weight: 500;
							}
						}
					}

					.companion-mood {
						font-size: 26rpx;
						color: #666;
						line-height: 1.5;
						margin-bottom: 20rpx;
						display: block;
					}

					.companion-footer {
						.action-btns {
							display: flex;
							align-items: center;
							justify-content: flex-end;
							gap: 16rpx;

							.btn-like {
								width: 48rpx;
								height: 48rpx;
								border-radius: 24rpx;
								background: #f8f9fa;
								display: flex;
								align-items: center;
								justify-content: center;
								transition: all 0.3s ease;

								&:active {
									transform: scale(0.9);
								}
							}

							.btn-chat {
								display: flex;
								align-items: center;
								gap: 8rpx;
								padding: 12rpx 20rpx;
								background: #f8f9fa;
								border-radius: 24rpx;
								font-size: 24rpx;
								color: #666;
								transition: all 0.3s ease;

								&:active {
									background: #e9ecef;
								}
							}

							.btn-invite {
								padding: 12rpx 24rpx;
								background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
								border-radius: 24rpx;
								font-size: 24rpx;
								color: #fff;
								font-weight: 600;
								transition: all 0.3s ease;

								&:active {
									transform: scale(0.95);
								}
							}
						}
					}
				}
			}
		}
	}
	

</style>
