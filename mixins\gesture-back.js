/**
 * 手势返回混入
 * 为页面提供统一的手势返回功能
 */

import screenAdapter from '@/utils/screen-adapter.js'

export default {
	data() {
		return {
			// 手势返回相关
			touchStartX: 0,
			touchStartY: 0,
			showGestureIndicator: false,
			gestureOpacity: 0,
			statusBarHeight: 0
		}
	},

	onLoad() {
		// 获取状态栏高度
		this.statusBarHeight = screenAdapter.statusBarHeight
		
		// 设置CSS变量
		this.setCSSVariables()
	},

	methods: {
		/**
		 * 设置CSS变量
		 */
		setCSSVariables() {
			const pages = getCurrentPages()
			const currentPage = pages[pages.length - 1]
			
			if (currentPage && currentPage.$el) {
				const el = currentPage.$el
				el.style.setProperty('--status-bar-height', screenAdapter.statusBarHeight + 'px')
				el.style.setProperty('--navigation-bar-height', screenAdapter.navigationBarHeight + 'px')
				el.style.setProperty('--bottom-safe-height', screenAdapter.bottomSafeHeight + 'px')
			}
		},

		/**
		 * 智能返回逻辑
		 * 子类可以重写此方法实现自定义返回逻辑
		 */
		goBack() {
			const pages = getCurrentPages()
			if (pages.length > 1) {
				uni.navigateBack()
			} else {
				// 默认返回到首页或我的页面
				uni.switchTab({
					url: '/pages/profile/profile'
				})
			}
		},

		/**
		 * 手势开始
		 */
		onTouchStart(e) {
			if (!e.touches || !e.touches[0]) return
			this.touchStartX = e.touches[0].clientX
			this.touchStartY = e.touches[0].clientY
		},

		/**
		 * 手势移动
		 */
		onTouchMove(e) {
			if (!e.touches || !e.touches[0]) return
			
			const currentX = e.touches[0].clientX
			const currentY = e.touches[0].clientY
			const deltaX = currentX - this.touchStartX
			const deltaY = Math.abs(currentY - this.touchStartY)
			
			// 从左边缘开始的右滑手势
			const edgeThreshold = screenAdapter.rpxToPx(100) // 50rpx转px
			const moveThreshold = screenAdapter.rpxToPx(60)  // 30rpx转px
			const verticalThreshold = screenAdapter.rpxToPx(200) // 100rpx转px
			
			if (this.touchStartX < edgeThreshold && deltaX > moveThreshold && deltaY < verticalThreshold) {
				this.showGestureIndicator = true
				// 根据滑动距离调整透明度
				const maxDistance = screenAdapter.rpxToPx(300) // 150rpx转px
				this.gestureOpacity = Math.min(deltaX / maxDistance, 1)
			} else {
				this.showGestureIndicator = false
				this.gestureOpacity = 0
			}
		},

		/**
		 * 手势结束
		 */
		onTouchEnd(e) {
			if (!e.changedTouches || !e.changedTouches[0]) return
			
			const currentX = e.changedTouches[0].clientX
			const deltaX = currentX - this.touchStartX
			const deltaY = Math.abs(e.changedTouches[0].clientY - this.touchStartY)
			
			// 判断是否触发返回
			const edgeThreshold = screenAdapter.rpxToPx(100)
			const triggerThreshold = screenAdapter.rpxToPx(200) // 100rpx转px
			const verticalThreshold = screenAdapter.rpxToPx(200)
			
			if (this.touchStartX < edgeThreshold && deltaX > triggerThreshold && deltaY < verticalThreshold) {
				this.goBack()
			}
			
			// 重置状态
			this.showGestureIndicator = false
			this.gestureOpacity = 0
		},

		/**
		 * 获取状态栏样式
		 */
		getStatusBarStyle(backgroundColor = 'transparent') {
			return screenAdapter.getStatusBarStyle(backgroundColor)
		},

		/**
		 * 获取页面容器样式
		 */
		getPageContainerStyle(hasStatusBar = true) {
			return screenAdapter.getPageContainerStyle(hasStatusBar)
		},

		/**
		 * 获取安全区域样式
		 */
		getSafeAreaStyle() {
			return screenAdapter.getSafeAreaStyle()
		},

		/**
		 * 获取响应式字体大小
		 */
		getResponsiveFontSize(baseFontSize) {
			return screenAdapter.getResponsiveFontSize(baseFontSize) + 'rpx'
		},

		/**
		 * 获取响应式间距
		 */
		getResponsiveSpacing(baseSpacing) {
			return screenAdapter.getResponsiveSpacing(baseSpacing) + 'rpx'
		}
	}
}
